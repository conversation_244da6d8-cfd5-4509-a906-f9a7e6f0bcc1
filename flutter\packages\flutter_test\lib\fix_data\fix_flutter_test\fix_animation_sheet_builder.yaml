# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the
# flutter/packages/flutter_test/test_fixes/README.md file for instructions
# on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for AnimationSheetBuilder from the flutter_test/animation_sheet.dart file. *

version: 1
transforms:
  # Changes made in https://github.com/flutter/flutter/pull/83337
  # The related deprecation for `sheetSize` doesn't have a fix because there
  # isn't an alternative API, it's being removed completely.
  - title: 'Migrate to collate'
    date: 2023-03-29
    element:
      uris: [ 'flutter_test.dart' ]
      method: 'display'
      inClass: 'AnimationSheetBuilder'
    changes:
      - kind: 'rename'
        newName: 'collate'
      - kind: 'removeParameter'
        name: 'key'
      - kind: 'addParameter'
        index: 0
        name: 'cellsPerRow'
        style: 'required_positional'
        argumentValue:
          expression: '1'
