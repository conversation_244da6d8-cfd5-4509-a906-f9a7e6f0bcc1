{"clearButtonTooltip": "Efface<PERSON> le texte", "selectedDateLabel": "Sélectionnée", "lookUpButtonLabel": "Regarder en haut", "searchWebButtonLabel": "Rechercher sur le Web", "shareButtonLabel": "Partager", "scanTextButtonLabel": "Balayer un texte", "menuDismissLabel": "Ignorer le menu", "expansionTileExpandedHint": "toucher deux fois pour réduire", "expansionTileCollapsedHint": "toucher deux fois pour développer", "expansionTileExpandedTapHint": "<PERSON><PERSON><PERSON><PERSON>", "expansionTileCollapsedTapHint": "Développer le panneau pour plus de détails", "expandedHint": "Réduit", "collapsedHint": "Développé", "scrimLabel": "Grille", "bottomSheetLabel": "Zone de contenu dans le bas de l'écran", "scrimOnTapHint": "Fermer $modalRouteContentName", "currentDateLabel": "<PERSON><PERSON><PERSON>'hui", "keyboardKeyShift": "Maj", "menuBarMenuLabel": "Menu de la barre de menu", "keyboardKeyNumpad8": "Num 8", "keyboardKeyChannelDown": "<PERSON><PERSON><PERSON>.", "keyboardKeyCapsLock": "Verr. maj.", "keyboardKeyMetaMacOs": "Commande", "keyboardKeyMetaWindows": "Win", "keyboardKeyNumLock": "Verr. num.", "keyboardKeyNumpad1": "Num 1", "keyboardKeyNumpad2": "Num 2", "keyboardKeyNumpad3": "Num 3", "keyboardKeyNumpad4": "Num 4", "keyboardKeyNumpad5": "Num 5", "keyboardKeyNumpad6": "Num 6", "keyboardKeyNumpad7": "Num 7", "keyboardKeyNumpad9": "Num 9", "keyboardKeyNumpadAdd": "Num +", "keyboardKeyNumpadComma": "<PERSON><PERSON> ,", "keyboardKeyPrintScreen": "Impression de l'écran", "keyboardKeyNumpadDecimal": "Num .", "keyboardKeyNumpadDivide": "Num /", "keyboardKeyNumpadEnter": "Num Entrée", "keyboardKeyNumpadEqual": "Num =", "keyboardKeyEnd": "Fin", "keyboardKeyInsert": "Insér.", "keyboardKeyHome": "Accueil", "keyboardKeyNumpadMultiply": "Num *", "keyboardKeyFn": "Fn", "keyboardKeyEscape": "Échap", "keyboardKeyNumpadParenLeft": "Num (", "keyboardKeyEject": "Éject.", "keyboardKeyDelete": "Suppr", "keyboardKeyControl": "Ctrl", "keyboardKeyChannelUp": "Chaîne suiv.", "keyboardKeyPower": "Alimentation", "keyboardKeyBackspace": "Retour arrière", "keyboardKeyAltGraph": "AltGr", "keyboardKeyAlt": "Alt", "keyboardKeyNumpadParenRight": "Num )", "keyboardKeySpace": "Espace", "keyboardKeySelect": "Sélect.", "keyboardKeyScrollLock": "<PERSON><PERSON><PERSON><PERSON>", "keyboardKeyNumpadSubtract": "Num -", "keyboardKeyPowerOff": "<PERSON><PERSON><PERSON>", "keyboardKeyMeta": "<PERSON><PERSON><PERSON>", "keyboardKeyPageUp": "Bas page", "keyboardKeyPageDown": "Haut page", "keyboardKeyNumpad0": "Num 0", "invalidTimeLabel": "Entrez une heure valide", "licensesPackageDetailTextOne": "1 licence", "timePickerDialHelpText": "Sélectionner l'heure", "timePickerInputHelpText": "Entrer l'heure", "timePickerHourLabel": "<PERSON><PERSON>", "timePickerMinuteLabel": "Minutes", "licensesPackageDetailTextOther": "$licenseCount licences", "dialModeButtonLabel": "Passer au mode de sélection du cadran", "inputTimeModeButtonLabel": "Passer au mode d'entrée Texte", "dateSeparator": "/", "dateRangeStartLabel": "Date de début", "calendarModeButtonLabel": "Passer à l'agenda", "dateRangePickerHelpText": "Sélectionner la plage", "datePickerHelpText": "Sélectionner la date", "saveButtonLabel": "Enregistrer", "dateOutOfRangeLabel": "<PERSON><PERSON>.", "invalidDateRangeLabel": "<PERSON><PERSON>.", "invalidDateFormatLabel": "Format incorrect", "dateRangeEndDateSemanticLabel": "Date de fin : $fullDate", "dateRangeStartDateSemanticLabel": "Date de début : $fullDate", "dateRangeEndLabel": "Date de fin", "inputDateModeButtonLabel": "Passer à l'entrée", "dateInputLabel": "Entrer une date", "unspecifiedDateRange": "Période", "unspecifiedDate": "Date", "selectYearSemanticsLabel": "Sélectionner une année", "dateHelpText": "jj-mm-aaaa", "moreButtonTooltip": "Plus", "selectedRowCountTitleOne": "1 élément sélectionné", "remainingTextFieldCharacterCountOther": "$remainingCount caractères restants", "openAppDrawerTooltip": "<PERSON><PERSON><PERSON><PERSON>r le menu de navigation", "backButtonTooltip": "Retour", "closeButtonTooltip": "<PERSON><PERSON><PERSON>", "deleteButtonTooltip": "<PERSON><PERSON><PERSON><PERSON>", "nextMonthTooltip": "<PERSON><PERSON> suivant", "previousMonthTooltip": "<PERSON><PERSON>", "nextPageTooltip": "<PERSON> suivante", "previousPageTooltip": "<PERSON> p<PERSON>", "firstPageTooltip": "Première page", "lastPageTooltip": "Dernière page", "showMenuTooltip": "<PERSON><PERSON><PERSON><PERSON> le menu", "aboutListTileTitle": "À propos de $applicationName", "licensesPageTitle": "Licences", "pageRowsInfoTitle": "$firstRow à $lastRow sur $rowCount", "pageRowsInfoTitleApproximate": "$firstRow à $lastRow sur environ $rowCount", "rowsPerPageTitle": "<PERSON><PERSON><PERSON> par page :", "tabLabel": "Onglet $tabIndex sur $tabCount", "remainingTextFieldCharacterCountOne": "1 caractère restant", "selectedRowCountTitleOther": "$selectedRowCount éléments sélectionnés", "cancelButtonLabel": "Annuler", "closeButtonLabel": "<PERSON><PERSON><PERSON>", "continueButtonLabel": "<PERSON><PERSON><PERSON>", "copyButtonLabel": "<PERSON><PERSON><PERSON>", "cutButtonLabel": "Couper", "okButtonLabel": "OK", "pasteButtonLabel": "<PERSON><PERSON>", "selectAllButtonLabel": "<PERSON><PERSON>", "viewLicensesButtonLabel": "Afficher les licences", "anteMeridiemAbbreviation": "am", "postMeridiemAbbreviation": "pm", "timePickerHourModeAnnouncement": "Sélectionnez les heures", "timePickerMinuteModeAnnouncement": "Sélectionnez les minutes", "modalBarrierDismissLabel": "<PERSON><PERSON><PERSON>", "refreshIndicatorSemanticLabel": "Actualiser", "hideAccountsLabel": "Masquer les comptes", "showAccountsLabel": "Affiche<PERSON> les comptes", "drawerLabel": "Menu de navigation", "popupMenuLabel": "Menu contextuel", "dialogLabel": "Boîte de dialogue", "alertDialogLabel": "<PERSON><PERSON><PERSON>", "searchFieldLabel": "<PERSON><PERSON><PERSON>", "reorderItemToStart": "<PERSON><PERSON><PERSON><PERSON> au début", "reorderItemToEnd": "Déplacer à la fin", "reorderItemUp": "<PERSON><PERSON><PERSON><PERSON> vers le haut", "reorderItemDown": "<PERSON><PERSON><PERSON><PERSON> vers le bas", "reorderItemLeft": "<PERSON><PERSON><PERSON>r vers la gauche", "reorderItemRight": "<PERSON><PERSON><PERSON>r vers la droite", "expandedIconTapHint": "<PERSON><PERSON><PERSON><PERSON>", "collapsedIconTapHint": "Développer", "signedInLabel": "Connecté", "scriptCategory": "English-like", "timeOfDayFormat": "HH 'h' mm"}