// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_IMPELLER_FIXTURES_GOLDEN_PATHS_H_
#define FLUTTER_IMPELLER_FIXTURES_GOLDEN_PATHS_H_

#include <vector>

#include "impeller/geometry/path_component.h"
#include "impeller/geometry/point.h"

namespace impeller {
namespace testing {

std::vector<Point> golden_cubic_and_quad_points = {
    {139.982, 19.5003}, {140.018, 20.4997}, {131.747, 19.8026},
    {131.783, 20.802},  {131.715, 19.8048}, {131.815, 20.7998},
    {123.622, 20.6102}, {123.721, 21.6053}, {123.59, 20.6145},
    {123.753, 21.601},  {115.652, 21.9306}, {115.816, 22.9171},
    {115.619, 21.9372}, {115.848, 22.9105}, {107.85, 23.7687},
    {108.08, 24.742},   {107.817, 23.7777}, {108.113, 24.733},
    {100.23, 26.1264},  {100.526, 27.0817}, {100.197, 26.1378},
    {100.558, 27.0703}, {92.8037, 29.0025}, {93.165, 29.935},
    {92.7736, 29.0154}, {93.1952, 29.9221}, {85.708, 32.3003},
    {86.1296, 33.2071}, {85.68, 32.3144},   {86.1576, 33.193},
    {78.8865, 36.008},  {79.3641, 36.8865}, {78.8588, 36.0242},
    {79.3918, 36.8703}, {72.3485, 40.125},  {72.8815, 40.9711},
    {72.3216, 40.1432}, {72.9084, 40.9529}, {66.1045, 44.6479},
    {66.6913, 45.4577}, {66.0788, 44.6679}, {66.717, 45.4378},
    {60.1632, 49.5711}, {60.8014, 50.341},  {60.139, 49.5924},
    {60.8255, 50.3196}, {54.5317, 54.8864}, {55.2183, 55.6136},
    {54.5094, 54.909},  {55.2406, 55.591},  {49.254, 60.5436},
    {49.9853, 61.2257}, {49.2329, 60.5677}, {50.0063, 61.2016},
    {44.3684, 66.5025}, {45.1418, 67.1364}, {44.3488, 66.5281},
    {45.1614, 67.1108}, {39.8827, 72.7564}, {40.6954, 73.3391},
    {39.8648, 72.7832}, {40.7133, 73.3123}, {35.8029, 79.297},
    {36.6515, 79.8261}, {35.7869, 79.3246}, {36.6675, 79.7985},
    {32.1328, 86.1144}, {33.0134, 86.5883}, {32.1188, 86.1424},
    {33.0273, 86.5603}, {28.8739, 93.1973}, {29.7824, 93.6152},
    {28.8613, 93.2272}, {29.795, 93.5853},  {26.0399, 100.583},
    {26.9736, 100.941}, {26.0288, 100.615}, {26.9847, 100.908},
    {23.7144, 108.15},  {24.6704, 108.444}, {23.7056, 108.183},
    {24.6792, 108.411}, {21.9002, 115.886}, {22.8738, 116.115},
    {21.8937, 115.919}, {22.8804, 116.082}, {20.5962, 123.779},
    {21.5828, 123.942}, {20.592, 123.812},  {21.5871, 123.91},
    {19.7985, 131.816}, {20.7936, 131.914}, {19.7964, 131.847},
    {20.7957, 131.883}, {19.5003, 139.982}, {20.4997, 140.018},
    {20.2883, 140.409}, {19.7117, 139.591}, {29.9421, 133.595},
    {29.3655, 132.778}, {29.9544, 133.586}, {29.3532, 132.787},
    {39.1905, 126.639}, {38.5894, 125.839}, {39.2039, 126.628},
    {38.576, 125.85},   {47.9822, 119.545}, {47.3542, 118.767},
    {47.9968, 119.533}, {47.3396, 118.779}, {56.2739, 112.316},
    {55.6167, 111.562}, {56.2898, 112.302}, {55.6008, 111.577},
    {64.0197, 104.952}, {63.3307, 104.228}, {64.0369, 104.935},
    {63.3134, 104.245}, {71.1714, 97.4578}, {70.4479, 96.7675},
    {71.1899, 97.4373}, {70.4294, 96.7879}, {77.6791, 89.8381},
    {76.9187, 89.1887}, {77.6987, 89.8137}, {76.8991, 89.2131},
    {83.4923, 82.1009}, {82.6928, 81.5003}, {83.5125, 82.0718},
    {82.6725, 81.5293}, {88.5609, 74.2563}, {87.7209, 73.7138},
    {88.5812, 74.2221}, {87.7007, 73.748},  {92.8369, 66.3173},
    {91.9564, 65.8432}, {92.8562, 66.2772}, {91.9371, 65.8833},
    {96.2756, 58.299},  {95.3564, 57.9051}, {96.2927, 58.253},
    {95.3393, 57.951},  {98.8374, 50.2196}, {97.8841, 49.9176},
    {98.8508, 50.1681}, {97.8708, 49.969},  {100.49, 42.0995},
    {99.51, 41.9005},   {100.49, 42.0995},  {99.51, 41.9005},
};

}  // namespace testing
}  // namespace impeller

#endif  // FLUTTER_IMPELLER_FIXTURES_GOLDEN_PATHS_H_
