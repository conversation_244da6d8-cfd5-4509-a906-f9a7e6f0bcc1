// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "flutter/fml/hash_combine.h"

#include "flutter/testing/testing.h"

namespace fml {
namespace testing {

TEST(HashCombineTest, CanHash) {
  ASSERT_EQ(Hash<PERSON>ombine(), Hash<PERSON>ombine());
  ASSERT_EQ(Has<PERSON><PERSON><PERSON><PERSON>("Hello"), <PERSON><PERSON><PERSON><PERSON><PERSON>("Hello"));
  ASSERT_NE(Hash<PERSON>ombine("Hello"), <PERSON><PERSON><PERSON><PERSON><PERSON>("World"));
  ASSERT_EQ(HashCombine("Hello", "World"), <PERSON><PERSON><PERSON><PERSON><PERSON>("Hello", "World"));
  ASSERT_NE(<PERSON>h<PERSON><PERSON>ine("World", "Hello"), <PERSON><PERSON><PERSON><PERSON><PERSON>("Hello", "World"));
  ASSERT_EQ(Hash<PERSON>ombine(12u), <PERSON>h<PERSON><PERSON><PERSON>(12u));
  ASSERT_NE(HashCombine(12u), <PERSON>h<PERSON>omb<PERSON>(12.0f));
  ASSERT_EQ(Hash<PERSON><PERSON><PERSON>('a'), <PERSON><PERSON><PERSON><PERSON><PERSON>('a'));
}

}  // namespace testing
}  // namespace fml
