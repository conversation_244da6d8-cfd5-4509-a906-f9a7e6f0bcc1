Signature: f4f0a7ce457a816a7b8e0f13355125f4

====================================================================================================
LIBRARY: dart
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/pprof/profile.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/common/builtin_clock.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/clock_snapshot.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/interned_data/interned_data.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/profiling/profile_common.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/profiling/profile_packet.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/trace.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/trace_packet.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/debug_annotation.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/process_descriptor.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/thread_descriptor.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/track_descriptor.proto
ORIGIN: http://www.apache.org/licenses/LICENSE-2.0 referenced by ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/track_event.proto
TYPE: LicenseType.apache
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/pprof/profile.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/common/builtin_clock.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/clock_snapshot.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/interned_data/interned_data.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/profiling/profile_common.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/profiling/profile_packet.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/trace.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/trace_packet.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/debug_annotation.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/process_descriptor.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/thread_descriptor.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/track_descriptor.proto
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/track_event.proto
----------------------------------------------------------------------------------------------------
Apache License
Version 2.0, January 2004
http://www.apache.org/licenses/

TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

1. Definitions.

   "License" shall mean the terms and conditions for use, reproduction,
   and distribution as defined by Sections 1 through 9 of this document.

   "Licensor" shall mean the copyright owner or entity authorized by
   the copyright owner that is granting the License.

   "Legal Entity" shall mean the union of the acting entity and all
   other entities that control, are controlled by, or are under common
   control with that entity. For the purposes of this definition,
   "control" means (i) the power, direct or indirect, to cause the
   direction or management of such entity, whether by contract or
   otherwise, or (ii) ownership of fifty percent (50%) or more of the
   outstanding shares, or (iii) beneficial ownership of such entity.

   "You" (or "Your") shall mean an individual or Legal Entity
   exercising permissions granted by this License.

   "Source" form shall mean the preferred form for making modifications,
   including but not limited to software source code, documentation
   source, and configuration files.

   "Object" form shall mean any form resulting from mechanical
   transformation or translation of a Source form, including but
   not limited to compiled object code, generated documentation,
   and conversions to other media types.

   "Work" shall mean the work of authorship, whether in Source or
   Object form, made available under the License, as indicated by a
   copyright notice that is included in or attached to the work
   (an example is provided in the Appendix below).

   "Derivative Works" shall mean any work, whether in Source or Object
   form, that is based on (or derived from) the Work and for which the
   editorial revisions, annotations, elaborations, or other modifications
   represent, as a whole, an original work of authorship. For the purposes
   of this License, Derivative Works shall not include works that remain
   separable from, or merely link (or bind by name) to the interfaces of,
   the Work and Derivative Works thereof.

   "Contribution" shall mean any work of authorship, including
   the original version of the Work and any modifications or additions
   to that Work or Derivative Works thereof, that is intentionally
   submitted to Licensor for inclusion in the Work by the copyright owner
   or by an individual or Legal Entity authorized to submit on behalf of
   the copyright owner. For the purposes of this definition, "submitted"
   means any form of electronic, verbal, or written communication sent
   to the Licensor or its representatives, including but not limited to
   communication on electronic mailing lists, source code control systems,
   and issue tracking systems that are managed by, or on behalf of, the
   Licensor for the purpose of discussing and improving the Work, but
   excluding communication that is conspicuously marked or otherwise
   designated in writing by the copyright owner as "Not a Contribution."

   "Contributor" shall mean Licensor and any individual or Legal Entity
   on behalf of whom a Contribution has been received by Licensor and
   subsequently incorporated within the Work.

2. Grant of Copyright License. Subject to the terms and conditions of
   this License, each Contributor hereby grants to You a perpetual,
   worldwide, non-exclusive, no-charge, royalty-free, irrevocable
   copyright license to reproduce, prepare Derivative Works of,
   publicly display, publicly perform, sublicense, and distribute the
   Work and such Derivative Works in Source or Object form.

3. Grant of Patent License. Subject to the terms and conditions of
   this License, each Contributor hereby grants to You a perpetual,
   worldwide, non-exclusive, no-charge, royalty-free, irrevocable
   (except as stated in this section) patent license to make, have made,
   use, offer to sell, sell, import, and otherwise transfer the Work,
   where such license applies only to those patent claims licensable
   by such Contributor that are necessarily infringed by their
   Contribution(s) alone or by combination of their Contribution(s)
   with the Work to which such Contribution(s) was submitted. If You
   institute patent litigation against any entity (including a
   cross-claim or counterclaim in a lawsuit) alleging that the Work
   or a Contribution incorporated within the Work constitutes direct
   or contributory patent infringement, then any patent licenses
   granted to You under this License for that Work shall terminate
   as of the date such litigation is filed.

4. Redistribution. You may reproduce and distribute copies of the
   Work or Derivative Works thereof in any medium, with or without
   modifications, and in Source or Object form, provided that You
   meet the following conditions:

   (a) You must give any other recipients of the Work or
       Derivative Works a copy of this License; and

   (b) You must cause any modified files to carry prominent notices
       stating that You changed the files; and

   (c) You must retain, in the Source form of any Derivative Works
       that You distribute, all copyright, patent, trademark, and
       attribution notices from the Source form of the Work,
       excluding those notices that do not pertain to any part of
       the Derivative Works; and

   (d) If the Work includes a "NOTICE" text file as part of its
       distribution, then any Derivative Works that You distribute must
       include a readable copy of the attribution notices contained
       within such NOTICE file, excluding those notices that do not
       pertain to any part of the Derivative Works, in at least one
       of the following places: within a NOTICE text file distributed
       as part of the Derivative Works; within the Source form or
       documentation, if provided along with the Derivative Works; or,
       within a display generated by the Derivative Works, if and
       wherever such third-party notices normally appear. The contents
       of the NOTICE file are for informational purposes only and
       do not modify the License. You may add Your own attribution
       notices within Derivative Works that You distribute, alongside
       or as an addendum to the NOTICE text from the Work, provided
       that such additional attribution notices cannot be construed
       as modifying the License.

   You may add Your own copyright statement to Your modifications and
   may provide additional or different license terms and conditions
   for use, reproduction, or distribution of Your modifications, or
   for any such Derivative Works as a whole, provided Your use,
   reproduction, and distribution of the Work otherwise complies with
   the conditions stated in this License.

5. Submission of Contributions. Unless You explicitly state otherwise,
   any Contribution intentionally submitted for inclusion in the Work
   by You to the Licensor shall be under the terms and conditions of
   this License, without any additional terms or conditions.
   Notwithstanding the above, nothing herein shall supersede or modify
   the terms of any separate license agreement you may have executed
   with Licensor regarding such Contributions.

6. Trademarks. This License does not grant permission to use the trade
   names, trademarks, service marks, or product names of the Licensor,
   except as required for reasonable and customary use in describing the
   origin of the Work and reproducing the content of the NOTICE file.

7. Disclaimer of Warranty. Unless required by applicable law or
   agreed to in writing, Licensor provides the Work (and each
   Contributor provides its Contributions) on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
   implied, including, without limitation, any warranties or conditions
   of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
   PARTICULAR PURPOSE. You are solely responsible for determining the
   appropriateness of using or redistributing the Work and assume any
   risks associated with Your exercise of permissions under this License.

8. Limitation of Liability. In no event and under no legal theory,
   whether in tort (including negligence), contract, or otherwise,
   unless required by applicable law (such as deliberate and grossly
   negligent acts) or agreed to in writing, shall any Contributor be
   liable to You for damages, including any direct, indirect, special,
   incidental, or consequential damages of any character arising as a
   result of this License or out of the use or inability to use the
   Work (including but not limited to damages for loss of goodwill,
   work stoppage, computer failure or malfunction, or any and all
   other commercial damages or losses), even if such Contributor
   has been advised of the possibility of such damages.

9. Accepting Warranty or Additional Liability. While redistributing
   the Work or Derivative Works thereof, You may choose to offer,
   and charge a fee for, acceptance of support, warranty, indemnity,
   or other liability obligations and/or rights consistent with this
   License. However, in accepting such obligations, You may act only
   on Your own behalf and on Your sole responsibility, not on behalf
   of any other Contributor, and only if You agree to indemnify,
   defend, and hold each Contributor harmless for any liability
   incurred by, or claims asserted against, such Contributor by reason
   of your accepting any such warranty or additional liability.

END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

   To apply the Apache License to your work, attach the following
   boilerplate notice, with the fields enclosed by brackets "[]"
   replaced with your own identifying information. (Don't include
   the brackets!)  The text should be enclosed in the appropriate
   comment syntax for the file format. We also recommend that a
   file or class name and description of purpose be included on the
   same "printed page" as the copyright notice for easier
   identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/bigint_patch.dart
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/bigint_patch.dart
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/bigint_patch.dart
TYPE: LicenseType.mit
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/bigint_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/bigint_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/bigint_patch.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2003-2005  Tom Wu
Copyright (c) 2012 Adam Singer (<EMAIL>)
All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND,
EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY
WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.

IN NO EVENT SHALL TOM WU BE LIABLE FOR ANY SPECIAL, INCIDENTAL,
INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND, OR ANY DAMAGES WHATSOEVER
RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER OR NOT ADVISED OF
THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF LIABILITY, ARISING OUT
OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

In addition, the following condition applies:

All redistributions must retain an intact copy of this copyright notice
and disclaimer.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/splay-tree-inl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/splay-tree.h + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/platform/splay-tree-inl.h
FILE: ../../../flutter/third_party/dart/runtime/platform/splay-tree.h
----------------------------------------------------------------------------------------------------
Copyright (c) 2010, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/snapshot_empty.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/snapshot_in.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/dart_tools_api.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/double.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/math.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/mirrors.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/object.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/string.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bitfield.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dart.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dart_api_impl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dart_entry.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dart_entry.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/debugger_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/debugger_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/debugger_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/double_conversion.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/double_conversion.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/exceptions.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/exceptions.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/handle_visitor.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/handles.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/freelist.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/marker.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/marker.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/pages.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/scavenger.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/sweeper.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/sweeper.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/verifier.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/longjump.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/memory_region.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/message.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/message.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/message_handler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/message_handler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_entry.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_entry.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_function.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/port.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/resolver.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/resolver.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/runtime_entry.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/runtime_entry.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_list.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_frame.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stub_code.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/timer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/timer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/token.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/unicode_data.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/visitor.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/queue.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/comparable.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/date_time.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/duration.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/function.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/iterable.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/pattern.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/set.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/stopwatch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/string_buffer.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/html/html_common/device.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/html/html_common/filtered_element_list.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/html/html_common/lists.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/iterable.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/sort.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/snapshot_empty.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/snapshot_in.cc
FILE: ../../../flutter/third_party/dart/runtime/include/dart_tools_api.h
FILE: ../../../flutter/third_party/dart/runtime/lib/double.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/math.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/mirrors.h
FILE: ../../../flutter/third_party/dart/runtime/lib/object.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/string.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/bitfield.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/dart.h
FILE: ../../../flutter/third_party/dart/runtime/vm/dart_api_impl.h
FILE: ../../../flutter/third_party/dart/runtime/vm/dart_entry.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/dart_entry.h
FILE: ../../../flutter/third_party/dart/runtime/vm/debugger_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/debugger_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/debugger_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/double_conversion.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/double_conversion.h
FILE: ../../../flutter/third_party/dart/runtime/vm/exceptions.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/exceptions.h
FILE: ../../../flutter/third_party/dart/runtime/vm/handle_visitor.h
FILE: ../../../flutter/third_party/dart/runtime/vm/handles.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/freelist.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/marker.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/marker.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/pages.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/scavenger.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/sweeper.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/sweeper.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/verifier.h
FILE: ../../../flutter/third_party/dart/runtime/vm/longjump.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/memory_region.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/message.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/message.h
FILE: ../../../flutter/third_party/dart/runtime/vm/message_handler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/message_handler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/native_entry.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/native_entry.h
FILE: ../../../flutter/third_party/dart/runtime/vm/native_function.h
FILE: ../../../flutter/third_party/dart/runtime/vm/os.h
FILE: ../../../flutter/third_party/dart/runtime/vm/port.h
FILE: ../../../flutter/third_party/dart/runtime/vm/resolver.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/resolver.h
FILE: ../../../flutter/third_party/dart/runtime/vm/runtime_entry.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/runtime_entry.h
FILE: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_list.h
FILE: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_frame.h
FILE: ../../../flutter/third_party/dart/runtime/vm/stub_code.h
FILE: ../../../flutter/third_party/dart/runtime/vm/timer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/timer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/token.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/unicode_data.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/visitor.h
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/queue.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/comparable.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/date_time.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/duration.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/function.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/iterable.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/map.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/pattern.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/set.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/stopwatch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/string_buffer.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/html_common/device.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/html_common/filtered_element_list.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/html_common/lists.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/iterable.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/sort.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2011, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/builtin.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/builtin.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/builtin_gen_snapshot.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/builtin_in.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/builtin_natives.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/crashpad.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/crypto.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/crypto.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/crypto_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/crypto_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/crypto_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dartutils.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dartutils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/directory.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/directory.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/directory_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/directory_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/directory_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler_linux.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler_macos.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler_win.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/fdutils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/fdutils_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/fdutils_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/fdutils_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/io_buffer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/io_buffer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/io_natives.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/isolate_data.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/lockers.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/main.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/main_impl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/platform.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/platform.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/platform_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/platform_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/platform_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/process.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/process_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/process_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/process_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/run_vm_tests.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/secure_socket_unsupported.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base_linux.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base_macos.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base_win.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/thread.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/thread_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/thread_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/thread_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/utils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/utils_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/utils_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/utils_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/utils_win.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/dart_api.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/array.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/bool.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/date.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/errors.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/function.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/growable_array.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/identical.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/integers.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/isolate.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/mirrors.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/regexp.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/stopwatch.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/assert.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/assert.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/floating_point.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/floating_point_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/floating_point_win.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/globals.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/hashmap.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/hashmap.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/syslog.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/syslog_android.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/syslog_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/syslog_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/syslog_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/text_buffer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/text_buffer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/unicode.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/unicode.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_android.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_android.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_linux.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_macos.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_win.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/allocation.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bit_set.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bit_vector.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bit_vector.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bitmap.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bitmap.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bootstrap.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bootstrap.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bootstrap_natives.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bootstrap_natives.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/class_finalizer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/class_table.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/class_table.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_descriptors.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_descriptors.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_observers.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_observers.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_patcher.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_patcher.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_patcher_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_patcher_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/aot_call_specializer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_base.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_x86.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/object_pool_builder.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/constant_propagator.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_printer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_printer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/inliner.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/cha.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/cha.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/flow_graph_builder.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/flow_graph_builder.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/intrinsifier.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/intrinsifier.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/jit/compiler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/jit/compiler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dart_api_message.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dart_api_state.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/datastream.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/debugger.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/debugger.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/double_internals.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/flag_list.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/flags.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/flags.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/globals.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/growable_array.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/handles.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/handles_impl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/hash_map.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/freelist.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/heap.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/heap.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/pages.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/pointer_block.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/pointer_block.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/scavenger.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/verifier.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_ia32.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_x64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/lockers.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/megamorphic_cache_table.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/megamorphic_cache_table.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/memory_region.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_arguments.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_message_handler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_message_handler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_set.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_store.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_android.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_android.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_android.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_linux.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_macos.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_win.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/parser.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/parser.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/port.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/proccpuinfo.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/proccpuinfo.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/raw_object.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/raw_object.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/scopes.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/scopes.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/snapshot.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/snapshot.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_frame.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stub_code.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/symbols.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/symbols.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_pool.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_pool.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/token.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/unicode.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/version.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/version_in.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/virtual_memory.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/virtual_memory.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_posix.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/zone.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/zone.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/crypto.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/http_date.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/async_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/bigint_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/core_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/isolate_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/math_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/foreign_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/interceptors.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/isolate_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_array.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_number.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_string.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/native_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/regexp_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/string_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/async_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/bigint_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/constant_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/core_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/foreign_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/interceptors.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/isolate_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_array.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_number.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_string.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/math_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/native_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/regexp_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/string_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/sdk_library_metadata/lib/libraries.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/builtin.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/common_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/directory_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/eventhandler_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/file_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/platform_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/secure_socket_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/array.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/array_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/double.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/double_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/empty_source.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/errors_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/expando_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/function_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/growable_array.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/identical_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/immutable_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/integers.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/invocation_mirror_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/isolate_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/math_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/mirrors_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/mirrors_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/object_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/print_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/regexp_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/stopwatch_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/string_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/timer_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/type_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/weak_property.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/bool_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/date_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/integers_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/map_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/string_buffer_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/array_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/async.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/async_error.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/broadcast_stream_controller.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/future.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/future_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/stream_controller.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/stream_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/stream_pipe.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/timer.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/collection.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/iterable.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/iterator.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/maps.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/splay_tree.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/bool.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/core.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/double.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/errors.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/exceptions.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/identical.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/int.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/invocation.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/iterator.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/list.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/num.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/object.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/print.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/regexp.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/string.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/type.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/uri.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/weak.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/html/dartium/nativewrappers.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/html/html_common/conversions.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/html/html_common/css_class_set.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/html/html_common/html_common_dart2js.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/html/html_common/metadata.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/indexed_db/dart2js/indexed_db_dart2js.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/async_cast.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/cast.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/internal.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/common.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/directory.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/directory_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/eventhandler.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/io.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/platform.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/platform_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/secure_server_socket.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/isolate/isolate.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/math/math.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/math/random.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/web_audio/dart2js/web_audio_dart2js.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/web_sql/dart2js/web_sql_dart2js.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/builtin.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/builtin.h
FILE: ../../../flutter/third_party/dart/runtime/bin/builtin_gen_snapshot.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/builtin_in.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/builtin_natives.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/crashpad.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/crypto.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/crypto.h
FILE: ../../../flutter/third_party/dart/runtime/bin/crypto_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/crypto_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/crypto_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/dartutils.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/dartutils.h
FILE: ../../../flutter/third_party/dart/runtime/bin/directory.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/directory.h
FILE: ../../../flutter/third_party/dart/runtime/bin/directory_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/directory_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/directory_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler.h
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler_linux.h
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler_macos.h
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler_win.h
FILE: ../../../flutter/third_party/dart/runtime/bin/fdutils.h
FILE: ../../../flutter/third_party/dart/runtime/bin/fdutils_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/fdutils_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/fdutils_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/io_buffer.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/io_buffer.h
FILE: ../../../flutter/third_party/dart/runtime/bin/io_natives.h
FILE: ../../../flutter/third_party/dart/runtime/bin/isolate_data.h
FILE: ../../../flutter/third_party/dart/runtime/bin/lockers.h
FILE: ../../../flutter/third_party/dart/runtime/bin/main.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/main_impl.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/platform.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/platform.h
FILE: ../../../flutter/third_party/dart/runtime/bin/platform_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/platform_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/platform_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/process.h
FILE: ../../../flutter/third_party/dart/runtime/bin/process_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/process_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/process_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/run_vm_tests.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/secure_socket_unsupported.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base_linux.h
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base_macos.h
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base_win.h
FILE: ../../../flutter/third_party/dart/runtime/bin/thread.h
FILE: ../../../flutter/third_party/dart/runtime/bin/thread_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/thread_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/thread_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/utils.h
FILE: ../../../flutter/third_party/dart/runtime/bin/utils_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/utils_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/utils_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/utils_win.h
FILE: ../../../flutter/third_party/dart/runtime/include/dart_api.h
FILE: ../../../flutter/third_party/dart/runtime/lib/array.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/bool.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/date.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/errors.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/function.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/growable_array.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/identical.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/integers.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/isolate.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/mirrors.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/regexp.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/stopwatch.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/assert.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/assert.h
FILE: ../../../flutter/third_party/dart/runtime/platform/floating_point.h
FILE: ../../../flutter/third_party/dart/runtime/platform/floating_point_win.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/floating_point_win.h
FILE: ../../../flutter/third_party/dart/runtime/platform/globals.h
FILE: ../../../flutter/third_party/dart/runtime/platform/hashmap.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/hashmap.h
FILE: ../../../flutter/third_party/dart/runtime/platform/syslog.h
FILE: ../../../flutter/third_party/dart/runtime/platform/syslog_android.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/syslog_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/syslog_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/syslog_win.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/text_buffer.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/text_buffer.h
FILE: ../../../flutter/third_party/dart/runtime/platform/unicode.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/unicode.h
FILE: ../../../flutter/third_party/dart/runtime/platform/utils.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/utils.h
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_android.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_android.h
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_linux.h
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_macos.h
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_win.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_win.h
FILE: ../../../flutter/third_party/dart/runtime/vm/allocation.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/bit_set.h
FILE: ../../../flutter/third_party/dart/runtime/vm/bit_vector.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/bit_vector.h
FILE: ../../../flutter/third_party/dart/runtime/vm/bitmap.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/bitmap.h
FILE: ../../../flutter/third_party/dart/runtime/vm/bootstrap.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/bootstrap.h
FILE: ../../../flutter/third_party/dart/runtime/vm/bootstrap_natives.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/bootstrap_natives.h
FILE: ../../../flutter/third_party/dart/runtime/vm/class_finalizer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/class_table.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/class_table.h
FILE: ../../../flutter/third_party/dart/runtime/vm/code_descriptors.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/code_descriptors.h
FILE: ../../../flutter/third_party/dart/runtime/vm/code_observers.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/code_observers.h
FILE: ../../../flutter/third_party/dart/runtime/vm/code_patcher.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/code_patcher.h
FILE: ../../../flutter/third_party/dart/runtime/vm/code_patcher_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/code_patcher_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/aot_call_specializer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_base.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_x86.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/object_pool_builder.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/constant_propagator.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_printer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_printer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/inliner.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/cha.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/cha.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/flow_graph_builder.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/flow_graph_builder.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/intrinsifier.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/intrinsifier.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/jit/compiler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/jit/compiler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/dart_api_message.h
FILE: ../../../flutter/third_party/dart/runtime/vm/dart_api_state.h
FILE: ../../../flutter/third_party/dart/runtime/vm/datastream.h
FILE: ../../../flutter/third_party/dart/runtime/vm/debugger.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/debugger.h
FILE: ../../../flutter/third_party/dart/runtime/vm/double_internals.h
FILE: ../../../flutter/third_party/dart/runtime/vm/flag_list.h
FILE: ../../../flutter/third_party/dart/runtime/vm/flags.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/flags.h
FILE: ../../../flutter/third_party/dart/runtime/vm/globals.h
FILE: ../../../flutter/third_party/dart/runtime/vm/growable_array.h
FILE: ../../../flutter/third_party/dart/runtime/vm/handles.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/handles_impl.h
FILE: ../../../flutter/third_party/dart/runtime/vm/hash_map.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/freelist.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/heap.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/heap.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/pages.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/pointer_block.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/pointer_block.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/scavenger.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/verifier.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_ia32.h
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_x64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/lockers.h
FILE: ../../../flutter/third_party/dart/runtime/vm/megamorphic_cache_table.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/megamorphic_cache_table.h
FILE: ../../../flutter/third_party/dart/runtime/vm/memory_region.h
FILE: ../../../flutter/third_party/dart/runtime/vm/native_arguments.h
FILE: ../../../flutter/third_party/dart/runtime/vm/native_message_handler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/native_message_handler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/object.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/object.h
FILE: ../../../flutter/third_party/dart/runtime/vm/object_set.h
FILE: ../../../flutter/third_party/dart/runtime/vm/object_store.h
FILE: ../../../flutter/third_party/dart/runtime/vm/os_android.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread.h
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_android.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_android.h
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_linux.h
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_macos.h
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_win.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_win.h
FILE: ../../../flutter/third_party/dart/runtime/vm/os_win.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/parser.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/parser.h
FILE: ../../../flutter/third_party/dart/runtime/vm/port.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/proccpuinfo.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/proccpuinfo.h
FILE: ../../../flutter/third_party/dart/runtime/vm/raw_object.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/raw_object.h
FILE: ../../../flutter/third_party/dart/runtime/vm/scopes.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/scopes.h
FILE: ../../../flutter/third_party/dart/runtime/vm/snapshot.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/snapshot.h
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_frame.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/stub_code.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/symbols.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/symbols.h
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_pool.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_pool.h
FILE: ../../../flutter/third_party/dart/runtime/vm/token.h
FILE: ../../../flutter/third_party/dart/runtime/vm/unicode.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/version.h
FILE: ../../../flutter/third_party/dart/runtime/vm/version_in.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/virtual_memory.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/virtual_memory.h
FILE: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_posix.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_win.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/zone.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/zone.h
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/crypto.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/http_date.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/async_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/bigint_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/core_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/isolate_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/math_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/foreign_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/interceptors.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/isolate_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_array.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_number.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_string.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/native_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/regexp_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/string_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/async_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/bigint_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/constant_map.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/core_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/foreign_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/interceptors.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/isolate_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_array.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_number.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_string.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/math_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/native_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/regexp_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/string_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/sdk_library_metadata/lib/libraries.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/builtin.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/common_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/directory_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/eventhandler_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/file_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/platform_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/secure_socket_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/array.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/array_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/double.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/double_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/empty_source.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/errors_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/expando_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/function_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/growable_array.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/identical_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/immutable_map.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/integers.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/invocation_mirror_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/isolate_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/math_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/mirrors_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/mirrors_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/object_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/print_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/regexp_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/stopwatch_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/string_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/timer_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/type_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/weak_property.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/bool_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/date_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/integers_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/map_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/string_buffer_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/array_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/async.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/async_error.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/broadcast_stream_controller.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/future.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/future_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/stream_controller.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/stream_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/stream_pipe.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/timer.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/collection.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/iterable.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/iterator.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/maps.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/splay_tree.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/bool.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/core.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/double.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/errors.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/exceptions.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/identical.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/int.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/invocation.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/iterator.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/list.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/num.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/object.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/print.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/regexp.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/string.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/type.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/uri.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/weak.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/dartium/nativewrappers.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/html_common/conversions.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/html_common/css_class_set.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/html_common/html_common_dart2js.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/html_common/metadata.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/indexed_db/dart2js/indexed_db_dart2js.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/async_cast.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/cast.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/internal.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/common.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/directory.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/directory_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/eventhandler.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/io.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/platform.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/platform_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/secure_server_socket.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/isolate/isolate.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/math/math.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/math/random.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/web_audio/dart2js/web_audio_dart2js.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/web_sql/dart2js/web_sql_dart2js.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2012, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/filter.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/filter.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/gen_snapshot.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/io_natives.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/io_service.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/io_service.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/io_service_no_ssl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/io_service_no_ssl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/process.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/stdio.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/stdio.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/stdio_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/stdio_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/stdio_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/vmservice_impl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/vmservice_impl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/dart_native_api.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/invocation_mirror.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/libgen_in.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/simd128.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/stacktrace.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/typed_data.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/uri.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/application.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/location_manager.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_instances.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/code_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/code_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/context_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/context_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/cpu_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/cpu_profile/virtual_tree.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/cpu_profile_table.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/curly_block.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/error_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/eval_box.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/field_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/field_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/flag_list.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/function_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/function_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/heap_snapshot.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/any_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/icdata_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/icdata_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/instance_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/instance_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/json_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/library_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/library_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/local_var_descriptors_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/megamorphiccache_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/native_memory_profiler.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/object_common.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/object_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/objectpool_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/objectstore_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/observatory_application.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/pc_descriptors_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/sample_buffer_control.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/script_inset.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/script_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/script_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/sentinel_value.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/sentinel_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/source_inset.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/stack_trace_tree_config.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/type_arguments_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/unknown_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/vm_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/tracer.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/atomic.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/signal_blocker.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/allocation.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/class_finalizer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_patcher_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/aot_call_specializer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_arm.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_ia32.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_x64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/block_scheduler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/block_scheduler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/constant_propagator.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/inliner.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/linearscan.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/linearscan.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/locations.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/locations.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/type_propagator.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/type_propagator.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/jit/jit_call_specializer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_arm.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_ia32.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_x64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dart.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dart_api_impl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/deferred_objects.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/deferred_objects.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/deopt_instructions.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/deopt_instructions.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/weak_table.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/weak_table.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_arm.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/isolate.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/isolate.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/json_stream.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/json_stream.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_api_impl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_symbol.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_symbol_posix.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/native_symbol_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_id_ring.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_id_ring.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_store.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/profiler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/profiler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/random.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/random.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/reusable_handles.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/service.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/service.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/service_isolate.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/signal_handler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/signal_handler_android.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/signal_handler_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/signal_handler_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/signal_handler_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_arm.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_frame_arm.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_frame_ia32.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_frame_x64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/tags.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_android.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/http.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/http_headers.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/http_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/http_parser.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/http_session.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/websocket.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/websocket_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/collection_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/convert_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/internal_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/io_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/typed_data_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/annotations.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_primitives.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_rti.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/mirror_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/native_typed_data.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/annotations.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/collection_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/convert_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/internal_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/io_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_names.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_primitives.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/native_typed_data.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/typed_data_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/file_system_entity_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/filter_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/io_service_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/process_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/socket_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/stdio_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/vmservice_io.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/vmservice_server.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/core_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/function.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/internal_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/mirror_reference.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/schedule_microtask_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/stacktrace.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/symbol_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/timer_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/typed_data_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/uri_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/collection_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/null_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/io_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/deferred_load.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/schedule_microtask.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/stream.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/stream_transformers.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/zone.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/collections.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/hash_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/hash_set.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/linked_hash_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/linked_hash_set.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/linked_list.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/list.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/ascii.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/byte_conversion.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/chunked_conversion.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/codec.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/convert.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/converter.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/encoding.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/html_escape.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/json.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/latin1.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/line_splitter.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/string_conversion.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/utf.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/annotations.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/null.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/stacktrace.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/string_sink.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/symbol.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/indexed_db/dart2js/indexed_db_dart2js.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/bytes_builder.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/list.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/print.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/symbol.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/data_transformer.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/file.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/file_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/file_system_entity.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/io_service.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/io_sink.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/link.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/secure_socket.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/socket.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/stdio.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/string_transformer.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js/js.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/math/point.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/math/rectangle.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/mirrors/mirrors.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/typed_data/typed_data.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/client.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/constants.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/message.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/message_router.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/running_isolate.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/running_isolates.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/utils/compiler/create_snapshot_entry.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file.h
FILE: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher.h
FILE: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/filter.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/filter.h
FILE: ../../../flutter/third_party/dart/runtime/bin/gen_snapshot.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/io_natives.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/io_service.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/io_service.h
FILE: ../../../flutter/third_party/dart/runtime/bin/io_service_no_ssl.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/io_service_no_ssl.h
FILE: ../../../flutter/third_party/dart/runtime/bin/process.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket.h
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/stdio.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/stdio.h
FILE: ../../../flutter/third_party/dart/runtime/bin/stdio_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/stdio_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/stdio_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/vmservice_impl.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/vmservice_impl.h
FILE: ../../../flutter/third_party/dart/runtime/include/dart_native_api.h
FILE: ../../../flutter/third_party/dart/runtime/lib/invocation_mirror.h
FILE: ../../../flutter/third_party/dart/runtime/lib/libgen_in.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/simd128.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/stacktrace.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/typed_data.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/uri.cc
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/application.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/location_manager.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_instances.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/code_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/code_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/context_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/context_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/cpu_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/cpu_profile/virtual_tree.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/cpu_profile_table.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/curly_block.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/error_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/eval_box.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/field_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/field_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/flag_list.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/function_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/function_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/heap_snapshot.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/any_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/icdata_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/icdata_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/instance_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/instance_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/json_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/library_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/library_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/local_var_descriptors_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/megamorphiccache_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/native_memory_profiler.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/object_common.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/object_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/objectpool_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/objectstore_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/observatory_application.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/pc_descriptors_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/sample_buffer_control.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/script_inset.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/script_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/script_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/sentinel_value.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/sentinel_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/source_inset.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/stack_trace_tree_config.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/type_arguments_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/unknown_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/vm_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/tracer.dart
FILE: ../../../flutter/third_party/dart/runtime/platform/atomic.h
FILE: ../../../flutter/third_party/dart/runtime/platform/signal_blocker.h
FILE: ../../../flutter/third_party/dart/runtime/vm/allocation.h
FILE: ../../../flutter/third_party/dart/runtime/vm/class_finalizer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/code_patcher_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/aot_call_specializer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_arm.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_ia32.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_x64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/block_scheduler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/block_scheduler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/constant_propagator.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/inliner.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/linearscan.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/linearscan.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/locations.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/locations.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/type_propagator.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/type_propagator.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/jit/jit_call_specializer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_arm.h
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_ia32.h
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_x64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/dart.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/dart_api_impl.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/deferred_objects.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/deferred_objects.h
FILE: ../../../flutter/third_party/dart/runtime/vm/deopt_instructions.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/deopt_instructions.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/weak_table.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/weak_table.h
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions.h
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_arm.h
FILE: ../../../flutter/third_party/dart/runtime/vm/isolate.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/isolate.h
FILE: ../../../flutter/third_party/dart/runtime/vm/json_stream.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/json_stream.h
FILE: ../../../flutter/third_party/dart/runtime/vm/native_api_impl.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/native_symbol.h
FILE: ../../../flutter/third_party/dart/runtime/vm/native_symbol_posix.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/native_symbol_win.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/object_id_ring.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/object_id_ring.h
FILE: ../../../flutter/third_party/dart/runtime/vm/object_store.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/profiler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/profiler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/random.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/random.h
FILE: ../../../flutter/third_party/dart/runtime/vm/reusable_handles.h
FILE: ../../../flutter/third_party/dart/runtime/vm/service.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/service.h
FILE: ../../../flutter/third_party/dart/runtime/vm/service_isolate.h
FILE: ../../../flutter/third_party/dart/runtime/vm/signal_handler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/signal_handler_android.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/signal_handler_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/signal_handler_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/signal_handler_win.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator.h
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_arm.h
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_frame_arm.h
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_frame_ia32.h
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_frame_x64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/tags.h
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter.h
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_android.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_win.cc
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/http.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/http_headers.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/http_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/http_parser.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/http_session.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/websocket.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/websocket_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/collection_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/convert_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/internal_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/io_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/typed_data_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/annotations.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_primitives.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_rti.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/mirror_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/native_typed_data.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/annotations.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/collection_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/convert_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/internal_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/io_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_names.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_primitives.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/native_typed_data.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/typed_data_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/file_system_entity_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/filter_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/io_service_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/process_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/socket_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/stdio_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/vmservice_io.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/vmservice_server.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/core_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/function.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/internal_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/mirror_reference.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/schedule_microtask_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/stacktrace.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/symbol_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/timer_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/typed_data_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/uri_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/collection_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/null_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/io_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/deferred_load.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/schedule_microtask.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/stream.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/stream_transformers.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/zone.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/collections.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/hash_map.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/hash_set.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/linked_hash_map.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/linked_hash_set.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/linked_list.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/list.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/ascii.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/byte_conversion.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/chunked_conversion.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/codec.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/convert.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/converter.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/encoding.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/html_escape.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/json.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/latin1.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/line_splitter.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/string_conversion.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/utf.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/annotations.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/null.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/stacktrace.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/string_sink.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/symbol.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/indexed_db/dart2js/indexed_db_dart2js.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/bytes_builder.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/list.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/print.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/symbol.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/data_transformer.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/file.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/file_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/file_system_entity.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/io_service.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/io_sink.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/link.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/secure_socket.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/socket.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/stdio.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/string_transformer.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js/js.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/math/point.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/math/rectangle.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/mirrors/mirrors.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/typed_data/typed_data.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/client.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/constants.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/message.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/message_router.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/running_isolate.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/running_isolates.dart
FILE: ../../../flutter/third_party/dart/utils/compiler/create_snapshot_entry.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2013, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/web/third_party/webcomponents.min.js + http://polymer.github.io/LICENSE.txt referenced by ../../../flutter/third_party/dart/runtime/observatory/web/third_party/webcomponents.min.js
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/observatory/web/third_party/webcomponents.min.js
----------------------------------------------------------------------------------------------------
Copyright (c) 2014 The Polymer Project Authors. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:

   * Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
   * Redistributions in binary form must reproduce the above
copyright notice, this list of conditions and the following disclaimer
in the documentation and/or other materials provided with the
distribution.
   * Neither the name of Google Inc. nor the names of its
contributors may be used to endorse or promote products derived from
this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/profiler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/bin/shell.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/app.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/object_graph.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/service.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/service_common.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/service_html.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/service_io.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/page.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/settings.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/view_model.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/allocation_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_tree.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/debugger.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/heap_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate_reconnect.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/metrics.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/vm_connect.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/service/object.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/utils.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/web/main.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/address_sanitizer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/memory_sanitizer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/safe_stack.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/verbose_gc_to_bmu.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_patcher_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_arm64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/range_analysis.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/range_analysis.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/method_recognizer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/method_recognizer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_arm64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_arm.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_arm64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_ia32.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_x64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpuid.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpuid.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpuinfo.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_android.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/debugger_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/hash_table.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/spaces.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/weak_code.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/weak_code.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_arm64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/metrics.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/metrics.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_graph.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_graph.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_ir.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_ir.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_ast.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_ast.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_parser.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_parser.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/unibrow-inl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/unibrow.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/unibrow.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/report.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/report.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/ring_buffer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_arm64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_frame_arm64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/tags.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/preambles/d8.js + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/preambles/jsshell.js + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/linked_hash_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/synced/embedded_names.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/convert_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/lib_prefix.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/profiler.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/collection/set.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/sink.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/developer/profiler.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/process.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/service_object.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/isolate/capability.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/lib/profiler.cc
FILE: ../../../flutter/third_party/dart/runtime/observatory/bin/shell.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/app.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/object_graph.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/service.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/service_common.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/service_html.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/service_io.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/page.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/settings.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/view_model.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/allocation_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_tree.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/debugger.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/heap_map.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate_reconnect.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/metrics.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/vm_connect.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/service/object.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/utils.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/web/main.dart
FILE: ../../../flutter/third_party/dart/runtime/platform/address_sanitizer.h
FILE: ../../../flutter/third_party/dart/runtime/platform/memory_sanitizer.h
FILE: ../../../flutter/third_party/dart/runtime/platform/safe_stack.h
FILE: ../../../flutter/third_party/dart/runtime/tools/verbose_gc_to_bmu.dart
FILE: ../../../flutter/third_party/dart/runtime/vm/code_patcher_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_arm64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/range_analysis.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/range_analysis.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/method_recognizer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/method_recognizer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_arm64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_arm.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_arm64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_ia32.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_x64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpuid.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/cpuid.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpuinfo.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_android.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_win.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/debugger_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/hash_table.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/spaces.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/weak_code.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/weak_code.h
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_arm64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/metrics.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/metrics.h
FILE: ../../../flutter/third_party/dart/runtime/vm/object_graph.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/object_graph.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_ir.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_ir.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_ast.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_ast.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_parser.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_parser.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/unibrow-inl.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/unibrow.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/unibrow.h
FILE: ../../../flutter/third_party/dart/runtime/vm/report.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/report.h
FILE: ../../../flutter/third_party/dart/runtime/vm/ring_buffer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_arm64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_frame_arm64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/tags.cc
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/preambles/d8.js
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/preambles/jsshell.js
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/linked_hash_map.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/synced/embedded_names.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/convert_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/lib_prefix.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/profiler.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/collection/set.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/sink.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/developer/profiler.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/process.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/service_object.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/isolate/capability.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2014, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dart_io_api_impl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/observatory_assets_empty.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/bin/dart_io_api.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/developer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/timeline.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/vmservice.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/allocation_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/cli.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/debugger.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/sample_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/allocation_profile/allocation_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/cli/command.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/debugger/debugger.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/debugger/debugger_location.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/heap_snapshot.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/logging.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/logging_list.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/megamorphiccache_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/objectpool_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/persistent_handles.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/ports.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/timeline_page.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/sample_profile/sample_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/web/timeline.js + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/precompiler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/precompiler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/log.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/log.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/profiler_service.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/profiler_service.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/program_visitor.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/program_visitor.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_bytecode.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_bytecode.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_bytecode_inl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_bytecodes.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_interpreter.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_interpreter.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/scope_timer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/service_event.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/service_event.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/service_isolate.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/source_report.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/source_report.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_barrier.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_registry.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_registry.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/timeline.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/timeline.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/developer_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/classes.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/errors.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/operations.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/rtti.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/runtime.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/types.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/utils.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/debugger.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/developer_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/preambles/d8.js + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/preambles/jsshell.js + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/async_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/developer.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/timeline.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/compact_hash.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/convert/base64.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/developer/developer.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/developer/extension.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/developer/timeline.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/io_resource_info.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/security_context.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js/_js_annotations.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/asset.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/vmservice.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/dart_io_api_impl.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/observatory_assets_empty.cc
FILE: ../../../flutter/third_party/dart/runtime/include/bin/dart_io_api.h
FILE: ../../../flutter/third_party/dart/runtime/lib/developer.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/timeline.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/vmservice.cc
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/allocation_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/cli.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/debugger.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/sample_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/allocation_profile/allocation_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/cli/command.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/debugger/debugger.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/debugger/debugger_location.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/heap_snapshot.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/logging.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/logging_list.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/megamorphiccache_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/objectpool_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/persistent_handles.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/ports.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/timeline_page.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/sample_profile/sample_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/web/timeline.js
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/precompiler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/precompiler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/log.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/log.h
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/profiler_service.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/profiler_service.h
FILE: ../../../flutter/third_party/dart/runtime/vm/program_visitor.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/program_visitor.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_bytecode.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_bytecode.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_assembler_bytecode_inl.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_bytecodes.h
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_interpreter.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/regexp/regexp_interpreter.h
FILE: ../../../flutter/third_party/dart/runtime/vm/scope_timer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/service_event.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/service_event.h
FILE: ../../../flutter/third_party/dart/runtime/vm/service_isolate.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/source_report.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/source_report.h
FILE: ../../../flutter/third_party/dart/runtime/vm/thread.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread.h
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_barrier.h
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_registry.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_registry.h
FILE: ../../../flutter/third_party/dart/runtime/vm/timeline.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/timeline.h
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/developer_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/classes.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/errors.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/operations.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/rtti.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/runtime.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/types.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/utils.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/debugger.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/developer_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/preambles/d8.js
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/preambles/jsshell.js
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/async_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/developer.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/timeline.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/compact_hash.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/convert/base64.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/developer/developer.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/developer/extension.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/developer/timeline.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/io_resource_info.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/security_context.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js/_js_annotations.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/asset.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/vmservice.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2015, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/crypto_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/directory_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/eventhandler_fuchsia.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_support.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/loader.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/loader.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/platform_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/process_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/reference_counting.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/root_certificates_unsupported.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base_fuchsia.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/stdio_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/thread_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/utils_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/stacktrace.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/event.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/models.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/repositories.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/notification.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_allocation_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/containers/virtual_collection.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/containers/virtual_tree.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/error_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/general_error.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/custom_element.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/nav_bar.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/nav_menu.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/rendering_queue.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/rendering_scheduler.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/uris.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/inbound_references.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/counter_chart.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/location.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/run_state.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/shared_summary.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/summary.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/metric/details.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/metric/graph.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/class_menu.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/isolate_menu.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/library_menu.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/menu_item.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/notify.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/notify_event.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/notify_exception.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/refresh.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/top_menu.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/vm_menu.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/retaining_path.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/source_link.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/strongly_reachable_instances.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/vm_connect_target.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/exceptions.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/allocation_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/breakpoint.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/class.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/code.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/context.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/error.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/event.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/extension_data.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/field.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/flag.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/frame.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/function.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/guarded.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/heap_space.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/icdata.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/inbound_references.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/instance.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/isolate.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/library.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/local_var_descriptors.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/map_association.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/megamorphiccache.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/metric.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/notification.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/object.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/objectpool.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/objectstore.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/pc_descriptors.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/persistent_handles.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/ports.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/retaining_path.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/sample_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/script.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/sentinel.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/source_location.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/target.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/timeline_event.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/type_arguments.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/unknown.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/vm.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/allocation_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/breakpoint.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/class.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/context.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/editor.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/eval.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/event.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/field.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/flag.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/function.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/heap_snapshot.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/icdata.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/inbound_references.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/instance.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/isolate.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/library.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/megamorphiccache.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/metric.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/notification.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/object.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/objectpool.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/objectstore.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/persistent_handles.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/ports.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/reachable_size.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/retained_size.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/retaining_path.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/sample_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/script.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/strongly_reachable_instances.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/target.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/type_arguments.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/allocation_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/breakpoint.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/class.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/context.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/editor.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/eval.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/event.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/field.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/flag.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/function.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/heap_snapshot.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/icdata.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/inbound_references.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/instance.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/isolate.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/library.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/megamorphiccache.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/metric.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/notification.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/object.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/objectpool.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/objectstore.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/persistent_handles.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/ports.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/reachable_size.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/retained_size.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/retaining_path.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/sample_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/script.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/settings.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/strongly_reachable_instances.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/target.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/type_arguments.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/web/timeline_message_handler.js + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/syslog_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/utils_fuchsia.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/app_snapshot.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/app_snapshot.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/canonical_tables.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/branch_optimizer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/branch_optimizer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/redundancy_elimination.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/redundancy_elimination.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_binary_flowgraph.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_to_il.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_to_il.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dart_api_state.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/become.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/become.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/safepoint.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/safepoint.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/isolate_reload.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/isolate_reload.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/kernel.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/kernel.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/kernel_binary.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/kernel_isolate.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/kernel_isolate.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/kernel_loader.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/kernel_loader.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/lockers.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_reload.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_service.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_fuchsia.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/signal_handler_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/token_position.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/token_position.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/developer/service.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js_util/js_util.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/devfs.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/crypto_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/directory_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/eventhandler_fuchsia.h
FILE: ../../../flutter/third_party/dart/runtime/bin/file_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file_support.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file_system_watcher_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/loader.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/loader.h
FILE: ../../../flutter/third_party/dart/runtime/bin/platform_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/process_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/reference_counting.h
FILE: ../../../flutter/third_party/dart/runtime/bin/root_certificates_unsupported.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base_fuchsia.h
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/stdio_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/thread_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/utils_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/stacktrace.h
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/event.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/models.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/repositories.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/app/notification.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/class_allocation_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/containers/virtual_collection.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/containers/virtual_tree.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/error_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/general_error.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/custom_element.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/nav_bar.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/nav_menu.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/rendering_queue.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/rendering_scheduler.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/helpers/uris.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/inbound_references.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/counter_chart.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/location.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/run_state.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/shared_summary.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/isolate/summary.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/metric/details.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/metric/graph.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/class_menu.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/isolate_menu.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/library_menu.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/menu_item.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/notify.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/notify_event.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/notify_exception.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/refresh.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/top_menu.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/vm_menu.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/retaining_path.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/source_link.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/strongly_reachable_instances.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/vm_connect_target.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/exceptions.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/allocation_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/breakpoint.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/class.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/code.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/context.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/error.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/event.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/extension_data.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/field.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/flag.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/frame.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/function.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/guarded.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/heap_space.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/icdata.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/inbound_references.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/instance.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/isolate.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/library.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/local_var_descriptors.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/map_association.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/megamorphiccache.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/metric.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/notification.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/object.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/objectpool.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/objectstore.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/pc_descriptors.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/persistent_handles.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/ports.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/retaining_path.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/sample_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/script.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/sentinel.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/source_location.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/target.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/timeline_event.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/type_arguments.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/unknown.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/vm.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/allocation_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/breakpoint.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/class.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/context.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/editor.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/eval.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/event.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/field.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/flag.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/function.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/heap_snapshot.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/icdata.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/inbound_references.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/instance.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/isolate.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/library.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/megamorphiccache.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/metric.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/notification.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/object.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/objectpool.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/objectstore.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/persistent_handles.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/ports.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/reachable_size.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/retained_size.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/retaining_path.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/sample_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/script.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/strongly_reachable_instances.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/target.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/type_arguments.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/allocation_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/breakpoint.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/class.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/context.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/editor.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/eval.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/event.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/field.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/flag.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/function.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/heap_snapshot.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/icdata.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/inbound_references.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/instance.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/isolate.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/library.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/megamorphiccache.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/metric.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/notification.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/object.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/objectpool.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/objectstore.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/persistent_handles.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/ports.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/reachable_size.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/retained_size.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/retaining_path.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/sample_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/script.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/settings.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/strongly_reachable_instances.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/target.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/type_arguments.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/web/timeline_message_handler.js
FILE: ../../../flutter/third_party/dart/runtime/platform/syslog_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/utils_fuchsia.h
FILE: ../../../flutter/third_party/dart/runtime/vm/app_snapshot.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/app_snapshot.h
FILE: ../../../flutter/third_party/dart/runtime/vm/canonical_tables.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/branch_optimizer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/branch_optimizer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/redundancy_elimination.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/redundancy_elimination.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_binary_flowgraph.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_to_il.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_to_il.h
FILE: ../../../flutter/third_party/dart/runtime/vm/cpuinfo_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/dart_api_state.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/become.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/become.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/safepoint.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/safepoint.h
FILE: ../../../flutter/third_party/dart/runtime/vm/isolate_reload.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/isolate_reload.h
FILE: ../../../flutter/third_party/dart/runtime/vm/kernel.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/kernel.h
FILE: ../../../flutter/third_party/dart/runtime/vm/kernel_binary.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/kernel_isolate.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/kernel_isolate.h
FILE: ../../../flutter/third_party/dart/runtime/vm/kernel_loader.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/kernel_loader.h
FILE: ../../../flutter/third_party/dart/runtime/vm/lockers.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/object_reload.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/object_service.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_fuchsia.h
FILE: ../../../flutter/third_party/dart/runtime/vm/signal_handler_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/token_position.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/token_position.h
FILE: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_fuchsia.cc
FILE: ../../../flutter/third_party/dart/sdk/lib/developer/service.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js_util/js_util.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/devfs.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2016, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dfe.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dfe.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/error_exit.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/error_exit.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/gzip.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/gzip.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/isolate_data.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/main_options.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/main_options.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/namespace.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/namespace.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/namespace_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/namespace_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/namespace_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/namespace_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/options.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/options.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/secure_socket_filter.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/secure_socket_filter.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/secure_socket_utils.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/secure_socket_utils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/security_context.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/security_context.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/security_context_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/security_context_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/security_context_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/security_context_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/snapshot_utils.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/snapshot_utils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/sync_socket.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/sync_socket.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/sync_socket_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/sync_socket_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/sync_socket_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/sync_socket_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/async.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/containers/search_bar.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/reload.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/singletargetcache_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/singletargetcache_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/subtypetestcache_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/subtypetestcache_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/unlinkedcall_ref.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/unlinkedcall_view.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/service.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/single_target_cache.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/subtype_test_cache.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/timeline.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/unlinked_call.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/single_target_cache.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/subtype_test_cache.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/timeline.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/unlinked_call.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/vm.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/single_target_cache.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/subtype_test_cache.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/timeline.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/unlinked_call.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/vm.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/allocation.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/growable_array.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_riscv.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/locations_helpers.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/locations_helpers_arm.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/call_specializer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/call_specializer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_binary_flowgraph.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/prologue_builder.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/prologue_builder.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/jit/jit_call_specializer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_riscv.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_x86.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dwarf.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dwarf.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/fixed_cache.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/gdb_helpers.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/compactor.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/compactor.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/image_snapshot.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/image_snapshot.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/json_writer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/json_writer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/kernel_binary.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_riscv.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_trace.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_trace.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/timeline_android.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/timeline_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/timeline_linux.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/zone_text_buffer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/zone_text_buffer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/overrides.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/custom_hash_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/identity_hash_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/linked_hash_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/namespace_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/sync_socket_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/class_id_fasta.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/bigint_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/cli/cli.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/bigint.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/linked_list.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/embedder_config.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/namespace_impl.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/overrides.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/sync_socket.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/vmservice/named_lookup.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/utils/bazel/kernel_worker.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/dfe.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/dfe.h
FILE: ../../../flutter/third_party/dart/runtime/bin/error_exit.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/error_exit.h
FILE: ../../../flutter/third_party/dart/runtime/bin/gzip.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/gzip.h
FILE: ../../../flutter/third_party/dart/runtime/bin/isolate_data.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/main_options.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/main_options.h
FILE: ../../../flutter/third_party/dart/runtime/bin/namespace.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/namespace.h
FILE: ../../../flutter/third_party/dart/runtime/bin/namespace_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/namespace_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/namespace_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/namespace_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/options.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/options.h
FILE: ../../../flutter/third_party/dart/runtime/bin/secure_socket_filter.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/secure_socket_filter.h
FILE: ../../../flutter/third_party/dart/runtime/bin/secure_socket_utils.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/secure_socket_utils.h
FILE: ../../../flutter/third_party/dart/runtime/bin/security_context.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/security_context.h
FILE: ../../../flutter/third_party/dart/runtime/bin/security_context_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/security_context_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/security_context_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/security_context_win.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/snapshot_utils.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/snapshot_utils.h
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base.h
FILE: ../../../flutter/third_party/dart/runtime/bin/sync_socket.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/sync_socket.h
FILE: ../../../flutter/third_party/dart/runtime/bin/sync_socket_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/sync_socket_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/sync_socket_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/sync_socket_win.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/async.cc
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/containers/search_bar.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/nav/reload.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/singletargetcache_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/singletargetcache_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/subtypetestcache_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/subtypetestcache_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/unlinkedcall_ref.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/unlinkedcall_view.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/service.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/single_target_cache.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/subtype_test_cache.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/timeline.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/unlinked_call.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/single_target_cache.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/subtype_test_cache.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/timeline.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/unlinked_call.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/vm.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/single_target_cache.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/subtype_test_cache.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/timeline.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/unlinked_call.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/vm.dart
FILE: ../../../flutter/third_party/dart/runtime/platform/allocation.h
FILE: ../../../flutter/third_party/dart/runtime/platform/growable_array.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_riscv.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/locations_helpers.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/locations_helpers_arm.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/call_specializer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/call_specializer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_binary_flowgraph.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/prologue_builder.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/prologue_builder.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/jit/jit_call_specializer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_riscv.h
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_x86.h
FILE: ../../../flutter/third_party/dart/runtime/vm/dwarf.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/dwarf.h
FILE: ../../../flutter/third_party/dart/runtime/vm/fixed_cache.h
FILE: ../../../flutter/third_party/dart/runtime/vm/gdb_helpers.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/compactor.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/compactor.h
FILE: ../../../flutter/third_party/dart/runtime/vm/image_snapshot.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/image_snapshot.h
FILE: ../../../flutter/third_party/dart/runtime/vm/json_writer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/json_writer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/kernel_binary.h
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_riscv.h
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_trace.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_trace.h
FILE: ../../../flutter/third_party/dart/runtime/vm/timeline_android.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/timeline_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/timeline_linux.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/zone_text_buffer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/zone_text_buffer.h
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/overrides.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/custom_hash_map.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/identity_hash_map.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/linked_hash_map.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/profile.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/namespace_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/sync_socket_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/class_id_fasta.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm_shared/lib/bigint_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/cli/cli.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/bigint.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/linked_list.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/embedder_config.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/namespace_impl.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/overrides.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/io/sync_socket.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/vmservice/named_lookup.dart
FILE: ../../../flutter/third_party/dart/utils/bazel/kernel_worker.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2017, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/crashpad.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dart_embedder_api_impl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/typed_data_utils.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/typed_data_utils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/dart_embedder_api.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/dartfuzz.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/base64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/base64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/code_statistics.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/code_statistics.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/compile_type.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/loops.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/loops.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/slot.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/slot.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_pass.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_pass.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_state.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_state.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/base_flow_graph_builder.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/base_flow_graph_builder.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/constant_reader.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/constant_reader.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_fingerprints.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_fingerprints.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_translation_helper.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_translation_helper.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/scope_builder.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/scope_builder.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/relocation.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/datastream.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/finalizable_data.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/hash.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/raw_object_fields.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/raw_object_fields.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/reverse_pc_lookup_cache.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/reverse_pc_lookup_cache.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/type_testing_stubs.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/type_testing_stubs.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/v8_snapshot_writer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/v8_snapshot_writer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/instantiation.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js/_js.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js/_js_client.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js/_js_server.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/crashpad.h
FILE: ../../../flutter/third_party/dart/runtime/bin/dart_embedder_api_impl.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/typed_data_utils.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/typed_data_utils.h
FILE: ../../../flutter/third_party/dart/runtime/include/dart_embedder_api.h
FILE: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/dartfuzz.dart
FILE: ../../../flutter/third_party/dart/runtime/vm/base64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/base64.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/code_statistics.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/code_statistics.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/compile_type.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/loops.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/loops.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/slot.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/slot.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_pass.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_pass.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_state.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_state.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/base_flow_graph_builder.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/base_flow_graph_builder.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/constant_reader.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/constant_reader.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_fingerprints.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_fingerprints.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_translation_helper.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/kernel_translation_helper.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/scope_builder.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/frontend/scope_builder.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/relocation.h
FILE: ../../../flutter/third_party/dart/runtime/vm/constants.h
FILE: ../../../flutter/third_party/dart/runtime/vm/datastream.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/finalizable_data.h
FILE: ../../../flutter/third_party/dart/runtime/vm/hash.h
FILE: ../../../flutter/third_party/dart/runtime/vm/raw_object_fields.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/raw_object_fields.h
FILE: ../../../flutter/third_party/dart/runtime/vm/reverse_pc_lookup_cache.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/reverse_pc_lookup_cache.h
FILE: ../../../flutter/third_party/dart/runtime/vm/type_testing_stubs.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/type_testing_stubs.h
FILE: ../../../flutter/third_party/dart/runtime/vm/v8_snapshot_writer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/v8_snapshot_writer.h
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/instantiation.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js/_js.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js/_js_client.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js/_js_server.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2018, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/console.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/console_posix.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/console_win.cc + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/console.h
FILE: ../../../flutter/third_party/dart/runtime/bin/console_posix.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/console_win.cc
----------------------------------------------------------------------------------------------------
Copyright (c) 2018, the Dart project authors. Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/elf_loader.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/elf_loader.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/namespace_fuchsia.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/ffi.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/ffi_dynamic_library.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/tree_map.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/isolate_group.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/isolate_group.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/isolate_group.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/timeline_base.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/elf.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/thread_sanitizer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/dartfuzz_api_table.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/dartfuzz_ffi_api.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/dartfuzz_type_table.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/gen_api_table.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/gen_type_table.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/gen_util.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/ffi/sdk_lib_ffi_generator.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/graphexplorer/graphexplorer.html + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/graphexplorer/graphexplorer.js + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/run_clang_tidy.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bss_relocs.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bss_relocs.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/class_id.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_comments.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_comments.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_entry_kind.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/block_builder.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/evaluator.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/evaluator.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_checker.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_checker.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_test_helper.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_test_helper.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/graph_intrinsifier.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/graph_intrinsifier.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/offsets_extractor.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/recognized_methods_list.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/relocation.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/runtime_api.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/runtime_api.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/runtime_offsets_extracted.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/runtime_offsets_list.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_arm.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_arm64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_ia32.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/elf.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/elf.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/frame_layout.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/intrusive_dlist.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/libfuzzer/dart_libfuzzer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/longjump.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/pointer_tagging.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/splay-tree.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/static_type_exactness_state.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stub_code_list.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_stack_resource.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_stack_resource.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_state.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_state.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/rti.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/synced/recipe_syntax.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_dynamic_library_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_native_type_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/annotations.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/dynamic_library.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/ffi.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/native_type.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/struct.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/errors.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/elf_loader.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/elf_loader.h
FILE: ../../../flutter/third_party/dart/runtime/bin/namespace_fuchsia.h
FILE: ../../../flutter/third_party/dart/runtime/lib/ffi.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/ffi_dynamic_library.cc
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/tree_map.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/objects/isolate_group.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/models/repositories/isolate_group.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/isolate_group.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/repositories/timeline_base.dart
FILE: ../../../flutter/third_party/dart/runtime/platform/elf.h
FILE: ../../../flutter/third_party/dart/runtime/platform/thread_sanitizer.h
FILE: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/dartfuzz_api_table.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/dartfuzz_ffi_api.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/dartfuzz_type_table.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/gen_api_table.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/gen_type_table.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/gen_util.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/ffi/sdk_lib_ffi_generator.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/graphexplorer/graphexplorer.html
FILE: ../../../flutter/third_party/dart/runtime/tools/graphexplorer/graphexplorer.js
FILE: ../../../flutter/third_party/dart/runtime/tools/run_clang_tidy.dart
FILE: ../../../flutter/third_party/dart/runtime/vm/bss_relocs.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/bss_relocs.h
FILE: ../../../flutter/third_party/dart/runtime/vm/class_id.h
FILE: ../../../flutter/third_party/dart/runtime/vm/code_comments.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/code_comments.h
FILE: ../../../flutter/third_party/dart/runtime/vm/code_entry_kind.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/block_builder.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/evaluator.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/evaluator.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_checker.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_checker.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_test_helper.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_test_helper.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/graph_intrinsifier.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/graph_intrinsifier.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/offsets_extractor.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/recognized_methods_list.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/relocation.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/runtime_api.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/runtime_api.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/runtime_offsets_extracted.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/runtime_offsets_list.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_arm.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_arm64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_ia32.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/elf.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/elf.h
FILE: ../../../flutter/third_party/dart/runtime/vm/frame_layout.h
FILE: ../../../flutter/third_party/dart/runtime/vm/intrusive_dlist.h
FILE: ../../../flutter/third_party/dart/runtime/vm/libfuzzer/dart_libfuzzer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/longjump.h
FILE: ../../../flutter/third_party/dart/runtime/vm/pointer_tagging.h
FILE: ../../../flutter/third_party/dart/runtime/vm/splay-tree.h
FILE: ../../../flutter/third_party/dart/runtime/vm/static_type_exactness_state.h
FILE: ../../../flutter/third_party/dart/runtime/vm/stub_code_list.h
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_stack_resource.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_stack_resource.h
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_state.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_state.h
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/rti.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/synced/recipe_syntax.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_dynamic_library_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_native_type_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/annotations.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/dynamic_library.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/ffi.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/native_type.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/struct.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/errors.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2019, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/io/network_profiling.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/sdk/lib/io/network_profiling.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2019, the Dart project authors. Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dartdev_isolate.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dartdev_isolate.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/exe_utils.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/exe_utils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/ffi_unit_test/run_ffi_unit_tests.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/file_win.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/platform_macos.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/dart_api_dl.c + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/dart_api_dl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/dart_version.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/internal/dart_api_dl_impl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/bin/heap_snapshot.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/process_snapshot.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/allocation.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/leak_sanitizer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/priority_queue.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/unaligned.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/undefined_behavior_sanitizer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/canonical_tables.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/closure_functions_cache.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/closure_functions_cache.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/dispatch_table_generator.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/dispatch_table_generator.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/precompiler_tracer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/precompiler_tracer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/api/deopt_id.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/api/print_filter.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/api/print_filter.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/api/type_check_mode.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_base.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/abi.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/abi.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/callback.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/callback.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/frame_rebase.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/frame_rebase.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/marshaller.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/marshaller.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_calling_convention.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_calling_convention.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_location.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_location.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_type.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_type.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/recognized_method.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/recognized_method.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/unit_test_custom_zone.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/unit_test_custom_zone.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/write_barrier_elimination.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/write_barrier_elimination.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_base.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dispatch_table.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/dispatch_table.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/experimental_features.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/experimental_features.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/field_table.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/field_table.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/port_set.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/tagged_pointer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/timeline_macos.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/visitor.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/embedder_config.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/js_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_struct_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/internal/lowering.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/dartdev_isolate.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/dartdev_isolate.h
FILE: ../../../flutter/third_party/dart/runtime/bin/exe_utils.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/exe_utils.h
FILE: ../../../flutter/third_party/dart/runtime/bin/ffi_unit_test/run_ffi_unit_tests.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/file_win.h
FILE: ../../../flutter/third_party/dart/runtime/bin/platform_macos.h
FILE: ../../../flutter/third_party/dart/runtime/include/dart_api_dl.c
FILE: ../../../flutter/third_party/dart/runtime/include/dart_api_dl.h
FILE: ../../../flutter/third_party/dart/runtime/include/dart_version.h
FILE: ../../../flutter/third_party/dart/runtime/include/internal/dart_api_dl_impl.h
FILE: ../../../flutter/third_party/dart/runtime/observatory/bin/heap_snapshot.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/process_snapshot.dart
FILE: ../../../flutter/third_party/dart/runtime/platform/allocation.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/leak_sanitizer.h
FILE: ../../../flutter/third_party/dart/runtime/platform/priority_queue.h
FILE: ../../../flutter/third_party/dart/runtime/platform/unaligned.h
FILE: ../../../flutter/third_party/dart/runtime/platform/undefined_behavior_sanitizer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/canonical_tables.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/closure_functions_cache.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/closure_functions_cache.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/dispatch_table_generator.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/dispatch_table_generator.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/precompiler_tracer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/aot/precompiler_tracer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/api/deopt_id.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/api/print_filter.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/api/print_filter.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/api/type_check_mode.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/assembler_base.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/abi.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/abi.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/callback.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/callback.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/frame_rebase.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/frame_rebase.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/marshaller.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/marshaller.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_calling_convention.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_calling_convention.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_location.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_location.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_type.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/native_type.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/recognized_method.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/recognized_method.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/unit_test_custom_zone.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/unit_test_custom_zone.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/write_barrier_elimination.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/write_barrier_elimination.h
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_base.h
FILE: ../../../flutter/third_party/dart/runtime/vm/dispatch_table.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/dispatch_table.h
FILE: ../../../flutter/third_party/dart/runtime/vm/experimental_features.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/experimental_features.h
FILE: ../../../flutter/third_party/dart/runtime/vm/field_table.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/field_table.h
FILE: ../../../flutter/third_party/dart/runtime/vm/port_set.h
FILE: ../../../flutter/third_party/dart/runtime/vm/tagged_pointer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/timeline_macos.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/visitor.cc
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/embedder_config.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/js_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_struct_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/internal/lowering.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2020, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/analyze_snapshot.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/platform_macos_cocoa.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/platform_macos_cocoa.mm + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/socket_base_posix.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/utils.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/virtual_memory.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/virtual_memory_fuchsia.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/virtual_memory_posix.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/virtual_memory_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/analyze_snapshot_api.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/analyze_snapshot_api_impl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/code_patcher_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_timings.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_timings.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/range.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/cpu_riscv.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/debugger_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions_riscv.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/message_snapshot.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/message_snapshot.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_graph_copy.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/object_graph_copy.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/pending_deopts.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/pending_deopts.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_riscv.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_frame_riscv.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_android_arm.S + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_compressed.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_compressed.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/dart2js_runtime_metrics.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/late_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_allocation_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/enum.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/abi.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/abi_specific.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/allocation.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/union.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/analyze_snapshot.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/platform_macos_cocoa.h
FILE: ../../../flutter/third_party/dart/runtime/bin/platform_macos_cocoa.mm
FILE: ../../../flutter/third_party/dart/runtime/bin/socket_base_posix.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/utils.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/virtual_memory.h
FILE: ../../../flutter/third_party/dart/runtime/bin/virtual_memory_fuchsia.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/virtual_memory_posix.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/virtual_memory_win.cc
FILE: ../../../flutter/third_party/dart/runtime/include/analyze_snapshot_api.h
FILE: ../../../flutter/third_party/dart/runtime/vm/analyze_snapshot_api_impl.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/code_patcher_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/asm_intrinsifier_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/flow_graph_compiler_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_timings.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/compiler_timings.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/ffi/range.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/stub_code_compiler_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/cpu_riscv.h
FILE: ../../../flutter/third_party/dart/runtime/vm/debugger_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions_riscv.h
FILE: ../../../flutter/third_party/dart/runtime/vm/message_snapshot.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/message_snapshot.h
FILE: ../../../flutter/third_party/dart/runtime/vm/object_graph_copy.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/object_graph_copy.h
FILE: ../../../flutter/third_party/dart/runtime/vm/pending_deopts.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/pending_deopts.h
FILE: ../../../flutter/third_party/dart/runtime/vm/runtime_entry_riscv.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_frame_riscv.h
FILE: ../../../flutter/third_party/dart/runtime/vm/thread_interrupter_android_arm.S
FILE: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_compressed.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/virtual_memory_compressed.h
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/dart2js_runtime_metrics.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/late_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_allocation_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/enum.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/abi.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/abi_specific.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/allocation.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/union.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2021, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/test_utils.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/test_utils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/thread_absl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/integers.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/mach_o.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/pe.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/bin/download.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/bin/explore.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/cli.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/completion.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/console.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/expression.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/load.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_serializer.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_serializer.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/gc_shared.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/gc_shared.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/page.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/page.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/sampler.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/sampler.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/instructions.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_absl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os_thread_absl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_x64.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_x64.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_http/http_testing.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_names.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/js_util_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/synced/embedded_names.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_native_finalizer_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/finalizer_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/hash_factories.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/record_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/boxed_bool.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/class_id.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/convert_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/core_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/deferred.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/double_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/errors_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/hash_factories.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/identical_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/internal_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/isolate_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_util_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/list.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/math_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/named_parameters.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/object_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/print_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/regexp_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/regexp_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/simd_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/stack_trace_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/stopwatch_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/symbol_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/timer_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/type.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/typed_data_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/uri_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/convert_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_wasm/wasm_types.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/core/record.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/c_type.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/ffi/native_finalizer.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js/js_wasm.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js_util/js_util_wasm.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/test_utils.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/test_utils.h
FILE: ../../../flutter/third_party/dart/runtime/bin/thread_absl.cc
FILE: ../../../flutter/third_party/dart/runtime/lib/integers.h
FILE: ../../../flutter/third_party/dart/runtime/platform/mach_o.h
FILE: ../../../flutter/third_party/dart/runtime/platform/pe.h
FILE: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/bin/download.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/bin/explore.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/cli.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/completion.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/console.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/expression.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/heapsnapshot/lib/src/load.dart
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_serializer.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/il_serializer.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/gc_shared.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/gc_shared.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/page.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/page.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/sampler.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/sampler.h
FILE: ../../../flutter/third_party/dart/runtime/vm/instructions.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_absl.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/os_thread_absl.h
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_x64.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_x64.h
FILE: ../../../flutter/third_party/dart/sdk/lib/_http/http_testing.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/js_names.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/js_util_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/synced/embedded_names.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/ffi_native_finalizer_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/finalizer_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/hash_factories.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/record_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/boxed_bool.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/class_id.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/convert_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/core_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/deferred.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/double_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/errors_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/hash_factories.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/identical_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/internal_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/isolate_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_util_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/list.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/math_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/named_parameters.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/object_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/print_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/regexp_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/regexp_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/simd_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/stack_trace_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/stopwatch_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/symbol_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/timer_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/type.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/typed_data_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/uri_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/convert_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_wasm/wasm_types.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/core/record.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/c_type.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/ffi/native_finalizer.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js/js_wasm.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js_util/js_util_wasm.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2022, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dart_test_component.cml + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dartaotruntime_test_component.cml + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/main_impl.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/run_vm_tests_test_component.cml + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/ffi_dynamic_library.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/unwinding_records.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/unwinding_records.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/unwinding_records_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm-jit.shard.cml + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm.shard.cml + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/parallel_move_resolver.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/parallel_move_resolver.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/ffi/native_assets.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/ffi/native_assets.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/ffi_callback_metadata.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/ffi_callback_metadata.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/unwinding_records.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/unwinding_records.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/unwinding_records_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/js_allow_interop_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/debugger.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/records.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_allow_interop_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/preambles/seal_native_object.js + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/records.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/convert_utf_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/js_interop_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/js_interop_unsafe_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/js_types.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/boxed_double.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/boxed_int.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/boxed_int_to_string.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/closure.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/date_patch_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/error_utils.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/int_common_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/int_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_array.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_interop_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_interop_unsafe_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_string.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_string_convert.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_typed_array.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_types.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/object_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/record_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/simd.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/string.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/string_buffer_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/string_helper.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/string_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/sync_star_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/typed_data.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/wasm_types_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/weak_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/boxed_int_to_string.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/string_buffer_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/string_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/typed_data_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/async/future_extensions.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js_interop/js_interop.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/js_interop_unsafe/js_interop_unsafe.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/dart_test_component.cml
FILE: ../../../flutter/third_party/dart/runtime/bin/dartaotruntime_test_component.cml
FILE: ../../../flutter/third_party/dart/runtime/bin/main_impl.h
FILE: ../../../flutter/third_party/dart/runtime/bin/run_vm_tests_test_component.cml
FILE: ../../../flutter/third_party/dart/runtime/lib/ffi_dynamic_library.h
FILE: ../../../flutter/third_party/dart/runtime/platform/unwinding_records.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/unwinding_records.h
FILE: ../../../flutter/third_party/dart/runtime/platform/unwinding_records_win.cc
FILE: ../../../flutter/third_party/dart/runtime/vm-jit.shard.cml
FILE: ../../../flutter/third_party/dart/runtime/vm.shard.cml
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/parallel_move_resolver.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/parallel_move_resolver.h
FILE: ../../../flutter/third_party/dart/runtime/vm/ffi/native_assets.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/ffi/native_assets.h
FILE: ../../../flutter/third_party/dart/runtime/vm/ffi_callback_metadata.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/ffi_callback_metadata.h
FILE: ../../../flutter/third_party/dart/runtime/vm/unwinding_records.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/unwinding_records.h
FILE: ../../../flutter/third_party/dart/runtime/vm/unwinding_records_win.cc
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/patch/js_allow_interop_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/debugger.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_runtime/records.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/js_allow_interop_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/preambles/seal_native_object.js
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/records.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/convert_utf_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/js_interop_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/js_interop_unsafe_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/js_types.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/boxed_double.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/boxed_int.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/boxed_int_to_string.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/closure.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/date_patch_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/error_utils.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/int_common_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/int_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_array.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_interop_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_interop_unsafe_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_string.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_string_convert.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_typed_array.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_types.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/object_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/record_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/simd.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/string.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/string_buffer_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/string_helper.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/string_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/sync_star_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/typed_data.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/wasm_types_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/weak_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/boxed_int_to_string.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/string_buffer_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/string_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/typed_data_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/async/future_extensions.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js_interop/js_interop.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/js_interop_unsafe/js_interop_unsafe.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2023, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/perfetto_utils.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/common/builtin_clock.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/clock_snapshot.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/interned_data/interned_data.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/profiling/profile_common.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/profiling/profile_packet.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/trace.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/trace_packet.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/debug_annotation.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/process_descriptor.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/thread_descriptor.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/track_descriptor.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/track_event.pbzero.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/protos/tools/compile_perfetto_protos.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/vm/perfetto_utils.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/common/builtin_clock.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/clock_snapshot.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/interned_data/interned_data.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/profiling/profile_common.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/profiling/profile_packet.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/trace.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/trace_packet.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/debug_annotation.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/process_descriptor.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/thread_descriptor.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/track_descriptor.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/perfetto/trace/track_event/track_event.pbzero.h
FILE: ../../../flutter/third_party/dart/runtime/vm/protos/tools/compile_perfetto_protos.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2023, the Dart project authors. Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/icu.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/icu.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/ifaddrs.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/ifaddrs.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/native_assets_api_impl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/thread.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/uri.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/uri.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/include/bin/native_assets_api.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/lib/concurrent.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/no_tsan.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/no_tsan.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/synchronization.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/synchronization_absl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/synchronization_posix.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/synchronization_win.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/threads.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/flag_fuzzer.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/profiling/bin/convert_allocation_profile.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/profiling/bin/set_uprobe.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/elf_utils.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/perf/perf_data.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/symbols.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bytecode_reader.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/bytecode_reader.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_kbc.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_kbc.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/dart_calling_conventions.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/dart_calling_conventions.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_kbc.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/constants_kbc.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/incremental_compactor.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/heap/incremental_compactor.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/interpreter.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/interpreter.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/os.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_memory.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/stack_frame_kbc.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/custom_hash_set.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_only.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/identity_hash_set.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/linked_hash_set.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/dart2js_only.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/synced/array_flags.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/synced/invocation_mirror_constants.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/date_time_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/synced/async_status_codes.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/resident_compiler_utils.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/concurrent_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/compact_hash.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/ffi_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_helper_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/js_helper_patch.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/concurrent/concurrent.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/icu.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/icu.h
FILE: ../../../flutter/third_party/dart/runtime/bin/ifaddrs.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/ifaddrs.h
FILE: ../../../flutter/third_party/dart/runtime/bin/native_assets_api_impl.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/thread.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/uri.cc
FILE: ../../../flutter/third_party/dart/runtime/bin/uri.h
FILE: ../../../flutter/third_party/dart/runtime/include/bin/native_assets_api.h
FILE: ../../../flutter/third_party/dart/runtime/lib/concurrent.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/no_tsan.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/no_tsan.h
FILE: ../../../flutter/third_party/dart/runtime/platform/synchronization.h
FILE: ../../../flutter/third_party/dart/runtime/platform/synchronization_absl.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/synchronization_posix.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/synchronization_win.cc
FILE: ../../../flutter/third_party/dart/runtime/platform/threads.h
FILE: ../../../flutter/third_party/dart/runtime/tools/dartfuzz/flag_fuzzer.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/bin/convert_allocation_profile.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/bin/set_uprobe.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/elf_utils.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/perf/perf_data.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/symbols.dart
FILE: ../../../flutter/third_party/dart/runtime/vm/bytecode_reader.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/bytecode_reader.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_kbc.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/assembler/disassembler_kbc.h
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/dart_calling_conventions.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/compiler/backend/dart_calling_conventions.h
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_kbc.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/constants_kbc.h
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/incremental_compactor.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/heap/incremental_compactor.h
FILE: ../../../flutter/third_party/dart/runtime/vm/interpreter.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/interpreter.h
FILE: ../../../flutter/third_party/dart/runtime/vm/os.cc
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_memory.h
FILE: ../../../flutter/third_party/dart/runtime/vm/stack_frame_kbc.h
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/custom_hash_set.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/ddc_only.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/identity_hash_set.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_dev_runtime/private/linked_hash_set.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/dart2js_only.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/synced/array_flags.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_runtime/lib/synced/invocation_mirror_constants.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/date_time_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/js_shared/lib/synced/async_status_codes.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/bin/resident_compiler_utils.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/vm/lib/concurrent_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/compact_hash.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/ffi_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/js_helper_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm_js_compatibility/lib/js_helper_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/concurrent/concurrent.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2024, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/developer/http_profiling.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/sdk/lib/developer/http_profiling.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2024, the Dart project authors. Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/runtime/bin/dart_api_win.c + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/engine/dart_engine_impl.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/engine/engine.cc + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/engine/engine.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/engine/include/dart_engine.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/platform/lockers.h + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/tools/generate_dart_api_win_c.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/runtime/vm/simulator_arm64_trampolines.S + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/dynamic_module.dart + ../../../flutter/third_party/dart/LICENSE
ORIGIN: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/function_patch.dart + ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/bin/dart_api_win.c
FILE: ../../../flutter/third_party/dart/runtime/engine/dart_engine_impl.cc
FILE: ../../../flutter/third_party/dart/runtime/engine/engine.cc
FILE: ../../../flutter/third_party/dart/runtime/engine/engine.h
FILE: ../../../flutter/third_party/dart/runtime/engine/include/dart_engine.h
FILE: ../../../flutter/third_party/dart/runtime/platform/lockers.h
FILE: ../../../flutter/third_party/dart/runtime/tools/generate_dart_api_win_c.dart
FILE: ../../../flutter/third_party/dart/runtime/vm/simulator_arm64_trampolines.S
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/dynamic_module.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/function_patch.dart
----------------------------------------------------------------------------------------------------
Copyright (c) 2025, the Dart project authors.  Please see the AUTHORS file
for details. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: double-conversion
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/cached-powers.cc
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/cached-powers.cc
----------------------------------------------------------------------------------------------------
Copyright 2006-2008 the V8 project authors. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google Inc. nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: double-conversion
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/bignum-dtoa.cc
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/bignum-dtoa.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/bignum.cc
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/bignum.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/cached-powers.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/diy-fp.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/double-to-string.cc
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/fast-dtoa.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/fixed-dtoa.cc
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/fixed-dtoa.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/string-to-double.cc
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/strtod.cc
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/strtod.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/utils.h
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/bignum-dtoa.cc
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/bignum-dtoa.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/bignum.cc
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/bignum.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/cached-powers.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/diy-fp.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/double-to-string.cc
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/fast-dtoa.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/fixed-dtoa.cc
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/fixed-dtoa.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/string-to-double.cc
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/strtod.cc
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/strtod.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/utils.h
----------------------------------------------------------------------------------------------------
Copyright 2010 the V8 project authors. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google Inc. nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: double-conversion
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/double-conversion.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/double-to-string.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/fast-dtoa.cc
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/ieee.h
ORIGIN: ../../../flutter/third_party/dart/third_party/double-conversion/src/string-to-double.h
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/double-conversion.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/double-to-string.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/fast-dtoa.cc
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/ieee.h
FILE: ../../../flutter/third_party/dart/third_party/double-conversion/src/string-to-double.h
----------------------------------------------------------------------------------------------------
Copyright 2012 the V8 project authors. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google Inc. nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: dart
ORIGIN: ../../../flutter/third_party/dart/LICENSE
TYPE: LicenseType.bsd
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/elements.dart
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/img/chromium_icon.png
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/img/dart_icon.png
FILE: ../../../flutter/third_party/dart/runtime/observatory/lib/src/elements/img/isolate_icon.png
FILE: ../../../flutter/third_party/dart/runtime/observatory/web/favicon.ico
FILE: ../../../flutter/third_party/dart/runtime/observatory/web/index.html
FILE: ../../../flutter/third_party/dart/runtime/observatory/web/third_party/trace_viewer_full.html
FILE: ../../../flutter/third_party/dart/runtime/observatory/web/timeline.html
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/dart.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/dartaotruntime.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/dartaotruntime_product.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/gen_snapshot.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/gen_snapshot_product.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_futures_aot.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_futures_kernel.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_main_aot.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_main_kernel.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_timer_aot.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_timer_async_aot.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_timer_async_kernel.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_timer_kernel.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_two_programs_aot.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_two_programs_kernel.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/entitlements/run_vm_tests.plist
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/pprof/generated/profile.pb.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/pprof/generated/profile.pbenum.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/pprof/generated/profile.pbjson.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/profiling/lib/src/pprof/generated/profile.pbserver.dart
FILE: ../../../flutter/third_party/dart/runtime/tools/wiki/styles/style.scss
FILE: ../../../flutter/third_party/dart/runtime/tools/wiki/templates/includes/auto-refresh.html
FILE: ../../../flutter/third_party/dart/runtime/tools/wiki/templates/page.html
FILE: ../../../flutter/third_party/dart/sdk.code-workspace
FILE: ../../../flutter/third_party/dart/sdk/lib/_internal/wasm/lib/async_patch.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/html_common/conversions_dart2js.dart
FILE: ../../../flutter/third_party/dart/sdk/lib/html/html_common/html_common.dart
----------------------------------------------------------------------------------------------------
Copyright 2012, the Dart project authors.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google LLC nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
====================================================================================================

====================================================================================================
LIBRARY: fallback_root_certificates
ORIGIN: ../../../flutter/third_party/dart/third_party/fallback_root_certificates/LICENSE
TYPE: LicenseType.mpl
FILE: ../../../flutter/third_party/dart/third_party/fallback_root_certificates/root_certificates.cc
----------------------------------------------------------------------------------------------------
Mozilla Public License Version 2.0
==================================

1. Definitions
--------------

1.1. "Contributor"
    means each individual or legal entity that creates, contributes to
    the creation of, or owns Covered Software.

1.2. "Contributor Version"
    means the combination of the Contributions of others (if any) used
    by a Contributor and that particular Contributor's Contribution.

1.3. "Contribution"
    means Covered Software of a particular Contributor.

1.4. "Covered Software"
    means Source Code Form to which the initial Contributor has attached
    the notice in Exhibit A, the Executable Form of such Source Code
    Form, and Modifications of such Source Code Form, in each case
    including portions thereof.

1.5. "Incompatible With Secondary Licenses"
    means

    (a) that the initial Contributor has attached the notice described
        in Exhibit B to the Covered Software; or

    (b) that the Covered Software was made available under the terms of
        version 1.1 or earlier of the License, but not also under the
        terms of a Secondary License.

1.6. "Executable Form"
    means any form of the work other than Source Code Form.

1.7. "Larger Work"
    means a work that combines Covered Software with other material, in
    a separate file or files, that is not Covered Software.

1.8. "License"
    means this document.

1.9. "Licensable"
    means having the right to grant, to the maximum extent possible,
    whether at the time of the initial grant or subsequently, any and
    all of the rights conveyed by this License.

1.10. "Modifications"
    means any of the following:

    (a) any file in Source Code Form that results from an addition to,
        deletion from, or modification of the contents of Covered
        Software; or

    (b) any new file in Source Code Form that contains any Covered
        Software.

1.11. "Patent Claims" of a Contributor
    means any patent claim(s), including without limitation, method,
    process, and apparatus claims, in any patent Licensable by such
    Contributor that would be infringed, but for the grant of the
    License, by the making, using, selling, offering for sale, having
    made, import, or transfer of either its Contributions or its
    Contributor Version.

1.12. "Secondary License"
    means either the GNU General Public License, Version 2.0, the GNU
    Lesser General Public License, Version 2.1, the GNU Affero General
    Public License, Version 3.0, or any later versions of those
    licenses.

1.13. "Source Code Form"
    means the form of the work preferred for making modifications.

1.14. "You" (or "Your")
    means an individual or a legal entity exercising rights under this
    License. For legal entities, "You" includes any entity that
    controls, is controlled by, or is under common control with You. For
    purposes of this definition, "control" means (a) the power, direct
    or indirect, to cause the direction or management of such entity,
    whether by contract or otherwise, or (b) ownership of more than
    fifty percent (50%) of the outstanding shares or beneficial
    ownership of such entity.

2. License Grants and Conditions
--------------------------------

2.1. Grants

Each Contributor hereby grants You a world-wide, royalty-free,
non-exclusive license:

(a) under intellectual property rights (other than patent or trademark)
    Licensable by such Contributor to use, reproduce, make available,
    modify, display, perform, distribute, and otherwise exploit its
    Contributions, either on an unmodified basis, with Modifications, or
    as part of a Larger Work; and

(b) under Patent Claims of such Contributor to make, use, sell, offer
    for sale, have made, import, and otherwise transfer either its
    Contributions or its Contributor Version.

2.2. Effective Date

The licenses granted in Section 2.1 with respect to any Contribution
become effective for each Contribution on the date the Contributor first
distributes such Contribution.

2.3. Limitations on Grant Scope

The licenses granted in this Section 2 are the only rights granted under
this License. No additional rights or licenses will be implied from the
distribution or licensing of Covered Software under this License.
Notwithstanding Section 2.1(b) above, no patent license is granted by a
Contributor:

(a) for any code that a Contributor has removed from Covered Software;
    or

(b) for infringements caused by: (i) Your and any other third party's
    modifications of Covered Software, or (ii) the combination of its
    Contributions with other software (except as part of its Contributor
    Version); or

(c) under Patent Claims infringed by Covered Software in the absence of
    its Contributions.

This License does not grant any rights in the trademarks, service marks,
or logos of any Contributor (except as may be necessary to comply with
the notice requirements in Section 3.4).

2.4. Subsequent Licenses

No Contributor makes additional grants as a result of Your choice to
distribute the Covered Software under a subsequent version of this
License (see Section 10.2) or under the terms of a Secondary License (if
permitted under the terms of Section 3.3).

2.5. Representation

Each Contributor represents that the Contributor believes its
Contributions are its original creation(s) or it has sufficient rights
to grant the rights to its Contributions conveyed by this License.

2.6. Fair Use

This License is not intended to limit any rights You have under
applicable copyright doctrines of fair use, fair dealing, or other
equivalents.

2.7. Conditions

Sections 3.1, 3.2, 3.3, and 3.4 are conditions of the licenses granted
in Section 2.1.

3. Responsibilities
-------------------

3.1. Distribution of Source Form

All distribution of Covered Software in Source Code Form, including any
Modifications that You create or to which You contribute, must be under
the terms of this License. You must inform recipients that the Source
Code Form of the Covered Software is governed by the terms of this
License, and how they can obtain a copy of this License. You may not
attempt to alter or restrict the recipients' rights in the Source Code
Form.

3.2. Distribution of Executable Form

If You distribute Covered Software in Executable Form then:

(a) such Covered Software must also be made available in Source Code
    Form, as described in Section 3.1, and You must inform recipients of
    the Executable Form how they can obtain a copy of such Source Code
    Form by reasonable means in a timely manner, at a charge no more
    than the cost of distribution to the recipient; and

(b) You may distribute such Executable Form under the terms of this
    License, or sublicense it under different terms, provided that the
    license for the Executable Form does not attempt to limit or alter
    the recipients' rights in the Source Code Form under this License.

3.3. Distribution of a Larger Work

You may create and distribute a Larger Work under terms of Your choice,
provided that You also comply with the requirements of this License for
the Covered Software. If the Larger Work is a combination of Covered
Software with a work governed by one or more Secondary Licenses, and the
Covered Software is not Incompatible With Secondary Licenses, this
License permits You to additionally distribute such Covered Software
under the terms of such Secondary License(s), so that the recipient of
the Larger Work may, at their option, further distribute the Covered
Software under the terms of either this License or such Secondary
License(s).

3.4. Notices

You may not remove or alter the substance of any license notices
(including copyright notices, patent notices, disclaimers of warranty,
or limitations of liability) contained within the Source Code Form of
the Covered Software, except that You may alter any license notices to
the extent required to remedy known factual inaccuracies.

3.5. Application of Additional Terms

You may choose to offer, and to charge a fee for, warranty, support,
indemnity or liability obligations to one or more recipients of Covered
Software. However, You may do so only on Your own behalf, and not on
behalf of any Contributor. You must make it absolutely clear that any
such warranty, support, indemnity, or liability obligation is offered by
You alone, and You hereby agree to indemnify every Contributor for any
liability incurred by such Contributor as a result of warranty, support,
indemnity or liability terms You offer. You may include additional
disclaimers of warranty and limitations of liability specific to any
jurisdiction.

4. Inability to Comply Due to Statute or Regulation
---------------------------------------------------

If it is impossible for You to comply with any of the terms of this
License with respect to some or all of the Covered Software due to
statute, judicial order, or regulation then You must: (a) comply with
the terms of this License to the maximum extent possible; and (b)
describe the limitations and the code they affect. Such description must
be placed in a text file included with all distributions of the Covered
Software under this License. Except to the extent prohibited by statute
or regulation, such description must be sufficiently detailed for a
recipient of ordinary skill to be able to understand it.

5. Termination
--------------

5.1. The rights granted under this License will terminate automatically
if You fail to comply with any of its terms. However, if You become
compliant, then the rights granted under this License from a particular
Contributor are reinstated (a) provisionally, unless and until such
Contributor explicitly and finally terminates Your grants, and (b) on an
ongoing basis, if such Contributor fails to notify You of the
non-compliance by some reasonable means prior to 60 days after You have
come back into compliance. Moreover, Your grants from a particular
Contributor are reinstated on an ongoing basis if such Contributor
notifies You of the non-compliance by some reasonable means, this is the
first time You have received notice of non-compliance with this License
from such Contributor, and You become compliant prior to 30 days after
Your receipt of the notice.

5.2. If You initiate litigation against any entity by asserting a patent
infringement claim (excluding declaratory judgment actions,
counter-claims, and cross-claims) alleging that a Contributor Version
directly or indirectly infringes any patent, then the rights granted to
You by any and all Contributors for the Covered Software under Section
2.1 of this License shall terminate.

5.3. In the event of termination under Sections 5.1 or 5.2 above, all
end user license agreements (excluding distributors and resellers) which
have been validly granted by You or Your distributors under this License
prior to termination shall survive termination.

************************************************************************
*                                                                      *
*  6. Disclaimer of Warranty                                           *
*  -------------------------                                           *
*                                                                      *
*  Covered Software is provided under this License on an "as is"       *
*  basis, without warranty of any kind, either expressed, implied, or  *
*  statutory, including, without limitation, warranties that the       *
*  Covered Software is free of defects, merchantable, fit for a        *
*  particular purpose or non-infringing. The entire risk as to the     *
*  quality and performance of the Covered Software is with You.        *
*  Should any Covered Software prove defective in any respect, You     *
*  (not any Contributor) assume the cost of any necessary servicing,   *
*  repair, or correction. This disclaimer of warranty constitutes an   *
*  essential part of this License. No use of any Covered Software is   *
*  authorized under this License except under this disclaimer.         *
*                                                                      *
************************************************************************

************************************************************************
*                                                                      *
*  7. Limitation of Liability                                          *
*  --------------------------                                          *
*                                                                      *
*  Under no circumstances and under no legal theory, whether tort      *
*  (including negligence), contract, or otherwise, shall any           *
*  Contributor, or anyone who distributes Covered Software as          *
*  permitted above, be liable to You for any direct, indirect,         *
*  special, incidental, or consequential damages of any character      *
*  including, without limitation, damages for lost profits, loss of    *
*  goodwill, work stoppage, computer failure or malfunction, or any    *
*  and all other commercial damages or losses, even if such party      *
*  shall have been informed of the possibility of such damages. This   *
*  limitation of liability shall not apply to liability for death or   *
*  personal injury resulting from such party's negligence to the       *
*  extent applicable law prohibits such limitation. Some               *
*  jurisdictions do not allow the exclusion or limitation of           *
*  incidental or consequential damages, so this exclusion and          *
*  limitation may not apply to You.                                    *
*                                                                      *
************************************************************************

8. Litigation
-------------

Any litigation relating to this License may be brought only in the
courts of a jurisdiction where the defendant maintains its principal
place of business and such litigation shall be governed by laws of that
jurisdiction, without reference to its conflict-of-law provisions.
Nothing in this Section shall prevent a party's ability to bring
cross-claims or counter-claims.

9. Miscellaneous
----------------

This License represents the complete agreement concerning the subject
matter hereof. If any provision of this License is held to be
unenforceable, such provision shall be reformed only to the extent
necessary to make it enforceable. Any law or regulation which provides
that the language of a contract shall be construed against the drafter
shall not be used to construe this License against a Contributor.

10. Versions of the License
---------------------------

10.1. New Versions

Mozilla Foundation is the license steward. Except as provided in Section
10.3, no one other than the license steward has the right to modify or
publish new versions of this License. Each version will be given a
distinguishing version number.

10.2. Effect of New Versions

You may distribute the Covered Software under the terms of the version
of the License under which You originally received the Covered Software,
or under the terms of any subsequent version published by the license
steward.

10.3. Modified Versions

If you create software not governed by this License, and you want to
create a new license for such software, you may create and use a
modified version of this License if you rename the license and remove
any references to the name of the license steward (except to note that
such modified license differs from this License).

10.4. Distributing Source Code Form that is Incompatible With Secondary
Licenses

If You choose to distribute Source Code Form that is Incompatible With
Secondary Licenses under the terms of this version of the License, the
notice described in Exhibit B of this License must be attached.

Exhibit A - Source Code Form License Notice
-------------------------------------------

  This Source Code Form is subject to the terms of the Mozilla Public
  License, v. 2.0. If a copy of the MPL was not distributed with this
  file, You can obtain one at http://mozilla.org/MPL/2.0/.

If it is not possible or desirable to put the notice in a particular
file, then You may include the notice in a location (such as a LICENSE
file in a relevant directory) where a recipient would be likely to look
for such a notice.

You may add additional accurate notices of copyright ownership.

Exhibit B - "Incompatible With Secondary Licenses" Notice
---------------------------------------------------------

  This Source Code Form is "Incompatible With Secondary Licenses", as
  defined by the Mozilla Public License, v. 2.0.

You may obtain a copy of this library's Source Code Form from: https://dart.googlesource.com/sdk/+/e7f2f0556e3e57acb60749467e54f9a44b2bfc76
/third_party/fallback_root_certificates/

====================================================================================================

Total license count: 28
