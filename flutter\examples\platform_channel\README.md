# Example of calling platform services from Flutter

This project demonstrates how to connect a Flutter app to platform-specific services.

You can read more about
[accessing platform and third-party services in Flutter](https://flutter.dev/to/platform-channels/).

## iOS
You can use the commands `flutter build` and `flutter run` from the app's root
directory to build/run the app or you can open `ios/Runner.xcworkspace` in Xcode
and build/run the project as usual.

## Android

You can use the commands `flutter build` and `flutter run` from the app's root
directory to build/run the app or to build with Android Studio, open the
`android` folder in Android Studio and build the project as usual.

## Windows
You can use the commands `flutter build` and `flutter run` from the app's root
directory to build/run the app or you can build once then open
`build\windows\platform_channel.sln` in Visual Studio to build and run.
