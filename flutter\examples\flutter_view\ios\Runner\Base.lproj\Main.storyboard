<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="15705" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="FnB-1o-m6P">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="15706"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Main View Controller-->
        <scene sceneID="nCd-Pe-nwC">
            <objects>
                <viewController id="FnB-1o-m6P" customClass="MainViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="MCZ-qn-U8U"/>
                        <viewControllerLayoutGuide type="bottom" id="T9c-61-ZEO"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Nt5-YR-XLZ">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" misplaced="YES" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Jdh-p2-Syc">
                                <rect key="frame" x="0.0" y="6" width="375" height="667"/>
                                <subviews>
                                    <containerView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="G3K-Ct-amG">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="333.5"/>
                                        <connections>
                                            <segue destination="yjl-pP-dW2" kind="embed" identifier="FlutterViewControllerSegue" id="zVC-ur-hSh"/>
                                        </connections>
                                    </containerView>
                                    <containerView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EWt-tU-lT8">
                                        <rect key="frame" x="0.0" y="333.5" width="375" height="333.5"/>
                                        <connections>
                                            <segue destination="g6V-0q-Qmt" kind="embed" identifier="NativeViewControllerSegue" id="TiA-wb-9xc"/>
                                        </connections>
                                    </containerView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="EWt-tU-lT8" firstAttribute="height" secondItem="G3K-Ct-amG" secondAttribute="height" id="OWV-Bz-aZ9"/>
                                    <constraint firstItem="EWt-tU-lT8" firstAttribute="width" secondItem="G3K-Ct-amG" secondAttribute="width" id="Vni-bz-bSt"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="Jdh-p2-Syc" firstAttribute="leading" secondItem="Nt5-YR-XLZ" secondAttribute="leading" id="736-ju-cs1"/>
                            <constraint firstAttribute="bottom" secondItem="Jdh-p2-Syc" secondAttribute="bottom" id="NJj-PW-ivg"/>
                            <constraint firstAttribute="trailing" secondItem="Jdh-p2-Syc" secondAttribute="trailing" id="Rlp-q3-1xR"/>
                            <constraint firstItem="Jdh-p2-Syc" firstAttribute="top" secondItem="Nt5-YR-XLZ" secondAttribute="top" id="XpW-hj-pI4"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="YK3-cu-zew" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1858" y="211"/>
        </scene>
        <!--Flutter View Controller-->
        <scene sceneID="QiU-td-PNw">
            <objects>
                <viewController id="yjl-pP-dW2" customClass="FlutterViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="GZT-2O-K6G"/>
                        <viewControllerLayoutGuide type="bottom" id="meP-7L-4EO"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="jhP-0W-2HS">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="333.5"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="nvJ-Dz-mOO" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1060" y="76"/>
        </scene>
        <!--Native View Controller-->
        <scene sceneID="W4R-Y9-Lec">
            <objects>
                <viewController id="g6V-0q-Qmt" customClass="NativeViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="wrm-99-E0t"/>
                        <viewControllerLayoutGuide type="bottom" id="H72-eC-cuN"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Lrt-2J-Mko" userLabel="ParentView">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="333.5"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="zVI-Xh-iNx">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="333.5"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NLS-lx-anZ" userLabel="Top">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="263.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Flutter button tapped 0 times." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PJ2-AA-Riy" userLabel="IncrementLabel">
                                                <rect key="frame" x="73.5" y="121.5" width="228.5" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="PJ2-AA-Riy" firstAttribute="centerX" secondItem="NLS-lx-anZ" secondAttribute="centerX" id="H1S-ZO-f6z"/>
                                            <constraint firstItem="PJ2-AA-Riy" firstAttribute="centerY" secondItem="NLS-lx-anZ" secondAttribute="centerY" id="ZSn-Gi-wdO"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" restorationIdentifier="Bottom" translatesAutoresizingMaskIntoConstraints="NO" id="Qxj-hW-CeP" userLabel="Bottom">
                                        <rect key="frame" x="0.0" y="263.5" width="375" height="70"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="iOS" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="K0h-kv-J7E">
                                                <rect key="frame" x="20" y="14" width="47" height="36"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="30"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Jfa-Lk-nDI">
                                                <rect key="frame" x="300" y="-5" width="55" height="55"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="55" id="QYW-fS-Hcx"/>
                                                    <constraint firstAttribute="width" constant="55" id="zeJ-gS-6zj"/>
                                                </constraints>
                                                <color key="tintColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <state key="normal" image="ic_add.png"/>
                                                <state key="disabled" image="ic_add.png"/>
                                                <state key="selected" image="ic_add.png"/>
                                                <state key="highlighted" image="ic_add.png"/>
                                                <connections>
                                                    <action selector="handleIncrement:" destination="g6V-0q-Qmt" eventType="touchUpInside" id="3ie-8K-E0v"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Jfa-Lk-nDI" secondAttribute="bottom" constant="20" symbolic="YES" id="Dl7-lD-YH3"/>
                                            <constraint firstAttribute="height" constant="70" id="Hgs-wm-FFX"/>
                                            <constraint firstAttribute="bottom" secondItem="K0h-kv-J7E" secondAttribute="bottom" constant="20" symbolic="YES" id="Iem-H7-VWN"/>
                                            <constraint firstItem="K0h-kv-J7E" firstAttribute="leading" secondItem="Qxj-hW-CeP" secondAttribute="leading" constant="20" symbolic="YES" id="Vtt-ur-fpR"/>
                                            <constraint firstAttribute="trailing" secondItem="Jfa-Lk-nDI" secondAttribute="trailing" constant="20" symbolic="YES" id="yCg-We-tBg"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" red="0.66666666669999997" green="0.66666666669999997" blue="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="zVI-Xh-iNx" firstAttribute="leading" secondItem="Lrt-2J-Mko" secondAttribute="leading" id="HoL-50-e9R"/>
                            <constraint firstAttribute="trailing" secondItem="zVI-Xh-iNx" secondAttribute="trailing" id="QMD-YW-tUG"/>
                            <constraint firstAttribute="bottom" secondItem="zVI-Xh-iNx" secondAttribute="bottom" id="VJM-I0-53q"/>
                            <constraint firstItem="zVI-Xh-iNx" firstAttribute="top" secondItem="Lrt-2J-Mko" secondAttribute="top" id="p1C-xB-mpQ"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="incrementLabel" destination="PJ2-AA-Riy" id="GmJ-fE-2WO"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="YRn-0M-UIv" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1090.4000000000001" y="489.3553223388306"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_add.png" width="24" height="24"/>
    </resources>
</document>
