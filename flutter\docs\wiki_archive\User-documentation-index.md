<!-- Don't add user documentation to the wiki. User documentation belongs on the web site. -->
<!-- We only have these pages for historical reasons. -->

<PERSON>lutter's user-facing documentation should all be on the Flutter web site and the API documentation site:

- https://docs.flutter.dev/
  - [Release notes](https://docs.flutter.dev/release/release-notes)
- https://api.flutter.dev/

Historically, experimental documentation was sometimes first written on this wiki and later moved to the web site. This is
now discouraged but some of the content created in this way still exists and is listed below.

_Contributors: please consider moving this content to the web site or deleting it if it is no longer applicable!_

## Old documentation

## General

<!-- don't add things here; if you have a new feature, it should be documented on the web site not the wiki -->
- [Apple Silicon support](../platforms/desktop/macos/Developing-with-Flutter-on-Apple-Silicon.md)
- [Bad Builds](../releases/Bad-Builds.md)
- [Binding to native code via FFI](https://flutter.dev/docs/development/platform-integration/c-interop)
- [Data-driven Fixes](../contributing/Data-driven-Fixes.md)
- [Flutter CLI crash reporting](https://docs.flutter.dev/reference/crash-reporting)
- [Flutter CLI custom embedder support](../tool/Using-custom-embedders-with-the-Flutter-CLI.md)
- [Flutter Test Fonts](../contributing/testing/Flutter-Test-Fonts.md)
- [Hybrid Composition](../platforms/Hybrid-Composition.md)
- [IntelliJ - Flutter Setup Tips and Tricks](IntelliJ---Flutter-Setup-Tips-and-Tricks.md)
- [JIT release builds](../engine/JIT-Release-Modes.md)
- [Making animated GIFs of Flutter apps](../contributing/issue_hygiene/Making-animated-GIFs-of-Flutter-apps.md)
- [Multi-device debugging in VS Code](Multi-device-debugging-in-VS-Code.md)
- [Obfuscating Dart Code](https://flutter.dev/docs/deployment/obfuscate)
- [Reduce shader compilation jank using SkSL warm up](https://flutter.dev/docs/perf/rendering/shader)
- [Upgrading Flutter projects from using PlatformMessages to using channels](Upgrading-Flutter-projects-from-using-PlatformMessages-to-using-channels.md)
- [Writing Effective Tests](../contributing/testing/Writing-Effective-Tests.md)

### Android
<!-- don't add things here; user documentation belongs on the web site not the wiki -->
- [Android Fast Start](../tool/Fast-Start.md)
- [Android Platform Views](../platforms/android/Android-Platform-Views.md)
- [How Flutter apps are compiled with Gradle for Android](../platforms/android/How-Flutter-apps-are-compiled-with-Gradle-for-Android.md)
- [Hybrid Composition](../platforms/Hybrid-Composition.md#android)
- [Multidex](../platforms/android/Android-Multidex-support.md)
- [Texture Layer Hybrid Composition](../platforms/android/Texture-Layer-Hybrid-Composition.md)

### iOS
<!-- don't add things here; user documentation belongs on the web site not the wiki -->
- [Hybrid Composition iOS](../platforms/Hybrid-Composition.md#ios)

### Web
<!-- don't add things here; user documentation belongs on the web site not the wiki -->
- [Debugging issues on the Web](../platforms/web/Debugging-issues-on-the-Web.md)
- [Running Flutter Driver tests with Web](../contributing/testing/Running-Flutter-Driver-tests-with-Web.md)

### Release notes
<!-- don't add things here; user documentation belongs on the web site not the wiki -->
- [Hotfixes to the Stable Channel](../../CHANGELOG.md)
