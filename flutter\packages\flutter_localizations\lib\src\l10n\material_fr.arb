{"scriptCategory": "English-like", "timeOfDayFormat": "HH:mm", "openAppDrawerTooltip": "<PERSON><PERSON><PERSON><PERSON>r le menu de navigation", "backButtonTooltip": "Retour", "closeButtonTooltip": "<PERSON><PERSON><PERSON>", "deleteButtonTooltip": "<PERSON><PERSON><PERSON><PERSON>", "nextMonthTooltip": "<PERSON><PERSON> suivant", "previousMonthTooltip": "<PERSON><PERSON>", "nextPageTooltip": "<PERSON> suivante", "previousPageTooltip": "<PERSON> p<PERSON>", "firstPageTooltip": "Première page", "lastPageTooltip": "Dernière page", "showMenuTooltip": "<PERSON><PERSON><PERSON><PERSON> le menu", "aboutListTileTitle": "À propos de $applicationName", "licensesPageTitle": "Licences", "pageRowsInfoTitle": "$firstRow – $lastRow sur $rowCount", "pageRowsInfoTitleApproximate": "$firstRow – $lastRow sur environ $rowCount", "rowsPerPageTitle": "<PERSON><PERSON><PERSON> par page :", "tabLabel": "Onglet $tabIndex sur $tabCount", "selectedRowCountTitleZero": "Aucun élément sélectionné", "selectedRowCountTitleOne": "1 élément sélectionné", "selectedRowCountTitleOther": "$selectedRowCount éléments sélectionnés", "cancelButtonLabel": "Annuler", "closeButtonLabel": "<PERSON><PERSON><PERSON>", "continueButtonLabel": "<PERSON><PERSON><PERSON>", "copyButtonLabel": "<PERSON><PERSON><PERSON>", "cutButtonLabel": "Couper", "scanTextButtonLabel": "Scanner du texte", "okButtonLabel": "OK", "pasteButtonLabel": "<PERSON><PERSON>", "selectAllButtonLabel": "<PERSON><PERSON>", "viewLicensesButtonLabel": "Afficher les licences", "anteMeridiemAbbreviation": "AM", "postMeridiemAbbreviation": "PM", "timePickerHourModeAnnouncement": "Sélectionner une heure", "timePickerMinuteModeAnnouncement": "Sélectionner des minutes", "signedInLabel": "Connecté", "hideAccountsLabel": "Masquer les comptes", "showAccountsLabel": "Affiche<PERSON> les comptes", "modalBarrierDismissLabel": "<PERSON><PERSON><PERSON>", "drawerLabel": "Menu de navigation", "popupMenuLabel": "Menu contextuel", "dialogLabel": "Boîte de dialogue", "alertDialogLabel": "<PERSON><PERSON><PERSON>", "searchFieldLabel": "<PERSON><PERSON><PERSON>", "reorderItemToStart": "<PERSON><PERSON><PERSON><PERSON> vers le début", "reorderItemToEnd": "<PERSON><PERSON><PERSON>r vers la fin", "reorderItemUp": "<PERSON><PERSON><PERSON><PERSON> vers le haut", "reorderItemDown": "<PERSON><PERSON><PERSON><PERSON> vers le bas", "reorderItemLeft": "<PERSON><PERSON><PERSON>r vers la gauche", "reorderItemRight": "<PERSON><PERSON><PERSON>r vers la droite", "expandedIconTapHint": "<PERSON><PERSON><PERSON><PERSON>", "collapsedIconTapHint": "Développer", "remainingTextFieldCharacterCountOne": "1 caractère restant", "remainingTextFieldCharacterCountOther": "$remainingCount caractères restants", "refreshIndicatorSemanticLabel": "Actualiser", "moreButtonTooltip": "Plus", "dateSeparator": "/", "dateHelpText": "jj/mm/aaaa", "selectYearSemanticsLabel": "Sélectionner une année", "unspecifiedDate": "Date", "unspecifiedDateRange": "Plage de dates", "dateInputLabel": "Sai<PERSON> une date", "dateRangeStartLabel": "Date de début", "dateRangeEndLabel": "Date de fin", "dateRangeStartDateSemanticLabel": "Date de début : $fullDate", "dateRangeEndDateSemanticLabel": "Date de fin : $fullDate", "invalidDateFormatLabel": "Format non valide.", "invalidDateRangeLabel": "Plage non valide.", "dateOutOfRangeLabel": "<PERSON><PERSON>.", "saveButtonLabel": "Enregistrer", "datePickerHelpText": "Sélectionner une date", "dateRangePickerHelpText": "Sélectionner une plage", "calendarModeButtonLabel": "Passer à l'agenda", "inputDateModeButtonLabel": "Passer à la saisie", "timePickerDialHelpText": "Sélectionner une heure", "timePickerInputHelpText": "<PERSON><PERSON> une heure", "timePickerHourLabel": "<PERSON><PERSON>", "timePickerMinuteLabel": "Minute", "invalidTimeLabel": "Veuillez indiquer une heure valide", "dialModeButtonLabel": "Passer au mode de sélection via le cadran", "inputTimeModeButtonLabel": "Passer au mode de saisie au format texte", "licensesPackageDetailTextZero": "No licenses", "licensesPackageDetailTextOne": "1 licence", "licensesPackageDetailTextOther": "$licenseCount licences", "keyboardKeyAlt": "Alt", "keyboardKeyAltGraph": "Alt Gr", "keyboardKeyBackspace": "Retour arrière", "keyboardKeyCapsLock": "<PERSON><PERSON><PERSON>", "keyboardKeyChannelDown": "<PERSON><PERSON><PERSON>", "keyboardKeyChannelUp": "<PERSON><PERSON><PERSON>", "keyboardKeyControl": "Ctrl", "keyboardKeyDelete": "Suppr", "keyboardKeyEject": "<PERSON><PERSON>er", "keyboardKeyEnd": "Fin", "keyboardKeyEscape": "Échap", "keyboardKeyFn": "Fn", "keyboardKeyHome": "Accueil", "keyboardKeyInsert": "<PERSON><PERSON><PERSON><PERSON>", "keyboardKeyMeta": "<PERSON><PERSON><PERSON>", "keyboardKeyNumLock": "<PERSON><PERSON><PERSON>", "keyboardKeyNumpad1": "Num 1", "keyboardKeyNumpad2": "Num 2", "keyboardKeyNumpad3": "Num 3", "keyboardKeyNumpad4": "Num 4", "keyboardKeyNumpad5": "Num 5", "keyboardKeyNumpad6": "Num 6", "keyboardKeyNumpad7": "Num 7", "keyboardKeyNumpad8": "Num 8", "keyboardKeyNumpad9": "Num 9", "keyboardKeyNumpad0": "Num 0", "keyboardKeyNumpadAdd": "Num +", "keyboardKeyNumpadComma": "<PERSON><PERSON> ,", "keyboardKeyNumpadDecimal": "Num .", "keyboardKeyNumpadDivide": "Num /", "keyboardKeyNumpadEnter": "Num Entrée", "keyboardKeyNumpadEqual": "Num =", "keyboardKeyNumpadMultiply": "Num *", "keyboardKeyNumpadParenLeft": "Num (", "keyboardKeyNumpadParenRight": "Num )", "keyboardKeyNumpadSubtract": "Num -", "keyboardKeyPageDown": "PgSuiv", "keyboardKeyPageUp": "PgPréc", "keyboardKeyPower": "Puissance", "keyboardKeyPowerOff": "<PERSON><PERSON><PERSON>", "keyboardKeyPrintScreen": "Impr. <PERSON><PERSON>ran", "keyboardKeyScrollLock": "<PERSON><PERSON><PERSON><PERSON>", "keyboardKeySelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyboardKeySpace": "Espace", "keyboardKeyMetaMacOs": "Commande", "keyboardKeyMetaWindows": "Win", "menuBarMenuLabel": "Menu de la barre de menu", "currentDateLabel": "<PERSON><PERSON><PERSON>'hui", "scrimLabel": "<PERSON>ond", "bottomSheetLabel": "Bottom sheet", "scrimOnTapHint": "Fermer $modalRouteContentName", "keyboardKeyShift": "Maj", "expansionTileExpandedHint": "appuyez deux fois pour réduire", "expansionTileCollapsedHint": "appuyez deux fois pour développer", "expansionTileExpandedTapHint": "<PERSON><PERSON><PERSON><PERSON>", "expansionTileCollapsedTapHint": "Développer pour en savoir plus", "expandedHint": "Réduit", "collapsedHint": "Développé", "menuDismissLabel": "<PERSON><PERSON><PERSON> le menu", "lookUpButtonLabel": "Recherche visuelle", "searchWebButtonLabel": "Rechercher sur le Web", "shareButtonLabel": "Partager", "clearButtonTooltip": "Efface<PERSON> le texte", "selectedDateLabel": "Sélectionnée"}