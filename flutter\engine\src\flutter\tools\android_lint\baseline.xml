<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.3.0 [11479570] " type="baseline" client="" dependencies="true" name="" variant="all" version="8.3.0 [11479570] ">

    <issue
        id="HardcodedDebugMode"
        message="Avoid hardcoding the debug mode; leaving it out allows debug and release builds to automatically assign one"
        errorLine1="    &lt;application android:label=&quot;Flutter Shell&quot; android:name=&quot;FlutterApplication&quot; android:debuggable=&quot;true&quot;>"
        errorLine2="                                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="../../../flutter/shell/platform/android/AndroidManifest.xml"
            line="13"
            column="82"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view `FlutterView` overrides `onTouchEvent` but not `performClick`"
        errorLine1="  public boolean onTouchEvent(MotionEvent event) {"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="../../../flutter/shell/platform/android/io/flutter/view/FlutterView.java"
            line="474"
            column="18"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view `FlutterView` overrides `onTouchEvent` but not `performClick`"
        errorLine1="  public boolean onTouchEvent(@NonNull MotionEvent event) {"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="../../../flutter/shell/platform/android/io/flutter/embedding/android/FlutterView.java"
            line="896"
            column="18"/>
    </issue>

</issues>
