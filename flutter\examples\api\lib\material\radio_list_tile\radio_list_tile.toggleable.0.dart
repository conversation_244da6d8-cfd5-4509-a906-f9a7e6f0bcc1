// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';

/// Flutter code sample for [RadioListTile.toggleable].

void main() => runApp(const RadioListTileApp());

class RadioListTileApp extends StatelessWidget {
  const RadioListTileApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: const Text('RadioListTile.toggleable Sample')),
        body: const RadioListTileExample(),
      ),
    );
  }
}

class RadioListTileExample extends StatefulWidget {
  const RadioListTileExample({super.key});

  @override
  State<RadioListTileExample> createState() => _RadioListTileExampleState();
}

class _RadioListTileExampleState extends State<RadioListTileExample> {
  int? groupValue;
  static const List<String> selections = <String>[
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView.builder(
        itemBuilder: (BuildContext context, int index) {
          return RadioListTile<int>(
            value: index,
            groupValue: groupValue,
            toggleable: true,
            title: Text(selections[index]),
            onChanged: (int? value) {
              setState(() {
                groupValue = value;
              });
            },
          );
        },
        itemCount: selections.length,
      ),
    );
  }
}
