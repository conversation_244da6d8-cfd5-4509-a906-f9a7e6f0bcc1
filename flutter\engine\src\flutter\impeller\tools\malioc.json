{"flutter/impeller/entity/advanced_blend.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/advanced_blend.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 2, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.53125, 0.53125, 0.21875, 0.125, 0.0, 0.5, 0.5], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.484375, 0.484375, 0.203125, 0.0625, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [0.53125, 0.53125, 0.296875, 0.125, 0.0, 0.5, 0.5]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 12, "work_registers_used": 25}}}}, "flutter/impeller/entity/advanced_blend.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/advanced_blend.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 30, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.03125, 0.03125, 0.03125, 0.0, 4.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.03125, 0.03125, 0.03125, 0.0, 4.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.03125, 0.03125, 0.03125, 0.0, 4.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 10}}}}, "flutter/impeller/entity/border_mask_blur.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/border_mask_blur.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 88, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.824999988079071, 0.824999988079071, 0.234375, 0.25, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.824999988079071, 0.824999988079071, 0.234375, 0.25, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [0.824999988079071, 0.824999988079071, 0.234375, 0.25, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 16, "work_registers_used": 16}}}}, "flutter/impeller/entity/clip.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/clip.frag.vkspv", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.015625, 0.0, 0.015625, 0.0, 0.0, 0.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.015625, 0.0, 0.015625, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.015625, 0.0, 0.015625, 0.0, 0.0, 0.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 0, "work_registers_used": 0}}}}, "flutter/impeller/entity/clip.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/clip.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.015625, 0.0625, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.015625, 0.0625, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.015625, 0.0625, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 24, "work_registers_used": 32}}}}, "flutter/impeller/entity/color_matrix_color_filter.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/color_matrix_color_filter.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.28125, 0.28125, 0.0625, 0.0625, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.28125, 0.28125, 0.0625, 0.0625, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [0.28125, 0.28125, 0.0625, 0.0625, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 16, "work_registers_used": 9}}}}, "flutter/impeller/entity/conical_gradient_fill_conical.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/conical_gradient_fill_conical.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.675000011920929, 0.46875, 0.675000011920929, 0.1875, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.265625, 0.203125, 0.265625, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.862500011920929, 0.59375, 0.862500011920929, 0.3125, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 30, "work_registers_used": 11}}}}, "flutter/impeller/entity/conical_gradient_fill_radial.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/conical_gradient_fill_radial.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.328125, 0.328125, 0.296875, 0.125, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.234375, 0.234375, 0.203125, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.421875, 0.359375, 0.421875, 0.125, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 12}}}}, "flutter/impeller/entity/conical_gradient_fill_strip.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/conical_gradient_fill_strip.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.421875, 0.328125, 0.421875, 0.125, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.109375, 0.0625, 0.109375, 0.0, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.546875, 0.359375, 0.546875, 0.125, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 11}}}}, "flutter/impeller/entity/conical_gradient_fill_strip_radial.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/conical_gradient_fill_strip_radial.frag.vkspv", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.03125, 0.0, 0.03125, 0.0, 0.0, 0.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.03125, 0.0, 0.03125, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.03125, 0.0, 0.03125, 0.0, 0.0, 0.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 0, "work_registers_used": 3}}}}, "flutter/impeller/entity/conical_gradient_ssbo_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/conical_gradient_ssbo_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_sfu"], "shortest_path_cycles": [0.625, 0.359375, 0.453125, 0.625, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [1.15625, 0.78125, 1.15625, 0.8125, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 34, "work_registers_used": 17}}}}, "flutter/impeller/entity/conical_gradient_uniform_fill_conical.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/conical_gradient_uniform_fill_conical.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.375, 0.34375, 0.375, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [1.2625000476837158, 0.75, 1.2625000476837158, 0.4375, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 28, "work_registers_used": 19}}}}, "flutter/impeller/entity/conical_gradient_uniform_fill_radial.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/conical_gradient_uniform_fill_radial.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma", "varying"], "shortest_path_cycles": [0.25, 0.25, 0.21875, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.862500011920929, 0.5, 0.862500011920929, 0.3125, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 18, "work_registers_used": 19}}}}, "flutter/impeller/entity/conical_gradient_uniform_fill_strip.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/conical_gradient_uniform_fill_strip.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.234375, 0.234375, 0.21875, 0.0625, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.9375, 0.515625, 0.9375, 0.25, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 18, "work_registers_used": 19}}}}, "flutter/impeller/entity/conical_gradient_uniform_fill_strip_radial.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/conical_gradient_uniform_fill_strip_radial.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 5}}}}, "flutter/impeller/entity/fast_gradient.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/fast_gradient.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 85, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_sfu"], "longest_path_cycles": [0.5, 0.109375, 0.109375, 0.5, 0.0, 0.25, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_sfu"], "shortest_path_cycles": [0.5, 0.109375, 0.109375, 0.5, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_sfu"], "total_cycles": [0.5, 0.109375, 0.109375, 0.5, 0.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 11}}}}, "flutter/impeller/entity/fast_gradient.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/fast_gradient.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 28, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 7}}}}, "flutter/impeller/entity/filter_position.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/filter_position.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 30, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 8}}}}, "flutter/impeller/entity/filter_position_uv.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/filter_position_uv.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 30, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 8}}}}, "flutter/impeller/entity/gaussian.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gaussian.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.125, 0.0, 0.125, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.265625, 0.09375, 0.265625, 0.0, 1.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 13}}}}, "flutter/impeller/entity/gles/advanced_blend.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/advanced_blend.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 2, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.53125, 0.53125, 0.265625, 0.125, 0.0, 0.5, 0.5], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.484375, 0.484375, 0.21875, 0.0625, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [0.53125, 0.53125, 0.34375, 0.125, 0.0, 0.5, 0.5]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 14, "work_registers_used": 23}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/advanced_blend.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [3.9600000381469727, 2.0, 2.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [3.299999952316284, 1.0, 1.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [4.666666507720947, 2.0, 2.0]}, "thread_occupancy": 100, "uniform_registers_used": 2, "work_registers_used": 4}}}}, "flutter/impeller/entity/gles/advanced_blend.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/advanced_blend.vert.gles", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.03125, 0.03125, 0.03125, 0.0, 4.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.03125, 0.03125, 0.03125, 0.0, 4.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.03125, 0.03125, 0.03125, 0.0, 4.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 10}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/advanced_blend.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [3.299999952316284, 7.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [3.299999952316284, 7.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [3.3333332538604736, 7.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/border_mask_blur.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/border_mask_blur.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 81, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.9375, 0.9375, 0.296875, 0.25, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.9375, 0.9375, 0.265625, 0.25, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [0.9375, 0.9375, 0.296875, 0.25, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 29}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/border_mask_blur.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [8.579999923706055, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [8.579999923706055, 1.0, 1.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [9.0, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/clip.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/clip.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.015625, 0.0, 0.015625, 0.0, 0.0, 0.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.015625, 0.0, 0.015625, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.015625, 0.0, 0.015625, 0.0, 0.0, 0.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 0, "work_registers_used": 1}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/clip.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [1.0, 0.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [1.0, 0.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [0.6666666865348816, 0.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 0, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/clip.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/clip.vert.gles", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.15625, 0.15625, 0.0, 0.0625, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.15625, 0.15625, 0.0, 0.0625, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.15625, 0.15625, 0.0, 0.0625, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 18, "work_registers_used": 32}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/clip.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.640000104904175, 3.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.640000104904175, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [2.6666667461395264, 3.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/color_matrix_color_filter.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/color_matrix_color_filter.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.28125, 0.28125, 0.09375, 0.0625, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.28125, 0.28125, 0.0625, 0.0625, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [0.28125, 0.28125, 0.09375, 0.0625, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 14, "work_registers_used": 21}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/color_matrix_color_filter.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [2.9700000286102295, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.9700000286102295, 1.0, 1.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [3.3333332538604736, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 3, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/conical_gradient_fill_conical.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/conical_gradient_fill_conical.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.78125, 0.46875, 0.78125, 0.25, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.265625, 0.203125, 0.265625, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.96875, 0.59375, 0.96875, 0.375, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 32, "work_registers_used": 20}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/conical_gradient_fill_conical.frag.gles", "has_uniform_computation": true, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [9.899999618530273, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [3.630000114440918, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [13.0, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/conical_gradient_fill_radial.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/conical_gradient_fill_radial.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.40625, 0.328125, 0.40625, 0.1875, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt", "varying"], "shortest_path_cycles": [0.25, 0.234375, 0.25, 0.1875, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.5625, 0.359375, 0.5625, 0.1875, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 20}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/conical_gradient_fill_radial.frag.gles", "has_uniform_computation": true, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [7.260000228881836, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.640000104904175, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [8.0, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 7, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/conical_gradient_fill_strip.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/conical_gradient_fill_strip.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.53125, 0.328125, 0.53125, 0.1875, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.109375, 0.0625, 0.109375, 0.0, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.65625, 0.359375, 0.65625, 0.1875, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 20}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/conical_gradient_fill_strip.frag.gles", "has_uniform_computation": true, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [8.25, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.640000104904175, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [9.666666984558105, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 7, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/conical_gradient_fill_strip_radial.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/conical_gradient_fill_strip_radial.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.03125, 0.0, 0.03125, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 0, "work_registers_used": 18}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/conical_gradient_fill_strip_radial.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [1.0, 0.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [1.0, 0.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [0.6666666865348816, 0.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 0, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/conical_gradient_uniform_fill_conical.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/conical_gradient_uniform_fill_conical.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.3125, 0.3125, 0.296875, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [1.21875, 0.737500011920929, 1.21875, 0.375, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 26, "work_registers_used": 23}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/conical_gradient_uniform_fill_conical.frag.gles", "has_uniform_computation": true, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [4.289999961853027, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [14.333333015441895, 5.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/conical_gradient_uniform_fill_radial.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/conical_gradient_uniform_fill_radial.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma", "varying"], "shortest_path_cycles": [0.25, 0.25, 0.21875, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.887499988079071, 0.5, 0.887499988079071, 0.25, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 18, "work_registers_used": 23}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/conical_gradient_uniform_fill_radial.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.9700000286102295, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [10.0, 5.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 3, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/conical_gradient_uniform_fill_strip.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/conical_gradient_uniform_fill_strip.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.171875, 0.171875, 0.140625, 0.0, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.90625, 0.5, 0.90625, 0.1875, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 18, "work_registers_used": 23}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/conical_gradient_uniform_fill_strip.frag.gles", "has_uniform_computation": true, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.9700000286102295, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [11.0, 5.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 5, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/conical_gradient_uniform_fill_strip_radial.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/conical_gradient_uniform_fill_strip_radial.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.109375, 0.0, 0.109375, 0.0, 0.0, 0.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.078125, 0.0, 0.078125, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.109375, 0.0, 0.109375, 0.0, 0.0, 0.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 18}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/conical_gradient_uniform_fill_strip_radial.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [1.0, 0.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [1.0, 0.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [0.6666666865348816, 0.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 2, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/fast_gradient.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/fast_gradient.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying"], "longest_path_cycles": [0.0625, 0.0625, 0.046875, 0.0, 0.0, 0.25, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.0625, 0.0625, 0.015625, 0.0, 0.0, 0.25, 0.0], "total_bound_pipelines": ["varying"], "total_cycles": [0.0625, 0.0625, 0.046875, 0.0, 0.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 2, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/fast_gradient.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic", "load_store"], "longest_path_cycles": [1.0, 1.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic", "load_store"], "shortest_path_cycles": [1.0, 1.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.6666666865348816, 1.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/fast_gradient.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/fast_gradient.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 7}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/fast_gradient.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.640000104904175, 5.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.640000104904175, 5.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [2.6666667461395264, 5.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 5, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/filter_position.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/filter_position.vert.gles", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 8}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/filter_position.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.9700000286102295, 4.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.9700000286102295, 4.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [3.0, 4.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/filter_position_uv.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/filter_position_uv.vert.gles", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 8}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/filter_position_uv.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.9700000286102295, 5.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.9700000286102295, 5.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [3.0, 5.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/gaussian.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/gaussian.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.09375, 0.0, 0.09375, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.265625, 0.09375, 0.265625, 0.0, 1.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/gaussian.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic", "load_store"], "shortest_path_cycles": [1.0, 1.0, 0.0], "total_bound_pipelines": ["arithmetic", "load_store"], "total_cycles": [2.0, 2.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/glyph_atlas.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/glyph_atlas.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying", "texture"], "longest_path_cycles": [0.109375, 0.03125, 0.109375, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.0625, 0.03125, 0.0625, 0.0, 0.0, 0.25, 0.25], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.125, 0.0625, 0.125, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/glyph_atlas.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic", "load_store", "texture"], "longest_path_cycles": [1.0, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_cycles": [1.0, 1.0, 1.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [2.6666667461395264, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/glyph_atlas.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/glyph_atlas.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 7}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/glyph_atlas.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.640000104904175, 5.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.640000104904175, 5.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [2.6666667461395264, 5.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 5, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/gradient_fill.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/gradient_fill.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 30, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0625, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0625, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0625, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 18, "work_registers_used": 11}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/gradient_fill.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [3.299999952316284, 4.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [3.299999952316284, 4.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [3.3333332538604736, 4.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/line.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/line.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying"], "longest_path_cycles": [0.1875, 0.1875, 0.1875, 0.0, 0.0, 1.75, 0.5], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.171875, 0.171875, 0.109375, 0.0, 0.0, 1.75, 0.0], "total_bound_pipelines": ["varying"], "total_cycles": [0.1875, 0.1875, 0.1875, 0.0, 0.0, 1.75, 0.5]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 24}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/line.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [3.299999952316284, 5.0, 2.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.309999942779541, 5.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [3.6666667461395264, 5.0, 2.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/line.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/line.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.09375, 0.0, 0.09375, 0.0, 10.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.09375, 0.0, 0.09375, 0.0, 10.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.09375, 0.0, 0.09375, 0.0, 10.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/line.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.640000104904175, 12.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.640000104904175, 12.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [2.6666667461395264, 12.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 5, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/linear_gradient_fill.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/linear_gradient_fill.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 4, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.40625, 0.296875, 0.40625, 0.125, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.203125, 0.1875, 0.203125, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.53125, 0.328125, 0.53125, 0.125, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 16, "work_registers_used": 20}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/linear_gradient_fill.frag.gles", "has_uniform_computation": true, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [7.260000228881836, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.309999942779541, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [8.0, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/linear_gradient_uniform_fill.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/linear_gradient_uniform_fill.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 2, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.203125, 0.203125, 0.171875, 0.0625, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.8125, 0.453125, 0.8125, 0.1875, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 12, "work_registers_used": 21}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/linear_gradient_uniform_fill.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.640000104904175, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [9.666666984558105, 4.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 3, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/linear_to_srgb_filter.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/linear_to_srgb_filter.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 40, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt", "arith_sfu"], "longest_path_cycles": [0.4375, 0.328125, 0.4375, 0.4375, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_sfu"], "shortest_path_cycles": [0.4375, 0.328125, 0.40625, 0.4375, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_cvt", "arith_sfu"], "total_cycles": [0.4375, 0.328125, 0.4375, 0.4375, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 30}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/linear_to_srgb_filter.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [4.949999809265137, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [4.949999809265137, 1.0, 1.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [5.333333492279053, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/morphology_filter.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/morphology_filter.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 80, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.234375, 0.078125, 0.234375, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 21}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/morphology_filter.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic", "load_store"], "shortest_path_cycles": [1.0, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [2.6666667461395264, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 4}}}}, "flutter/impeller/entity/gles/porter_duff_blend.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/porter_duff_blend.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 71, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying"], "longest_path_cycles": [0.21875, 0.21875, 0.03125, 0.0, 0.0, 0.5, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.21875, 0.21875, 0.0, 0.0, 0.0, 0.5, 0.25], "total_bound_pipelines": ["varying"], "total_cycles": [0.21875, 0.21875, 0.03125, 0.0, 0.0, 0.5, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 20}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/porter_duff_blend.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [1.3200000524520874, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [1.3200000524520874, 1.0, 1.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [1.6666666269302368, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/porter_duff_blend.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/porter_duff_blend.vert.gles", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.109375, 0.015625, 0.109375, 0.0, 4.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.109375, 0.015625, 0.109375, 0.0, 4.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.109375, 0.015625, 0.109375, 0.0, 4.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 9}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/porter_duff_blend.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.9700000286102295, 7.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.9700000286102295, 7.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [3.0, 7.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/radial_gradient_fill.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/radial_gradient_fill.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 3, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.4375, 0.3125, 0.4375, 0.1875, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.234375, 0.203125, 0.234375, 0.1875, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.5625, 0.34375, 0.5625, 0.1875, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 14, "work_registers_used": 20}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/radial_gradient_fill.frag.gles", "has_uniform_computation": true, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [7.260000228881836, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.309999942779541, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [8.0, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 3, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/radial_gradient_uniform_fill.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/radial_gradient_uniform_fill.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 28, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.203125, 0.171875, 0.203125, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.800000011920929, 0.375, 0.800000011920929, 0.25, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 21}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/radial_gradient_uniform_fill.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.640000104904175, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [10.0, 4.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 2, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/rrect_blur.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/rrect_blur.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.625, 0.625, 0.21875, 0.5, 0.0, 0.25, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.625, 0.625, 0.1875, 0.5, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [0.625, 0.625, 0.21875, 0.5, 0.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 18, "work_registers_used": 23}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/rrect_blur.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [6.599999904632568, 1.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [6.599999904632568, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [7.0, 1.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/rrect_blur.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/rrect_blur.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 7}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/rrect_blur.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.640000104904175, 4.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.640000104904175, 4.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [2.6666667461395264, 4.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 5, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/runtime_effect.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/runtime_effect.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 7}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/runtime_effect.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.640000104904175, 4.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.640000104904175, 4.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [2.6666667461395264, 4.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 5, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/solid_fill.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/solid_fill.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.03125, 0.0, 0.03125, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 2, "work_registers_used": 18}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/solid_fill.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [1.0, 0.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [1.0, 0.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [0.6666666865348816, 0.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/solid_fill.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/solid_fill.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 32}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/solid_fill.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.640000104904175, 3.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.640000104904175, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [2.6666667461395264, 3.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 5, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/srgb_to_linear_filter.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/srgb_to_linear_filter.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 40, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.484375, 0.328125, 0.484375, 0.4375, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.453125, 0.328125, 0.453125, 0.4375, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.484375, 0.328125, 0.484375, 0.4375, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 28}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/srgb_to_linear_filter.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [4.949999809265137, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [4.949999809265137, 1.0, 1.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [5.333333492279053, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/sweep_gradient_fill.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/sweep_gradient_fill.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 2, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.484375, 0.46875, 0.484375, 0.375, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_sfu"], "shortest_path_cycles": [0.375, 0.359375, 0.25, 0.375, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.578125, 0.5, 0.578125, 0.375, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 24}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/sweep_gradient_fill.frag.gles", "has_uniform_computation": true, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [7.920000076293945, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.9700000286102295, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [8.666666984558105, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/sweep_gradient_uniform_fill.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/sweep_gradient_uniform_fill.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.375, 0.375, 0.25, 0.3125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.925000011920929, 0.625, 0.925000011920929, 0.4375, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 25}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/sweep_gradient_uniform_fill.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.9700000286102295, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [10.0, 5.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 3, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/texture_downsample.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/texture_downsample.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.09375, 0.0, 0.09375, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.3125, 0.140625, 0.3125, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/texture_downsample.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic", "load_store"], "shortest_path_cycles": [1.0, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [2.6666667461395264, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 4}}}}, "flutter/impeller/entity/gles/texture_downsample_gles.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/texture_downsample_gles.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.09375, 0.0, 0.09375, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.421875, 0.140625, 0.421875, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/texture_downsample_gles.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic", "load_store"], "shortest_path_cycles": [1.0, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [5.0, 1.0, 1.0]}, "thread_occupancy": 50, "uniform_registers_used": 1, "work_registers_used": 6}}}}, "flutter/impeller/entity/gles/texture_fill.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/texture_fill.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying", "texture"], "longest_path_cycles": [0.046875, 0.03125, 0.046875, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.03125, 0.03125, 0.015625, 0.0, 0.0, 0.25, 0.25], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.046875, 0.03125, 0.046875, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/texture_fill.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic", "load_store", "texture"], "longest_path_cycles": [1.0, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_cycles": [1.0, 1.0, 1.0], "total_bound_pipelines": ["load_store", "texture"], "total_cycles": [0.6666666865348816, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/texture_fill.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/texture_fill.vert.gles", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 8}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/texture_fill.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.9700000286102295, 5.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [2.9700000286102295, 5.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [3.0, 5.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/texture_fill_strict_src.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/texture_fill_strict_src.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 33, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying", "texture"], "longest_path_cycles": [0.109375, 0.03125, 0.109375, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.078125, 0.03125, 0.078125, 0.0, 0.0, 0.25, 0.25], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.109375, 0.03125, 0.109375, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/texture_fill_strict_src.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic", "load_store", "texture"], "longest_path_cycles": [1.0, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_cycles": [1.0, 1.0, 1.0], "total_bound_pipelines": ["arithmetic", "load_store", "texture"], "total_cycles": [1.0, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/texture_uv_fill.vert.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/texture_uv_fill.vert.gles", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.140625, 0.140625, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 28, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.078125, 0.078125, 0.015625, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.078125, 0.078125, 0.015625, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.078125, 0.078125, 0.015625, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 16, "work_registers_used": 9}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/texture_uv_fill.vert.gles", "has_uniform_computation": false, "type": "Vertex", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [3.299999952316284, 4.0, 0.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [3.299999952316284, 4.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [3.3333332538604736, 4.0, 0.0]}, "thread_occupancy": 100, "uniform_registers_used": 7, "work_registers_used": 3}}}}, "flutter/impeller/entity/gles/tiled_texture_fill.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/tiled_texture_fill.frag.gles", "has_side_effects": false, "has_uniform_computation": false, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying", "texture"], "longest_path_cycles": [0.046875, 0.03125, 0.046875, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.03125, 0.03125, 0.015625, 0.0, 0.0, 0.25, 0.25], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.046875, 0.03125, 0.046875, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/tiled_texture_fill.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic", "load_store", "texture"], "longest_path_cycles": [1.0, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_cycles": [1.0, 1.0, 1.0], "total_bound_pipelines": ["load_store", "texture"], "total_cycles": [0.6666666865348816, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 1, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/tiled_texture_fill_external.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/tiled_texture_fill_external.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 35, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.421875, 0.328125, 0.421875, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.09375, 0.03125, 0.09375, 0.0, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.546875, 0.359375, 0.546875, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 22}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/tiled_texture_fill_external.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [8.25, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [1.3200000524520874, 1.0, 0.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [9.666666984558105, 1.0, 1.0]}, "thread_occupancy": 100, "uniform_registers_used": 2, "work_registers_used": 2}}}}, "flutter/impeller/entity/gles/vertices_uber_1.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/vertices_uber_1.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.887499988079071, 0.887499988079071, 0.53125, 0.125, 0.0, 0.5, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.421875, 0.421875, 0.34375, 0.0625, 0.0, 0.5, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [1.625, 1.625, 1.34375, 0.5, 0.0, 0.5, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 26}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/vertices_uber_1.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [9.569999694824219, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [3.9600000381469727, 1.0, 1.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [18.66666603088379, 1.0, 1.0]}, "thread_occupancy": 50, "uniform_registers_used": 1, "work_registers_used": 8}}}}, "flutter/impeller/entity/gles/vertices_uber_2.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/vertices_uber_2.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [1.2625000476837158, 1.2625000476837158, 0.637499988079071, 0.3125, 0.0, 0.5, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.421875, 0.421875, 0.359375, 0.0625, 0.0, 0.5, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [2.6875, 2.6875, 1.6749999523162842, 0.5625, 0.0, 0.5, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 12, "work_registers_used": 32}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/vertices_uber_2.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [11.550000190734863, 1.0, 1.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [4.289999961853027, 1.0, 1.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [31.66666603088379, 1.0, 1.0]}, "thread_occupancy": 50, "uniform_registers_used": 1, "work_registers_used": 8}}}}, "flutter/impeller/entity/gles/yuv_to_rgb_filter.frag.gles": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gles/yuv_to_rgb_filter.frag.gles", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["texture"], "longest_path_cycles": [0.15625, 0.15625, 0.046875, 0.0, 0.0, 0.25, 0.5], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["texture"], "shortest_path_cycles": [0.15625, 0.15625, 0.015625, 0.0, 0.0, 0.25, 0.5], "total_bound_pipelines": ["texture"], "total_cycles": [0.15625, 0.15625, 0.046875, 0.0, 0.0, 0.25, 0.5]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 12, "work_registers_used": 19}}}, "Mali-T880": {"core": "Mali-T880", "filename": "flutter/impeller/entity/gles/yuv_to_rgb_filter.frag.gles", "has_uniform_computation": false, "type": "Fragment", "variants": {"Main": {"has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arithmetic"], "longest_path_cycles": [2.309999942779541, 1.0, 2.0], "pipelines": ["arithmetic", "load_store", "texture"], "shortest_path_bound_pipelines": ["arithmetic"], "shortest_path_cycles": [2.309999942779541, 1.0, 2.0], "total_bound_pipelines": ["arithmetic"], "total_cycles": [2.6666667461395264, 1.0, 2.0]}, "thread_occupancy": 100, "uniform_registers_used": 3, "work_registers_used": 2}}}}, "flutter/impeller/entity/glyph_atlas.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/glyph_atlas.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying", "texture"], "longest_path_cycles": [0.078125, 0.03125, 0.078125, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.0625, 0.03125, 0.0625, 0.0, 0.0, 0.25, 0.25], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.09375, 0.0625, 0.09375, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 6}}}}, "flutter/impeller/entity/glyph_atlas.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/glyph_atlas.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 28, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 7}}}}, "flutter/impeller/entity/gradient_fill.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/gradient_fill.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 44, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0625, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0625, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0625, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 36, "work_registers_used": 11}}}}, "flutter/impeller/entity/line.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/line.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 33, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying"], "longest_path_cycles": [0.1875, 0.171875, 0.15625, 0.1875, 0.0, 1.75, 0.5], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.1875, 0.15625, 0.109375, 0.1875, 0.0, 1.75, 0.0], "total_bound_pipelines": ["varying"], "total_cycles": [0.1875, 0.171875, 0.15625, 0.1875, 0.0, 1.75, 0.5]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 18}}}}, "flutter/impeller/entity/line.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/line.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 28, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.046875, 0.0, 0.046875, 0.0, 10.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.046875, 0.0, 0.046875, 0.0, 10.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.046875, 0.0, 0.046875, 0.0, 10.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 22}}}}, "flutter/impeller/entity/linear_gradient_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/linear_gradient_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 28, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.296875, 0.234375, 0.296875, 0.0625, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.171875, 0.140625, 0.171875, 0.0625, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.390625, 0.265625, 0.390625, 0.0625, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 18, "work_registers_used": 7}}}}, "flutter/impeller/entity/linear_gradient_ssbo_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/linear_gradient_ssbo_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 32, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_sfu"], "shortest_path_cycles": [0.5625, 0.203125, 0.25, 0.5625, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.699999988079071, 0.390625, 0.699999988079071, 0.5625, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 24, "work_registers_used": 21}}}}, "flutter/impeller/entity/linear_gradient_uniform_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/linear_gradient_uniform_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 30, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.15625, 0.15625, 0.15625, 0.0625, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.84375, 0.34375, 0.84375, 0.25, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 14, "work_registers_used": 18}}}}, "flutter/impeller/entity/linear_to_srgb_filter.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/linear_to_srgb_filter.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 31, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_sfu"], "longest_path_cycles": [0.4375, 0.28125, 0.40625, 0.4375, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.171875, 0.140625, 0.171875, 0.0625, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_sfu"], "total_cycles": [0.4375, 0.328125, 0.40625, 0.4375, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 10}}}}, "flutter/impeller/entity/morphology_filter.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/morphology_filter.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 44, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.109375, 0.0, 0.109375, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.265625, 0.046875, 0.265625, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 11}}}}, "flutter/impeller/entity/porter_duff_blend.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/porter_duff_blend.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying"], "longest_path_cycles": [0.1875, 0.1875, 0.0, 0.0, 0.0, 0.5, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.1875, 0.1875, 0.0, 0.0, 0.0, 0.5, 0.25], "total_bound_pipelines": ["varying"], "total_cycles": [0.1875, 0.1875, 0.0, 0.0, 0.0, 0.5, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 8}}}}, "flutter/impeller/entity/porter_duff_blend.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/porter_duff_blend.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 30, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.078125, 0.015625, 0.078125, 0.0, 4.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.078125, 0.015625, 0.078125, 0.0, 4.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.078125, 0.015625, 0.078125, 0.0, 4.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 8}}}}, "flutter/impeller/entity/radial_gradient_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/radial_gradient_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 16, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.328125, 0.25, 0.328125, 0.125, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.203125, 0.15625, 0.203125, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.421875, 0.28125, 0.421875, 0.125, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 16, "work_registers_used": 8}}}}, "flutter/impeller/entity/radial_gradient_ssbo_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/radial_gradient_ssbo_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 26, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_sfu"], "shortest_path_cycles": [0.625, 0.21875, 0.28125, 0.625, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.737500011920929, 0.4375, 0.737500011920929, 0.6875, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 17}}}}, "flutter/impeller/entity/radial_gradient_uniform_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/radial_gradient_uniform_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 28, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.1875, 0.171875, 0.1875, 0.125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.875, 0.375, 0.875, 0.3125, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 12, "work_registers_used": 18}}}}, "flutter/impeller/entity/rrect_blur.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/rrect_blur.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.625, 0.625, 0.1875, 0.5, 0.0, 0.25, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.625, 0.625, 0.1875, 0.5, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [0.625, 0.625, 0.1875, 0.5, 0.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 18, "work_registers_used": 11}}}}, "flutter/impeller/entity/rrect_blur.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/rrect_blur.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 28, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 7}}}}, "flutter/impeller/entity/runtime_effect.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/runtime_effect.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 28, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.0, 0.0, 0.0, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 7}}}}, "flutter/impeller/entity/solid_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/solid_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.0625, 0.0, 0.0625, 0.0, 0.0, 0.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 5}}}}, "flutter/impeller/entity/solid_fill.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/solid_fill.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 28, "work_registers_used": 32}}}}, "flutter/impeller/entity/srgb_to_linear_filter.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/srgb_to_linear_filter.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 20, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_sfu"], "longest_path_cycles": [0.4375, 0.28125, 0.390625, 0.4375, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.15625, 0.140625, 0.15625, 0.0625, 0.0, 0.25, 0.25], "total_bound_pipelines": ["arith_total", "arith_sfu"], "total_cycles": [0.4375, 0.328125, 0.390625, 0.4375, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 9}}}}, "flutter/impeller/entity/sweep_gradient_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/sweep_gradient_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 10, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.40625, 0.40625, 0.359375, 0.3125, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma", "arith_sfu"], "shortest_path_cycles": [0.3125, 0.3125, 0.234375, 0.3125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.453125, 0.4375, 0.453125, 0.3125, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 15}}}}, "flutter/impeller/entity/sweep_gradient_ssbo_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/sweep_gradient_ssbo_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_sfu"], "shortest_path_cycles": [0.8125, 0.390625, 0.328125, 0.8125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.875, 0.65625, 0.78125, 0.875, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 28, "work_registers_used": 20}}}}, "flutter/impeller/entity/sweep_gradient_uniform_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/sweep_gradient_uniform_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.375, 0.375, 0.25, 0.3125, 0.0, 0.25, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.887499988079071, 0.625, 0.887499988079071, 0.5, 4.0, 0.25, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 20, "work_registers_used": 23}}}}, "flutter/impeller/entity/texture_downsample.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/texture_downsample.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.140625, 0.0, 0.140625, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.34375, 0.125, 0.34375, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 14}}}}, "flutter/impeller/entity/texture_downsample_gles.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/texture_downsample_gles.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": [null], "longest_path_cycles": [null, null, null, null, null, null, null], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.140625, 0.0, 0.140625, 0.0, 0.0, 0.0, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.453125, 0.125, 0.453125, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 17}}}}, "flutter/impeller/entity/texture_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/texture_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying", "texture"], "longest_path_cycles": [0.03125, 0.03125, 0.015625, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.03125, 0.03125, 0.015625, 0.0, 0.0, 0.25, 0.25], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.03125, 0.03125, 0.015625, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 5}}}}, "flutter/impeller/entity/texture_fill.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/texture_fill.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 30, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.015625, 0.015625, 0.015625, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 22, "work_registers_used": 8}}}}, "flutter/impeller/entity/texture_fill_strict_src.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/texture_fill_strict_src.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 33, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying", "texture"], "longest_path_cycles": [0.078125, 0.03125, 0.078125, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.078125, 0.03125, 0.078125, 0.0, 0.0, 0.25, 0.25], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.078125, 0.03125, 0.078125, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 5}}}}, "flutter/impeller/entity/texture_uv_fill.vert.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/texture_uv_fill.vert.vkspv", "has_uniform_computation": true, "type": "Vertex", "variants": {"Position": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.125, 0.125, 0.0, 0.0, 2.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 40, "work_registers_used": 32}, "Varying": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.078125, 0.078125, 0.015625, 0.0, 3.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.078125, 0.078125, 0.015625, 0.0, 3.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.078125, 0.078125, 0.015625, 0.0, 3.0, 0.0]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 32, "work_registers_used": 9}}}}, "flutter/impeller/entity/tiled_texture_fill.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/tiled_texture_fill.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying", "texture"], "longest_path_cycles": [0.03125, 0.03125, 0.015625, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.03125, 0.03125, 0.015625, 0.0, 0.0, 0.25, 0.25], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.03125, 0.03125, 0.015625, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 4, "work_registers_used": 5}}}}, "flutter/impeller/entity/tiled_texture_fill_external.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/tiled_texture_fill_external.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 72, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_cvt"], "longest_path_cycles": [0.375, 0.1875, 0.375, 0.0625, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying"], "shortest_path_cycles": [0.140625, 0.03125, 0.140625, 0.0625, 0.0, 0.25, 0.0], "total_bound_pipelines": ["arith_total", "arith_cvt"], "total_cycles": [0.4375, 0.21875, 0.4375, 0.0625, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 6, "work_registers_used": 7}}}}, "flutter/impeller/entity/vertices_uber_1.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/vertices_uber_1.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [0.887499988079071, 0.887499988079071, 0.46875, 0.125, 0.0, 0.5, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.5625, 0.5625, 0.21875, 0.125, 0.0, 0.5, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [1.6375000476837158, 1.6375000476837158, 1.1749999523162842, 0.5, 0.0, 0.5, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 10, "work_registers_used": 27}}}}, "flutter/impeller/entity/vertices_uber_2.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/vertices_uber_2.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 0, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["arith_total", "arith_fma"], "longest_path_cycles": [1.1124999523162842, 1.1124999523162842, 0.737500011920929, 0.3125, 0.0, 0.5, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_fma"], "shortest_path_cycles": [0.5625, 0.5625, 0.265625, 0.125, 0.0, 0.5, 0.25], "total_bound_pipelines": ["arith_total", "arith_fma"], "total_cycles": [2.700000047683716, 2.700000047683716, 1.4249999523162842, 0.5625, 0.0, 0.5, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 12, "work_registers_used": 32}}}}, "flutter/impeller/entity/yuv_to_rgb_filter.frag.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/entity/yuv_to_rgb_filter.frag.vkspv", "has_side_effects": false, "has_uniform_computation": true, "modifies_coverage": false, "reads_color_buffer": false, "type": "Fragment", "uses_late_zs_test": false, "uses_late_zs_update": false, "variants": {"Main": {"fp16_arithmetic": 100, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["varying", "texture"], "longest_path_cycles": [0.15625, 0.15625, 0.0, 0.0, 0.0, 0.25, 0.25], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "varying", "texture"], "shortest_path_bound_pipelines": ["varying", "texture"], "shortest_path_cycles": [0.15625, 0.15625, 0.0, 0.0, 0.0, 0.25, 0.25], "total_bound_pipelines": ["varying", "texture"], "total_cycles": [0.15625, 0.15625, 0.0, 0.0, 0.0, 0.25, 0.25]}, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 12, "work_registers_used": 6}}}}, "flutter/impeller/renderer/prefix_sum_test.comp.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/renderer/prefix_sum_test.comp.vkspv", "has_uniform_computation": true, "type": "Compute", "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [2.450000047683716, 0.0, 2.450000047683716, 1.0, 72.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["arith_total", "arith_cvt"], "shortest_path_cycles": [0.762499988079071, 0.0, 0.762499988079071, 0.0, 0.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [2.46875, 0.0, 2.46875, 1.0, 72.0, 0.0]}, "shared_storage_used": 4096, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 8, "work_registers_used": 18}}}}, "flutter/impeller/renderer/threadgroup_sizing_test.comp.vkspv": {"Mali-G78": {"core": "Mali-G78", "filename": "flutter/impeller/renderer/threadgroup_sizing_test.comp.vkspv", "has_uniform_computation": true, "type": "Compute", "variants": {"Main": {"fp16_arithmetic": null, "has_stack_spilling": false, "performance": {"longest_path_bound_pipelines": ["load_store"], "longest_path_cycles": [0.03125, 0.0, 0.03125, 0.0, 1.0, 0.0], "pipelines": ["arith_total", "arith_fma", "arith_cvt", "arith_sfu", "load_store", "texture"], "shortest_path_bound_pipelines": ["load_store"], "shortest_path_cycles": [0.03125, 0.0, 0.03125, 0.0, 1.0, 0.0], "total_bound_pipelines": ["load_store"], "total_cycles": [0.03125, 0.0, 0.03125, 0.0, 1.0, 0.0]}, "shared_storage_used": 0, "stack_spill_bytes": 0, "thread_occupancy": 100, "uniform_registers_used": 2, "work_registers_used": 4}}}}}