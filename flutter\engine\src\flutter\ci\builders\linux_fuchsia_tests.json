{"builds": [{"cas_archive": false, "drone_dimensions": ["device_type=none", "kvm=1", "os=Linux"], "gclient_variables": {"download_android_deps": false, "run_fuchsia_emu": true, "use_rbe": true}, "gn": ["--target-dir", "ci/fuchsia_profile_arm64_tester", "--fuchsia", "--fuchsia-cpu", "arm64", "--runtime-mode", "profile", "--no-lto", "--rbe", "--no-goma"], "name": "ci/fuchsia_profile_arm64_tester", "description": "Builds profile mode tests of arm64 Fuchsia.", "ninja": {"config": "ci/fuchsia_profile_arm64_tester", "targets": ["flutter/shell/platform/fuchsia:fuchsia", "flutter/shell/platform/fuchsia/dart_runner:dart_runner_tests", "fuchsia_tests"]}, "tests": [{"name": "arm64 emulator based profile / aot tests", "language": "python3", "script": "flutter/tools/fuchsia/with_envs.py", "parameters": ["testing/fuchsia/run_tests.py", "ci/fuchsia_profile_arm64_tester"]}]}, {"cas_archive": false, "drone_dimensions": ["device_type=none", "kvm=1", "os=Linux"], "gclient_variables": {"download_android_deps": false, "run_fuchsia_emu": true, "use_rbe": true}, "gn": ["--target-dir", "ci/fuchsia_release_arm64_tester", "--fuchsia", "--fuchsia-cpu", "arm64", "--runtime-mode", "release", "--no-lto", "--rbe", "--no-goma"], "name": "ci/fuchsia_release_arm64_tester", "description": "Builds release mode tests of arm64 Fuchsia.", "ninja": {"config": "ci/fuchsia_release_arm64_tester", "targets": ["flutter/shell/platform/fuchsia:fuchsia", "flutter/shell/platform/fuchsia/dart_runner:dart_runner_tests", "fuchsia_tests"]}, "tests": [{"name": "arm64 emulator based release tests", "language": "python3", "script": "flutter/tools/fuchsia/with_envs.py", "parameters": ["testing/fuchsia/run_tests.py", "ci/fuchsia_release_arm64_tester"]}]}, {"cas_archive": false, "drone_dimensions": ["device_type=none", "kvm=1", "os=Linux"], "gclient_variables": {"download_android_deps": false, "run_fuchsia_emu": true, "use_rbe": true}, "gn": ["--target-dir", "ci/fuchsia_debug_arm64_tester", "--fuchsia", "--fuchsia-cpu", "arm64", "--runtime-mode", "debug", "--no-lto", "--rbe", "--no-goma"], "name": "ci/fuchsia_debug_arm64_tester", "description": "Builds debug mode tests of arm64 Fuchsia.", "ninja": {"config": "ci/fuchsia_debug_arm64_tester", "targets": ["flutter/shell/platform/fuchsia:fuchsia", "flutter/shell/platform/fuchsia/dart_runner:dart_runner_tests", "fuchsia_tests"]}, "tests": [{"name": "arm64 emulator based debug tests", "language": "python3", "script": "flutter/tools/fuchsia/with_envs.py", "parameters": ["testing/fuchsia/run_tests.py", "ci/fuchsia_debug_arm64_tester"]}]}, {"cas_archive": false, "drone_dimensions": ["device_type=none", "kvm=1", "os=Linux"], "gclient_variables": {"download_android_deps": false, "run_fuchsia_emu": true, "use_rbe": true}, "gn": ["--target-dir", "ci/fuchsia_profile_x64_tester", "--fuchsia", "--fuchsia-cpu", "x64", "--runtime-mode", "profile", "--no-lto", "--rbe", "--no-goma"], "name": "ci/fuchsia_profile_x64_tester", "description": "Builds profile mode tests of x64 Fuchsia.", "ninja": {"config": "ci/fuchsia_profile_x64_tester", "targets": ["flutter/shell/platform/fuchsia:fuchsia", "flutter/shell/platform/fuchsia/dart_runner:dart_runner_tests", "fuchsia_tests"]}, "tests": [{"name": "x64 emulator based profile / aot tests", "language": "python3", "script": "flutter/tools/fuchsia/with_envs.py", "parameters": ["testing/fuchsia/run_tests.py", "ci/fuchsia_profile_x64_tester"]}]}, {"cas_archive": false, "drone_dimensions": ["device_type=none", "kvm=1", "os=Linux"], "gclient_variables": {"download_android_deps": false, "run_fuchsia_emu": true, "use_rbe": true}, "gn": ["--target-dir", "ci/fuchsia_release_x64_tester", "--fuchsia", "--fuchsia-cpu", "x64", "--runtime-mode", "release", "--no-lto", "--rbe", "--no-goma"], "name": "ci/fuchsia_release_x64_tester", "description": "Builds release mode tests of x64 Fuchsia.", "ninja": {"config": "ci/fuchsia_release_x64_tester", "targets": ["flutter/shell/platform/fuchsia:fuchsia", "flutter/shell/platform/fuchsia/dart_runner:dart_runner_tests", "fuchsia_tests"]}, "tests": [{"name": "x64 emulator based release tests", "language": "python3", "script": "flutter/tools/fuchsia/with_envs.py", "parameters": ["testing/fuchsia/run_tests.py", "ci/fuchsia_release_x64_tester"]}]}, {"cas_archive": false, "drone_dimensions": ["device_type=none", "kvm=1", "os=Linux"], "gclient_variables": {"download_android_deps": false, "run_fuchsia_emu": true, "use_rbe": true}, "gn": ["--target-dir", "ci/fuchsia_debug_x64_tester", "--fuchsia", "--fuchsia-cpu", "x64", "--runtime-mode", "debug", "--no-lto", "--rbe", "--no-goma"], "name": "ci/fuchsia_debug_x64_tester", "description": "Builds debug mode tests of x64 Fuchsia.", "ninja": {"config": "ci/fuchsia_debug_x64_tester", "targets": ["flutter/shell/platform/fuchsia:fuchsia", "flutter/shell/platform/fuchsia/dart_runner:dart_runner_tests", "fuchsia_tests"]}, "tests": [{"name": "run_tests test", "script": "flutter/testing/fuchsia/run_tests_test.py"}, {"name": "build_fuchsia_artifacts test", "script": "flutter/tools/fuchsia/build_fuchsia_artifacts_test.py"}, {"name": "x64 emulator based debug tests", "language": "python3", "script": "flutter/tools/fuchsia/with_envs.py", "parameters": ["testing/fuchsia/run_tests.py", "ci/fuchsia_debug_x64_tester"]}]}]}