// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';

void main() {
  // Changes made in https://github.com/flutter/flutter/pull/93427
  ColorScheme colorScheme = ColorScheme();
  colorScheme = ColorScheme();
  colorScheme = ColorScheme.light();
  colorScheme = ColorScheme.dark();
  colorScheme = ColorScheme.highContrastLight();
  colorScheme = ColorScheme.highContrastDark();
  colorScheme = colorScheme.copyWith();
  colorScheme.primaryContainer; // Removing field reference not supported.
  colorScheme.secondaryContainer;

  // Changes made in https://github.com/flutter/flutter/pull/138521
  ColorScheme colorScheme = ColorScheme();
  colorScheme = ColorScheme(
    surface: Colors.black,
    onSurface: Colors.white,
    surfaceContainerHighest: Colors.red,
  );
  colorScheme = ColorScheme(
    surface: Colors.orange,
    onSurface: Colors.yellow,
    surfaceContainerHighest: Colors.blue,
  );
  colorScheme = ColorScheme.light(
    surface: Colors.black,
    onSurface: Colors.white,
    surfaceContainerHighest: Colors.red,
  );
  colorScheme = ColorScheme.light(
    surface: Colors.orange,
    onSurface: Colors.yellow,
    surfaceContainerHighest: Colors.blue,
  );
  colorScheme = ColorScheme.dark(
    surface: Colors.black,
    onSurface: Colors.white,
    surfaceContainerHighest: Colors.red,
  );
  colorScheme = ColorScheme.dark(
    surface: Colors.orange,
    onSurface: Colors.yellow,
    surfaceContainerHighest: Colors.blue,
  );
  colorScheme = ColorScheme.highContrastLight(
    surface: Colors.black,
    onSurface: Colors.white,
    surfaceContainerHighest: Colors.red,
  );
  colorScheme = ColorScheme.highContrastLight(
    surface: Colors.orange,
    onSurface: Colors.yellow,
    surfaceContainerHighest: Colors.blue,
  );
  colorScheme = ColorScheme.highContrastDark(
    surface: Colors.black,
    onSurface: Colors.white,
    surfaceContainerHighest: Colors.red,
  );
  colorScheme = ColorScheme.highContrastDark(
    surface: Colors.orange,
    onSurface: Colors.yellow,
    surfaceContainerHighest: Colors.blue,
  );
  colorScheme = colorScheme.copyWith(
    surface: Colors.black,
    onSurface: Colors.white,
    surfaceContainerHighest: Colors.red,
  );
  colorScheme = colorScheme.copyWith(
    surface: Colors.orange,
    onSurface: Colors.yellow,
    surfaceContainerHighest: Colors.blue,
  );
  colorScheme.surface;
  colorScheme.onSurface;
  colorScheme.surfaceContainerHighest;
}
