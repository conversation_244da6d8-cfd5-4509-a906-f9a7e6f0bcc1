{"version": "6_1_0", "md.comp.date-picker.modal.container.color": "surfaceContainerHigh", "md.comp.date-picker.modal.container.elevation": "md.sys.elevation.level3", "md.comp.date-picker.modal.container.height": 568.0, "md.comp.date-picker.modal.container.shape": "md.sys.shape.corner.extra-large", "md.comp.date-picker.modal.container.width": 360.0, "md.comp.date-picker.modal.date.container.height": 40.0, "md.comp.date-picker.modal.date.container.shape": "md.sys.shape.corner.full", "md.comp.date-picker.modal.date.container.width": 40.0, "md.comp.date-picker.modal.date.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.date-picker.modal.date.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.date-picker.modal.date.label-text.text-style": "bodyLarge", "md.comp.date-picker.modal.date.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.date-picker.modal.date.selected.container.color": "primary", "md.comp.date-picker.modal.date.selected.focus.state-layer.color": "onPrimary", "md.comp.date-picker.modal.date.selected.hover.state-layer.color": "onPrimary", "md.comp.date-picker.modal.date.selected.label-text.color": "onPrimary", "md.comp.date-picker.modal.date.selected.pressed.state-layer.color": "onPrimary", "md.comp.date-picker.modal.date.state-layer.height": 40.0, "md.comp.date-picker.modal.date.state-layer.shape": "md.sys.shape.corner.full", "md.comp.date-picker.modal.date.state-layer.width": 40.0, "md.comp.date-picker.modal.date.today.container.outline.color": "primary", "md.comp.date-picker.modal.date.today.container.outline.width": 1.0, "md.comp.date-picker.modal.date.today.focus.state-layer.color": "primary", "md.comp.date-picker.modal.date.today.hover.state-layer.color": "primary", "md.comp.date-picker.modal.date.today.label-text.color": "primary", "md.comp.date-picker.modal.date.today.pressed.state-layer.color": "primary", "md.comp.date-picker.modal.date.unselected.focus.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.modal.date.unselected.hover.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.modal.date.unselected.label-text.color": "onSurface", "md.comp.date-picker.modal.date.unselected.pressed.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.modal.header.container.height": 120.0, "md.comp.date-picker.modal.header.container.width": 360.0, "md.comp.date-picker.modal.header.headline.color": "onSurfaceVariant", "md.comp.date-picker.modal.header.headline.text-style": "headlineLarge", "md.comp.date-picker.modal.header.supporting-text.color": "onSurfaceVariant", "md.comp.date-picker.modal.header.supporting-text.text-style": "labelLarge", "md.comp.date-picker.modal.range-selection.active-indicator.container.color": "secondaryContainer", "md.comp.date-picker.modal.range-selection.active-indicator.container.height": 40.0, "md.comp.date-picker.modal.range-selection.active-indicator.container.shape": "md.sys.shape.corner.full", "md.comp.date-picker.modal.range-selection.container.elevation": "md.sys.elevation.level0", "md.comp.date-picker.modal.range-selection.container.shape": "md.sys.shape.corner.none", "md.comp.date-picker.modal.range-selection.date.in-range.focus.state-layer.color": "onPrimaryContainer", "md.comp.date-picker.modal.range-selection.date.in-range.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.date-picker.modal.range-selection.date.in-range.hover.state-layer.color": "onPrimaryContainer", "md.comp.date-picker.modal.range-selection.date.in-range.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.date-picker.modal.range-selection.date.in-range.label-text.color": "onSecondaryContainer", "md.comp.date-picker.modal.range-selection.date.in-range.pressed.state-layer.color": "onPrimaryContainer", "md.comp.date-picker.modal.range-selection.date.in-range.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.date-picker.modal.range-selection.header.container.height": 128.0, "md.comp.date-picker.modal.range-selection.header.headline.text-style": "title<PERSON>arge", "md.comp.date-picker.modal.range-selection.month.subhead.color": "onSurfaceVariant", "md.comp.date-picker.modal.range-selection.month.subhead.text-style": "titleSmall", "md.comp.date-picker.modal.weekdays.label-text.color": "onSurface", "md.comp.date-picker.modal.weekdays.label-text.text-style": "bodyLarge", "md.comp.date-picker.modal.year-selection.year.container.height": 36.0, "md.comp.date-picker.modal.year-selection.year.container.width": 72.0, "md.comp.date-picker.modal.year-selection.year.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.date-picker.modal.year-selection.year.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.date-picker.modal.year-selection.year.label-text.text-style": "bodyLarge", "md.comp.date-picker.modal.year-selection.year.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.date-picker.modal.year-selection.year.selected.container.color": "primary", "md.comp.date-picker.modal.year-selection.year.selected.focus.state-layer.color": "onPrimary", "md.comp.date-picker.modal.year-selection.year.selected.hover.state-layer.color": "onPrimary", "md.comp.date-picker.modal.year-selection.year.selected.label-text.color": "onPrimary", "md.comp.date-picker.modal.year-selection.year.selected.pressed.state-layer.color": "onPrimary", "md.comp.date-picker.modal.year-selection.year.state-layer.height": 36.0, "md.comp.date-picker.modal.year-selection.year.state-layer.shape": "md.sys.shape.corner.full", "md.comp.date-picker.modal.year-selection.year.state-layer.width": 72.0, "md.comp.date-picker.modal.year-selection.year.unselected.focus.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.modal.year-selection.year.unselected.hover.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.modal.year-selection.year.unselected.label-text.color": "onSurfaceVariant", "md.comp.date-picker.modal.year-selection.year.unselected.pressed.state-layer.color": "onSurfaceVariant"}