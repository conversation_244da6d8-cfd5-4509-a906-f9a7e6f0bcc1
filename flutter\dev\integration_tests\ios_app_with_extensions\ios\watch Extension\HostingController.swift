// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

//
//  HostingController.swift
//  watch Extension
//
//  Created by <PERSON> on 08.04.20.
//

import Foundation
import SwiftUI
import WatchKit

class HostingController: WKHostingController<ContentView> {
    override var body: ContentView {
        return ContentView()
    }
}
