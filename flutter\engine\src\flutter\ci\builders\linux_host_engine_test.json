{"_comment": ["The builds defined in this file should only contain tests, ", "and the file should not contain builds that produce artifacts. "], "builds": [{"drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/host_debug_test", "--runtime-mode", "debug", "--prebuilt-dart-sdk", "--build-embedder-examples", "--rbe", "--no-goma"], "name": "ci/host_debug_test", "description": "Builds host-side unit tests for Linux.", "ninja": {"config": "ci/host_debug_test", "targets": ["flutter:unittests", "flutter/build/dart:copy_dart_sdk", "flutter/shell/testing", "flutter/tools/path_ops"]}, "tests": [{"language": "python3", "name": "Host Tests for host_debug", "script": "flutter/testing/run_tests.py", "parameters": ["--variant", "ci/host_debug_test", "--type", "dart,dart-host", "--engine-capture-core-dump"]}]}, {"drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/host_profile_test", "--runtime-mode", "profile", "--no-lto", "--prebuilt-dart-sdk", "--build-embedder-examples", "--rbe", "--no-goma"], "name": "ci/host_profile_test", "description": "Builds host-side unit tests for Linux.", "ninja": {"config": "ci/host_profile_test", "targets": ["flutter:unittests", "flutter/build/dart:copy_dart_sdk", "flutter/shell/testing", "flutter/tools/path_ops"]}, "tests": [{"language": "python3", "name": "Host Tests for host_profile", "script": "flutter/testing/run_tests.py", "parameters": ["--variant", "ci/host_profile_test", "--type", "dart,dart-host,engine", "--engine-capture-core-dump"]}]}, {"drone_dimensions": ["device_type=none", "os=Linux"], "dependencies": [{"dependency": "goldctl", "version": "git_revision:720a542f6fe4f92922c3b8f0fdcc4d2ac6bb83cd"}], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/host_release_test", "--runtime-mode", "release", "--prebuilt-dart-sdk", "--build-embedder-examples", "--rbe", "--no-goma"], "name": "ci/host_release_test", "description": "Builds host-side unit tests and benchmarks for Linux.", "ninja": {"config": "ci/host_release_test", "targets": ["flutter:unittests", "flutter/build/dart:copy_dart_sdk", "flutter/display_list:display_list_benchmarks", "flutter/display_list:display_list_builder_benchmarks", "flutter/display_list:display_list_region_benchmarks", "flutter/display_list:display_list_transform_benchmarks", "flutter/fml:fml_benchmarks", "flutter/impeller/geometry:geometry_benchmarks", "flutter/lib/ui:ui_benchmarks", "flutter/shell/common:shell_benchmarks", "flutter/shell/testing", "flutter/tools/path_ops", "flutter/txt:txt_benchmarks"]}, "tests": [{"language": "python3", "name": "Host Tests for host_release", "script": "flutter/testing/run_tests.py", "parameters": ["--variant", "ci/host_release_test", "--type", "dart,dart-host,engine,benchmarks", "--engine-capture-core-dump"]}, {"language": "bash", "name": "Generate metrics test", "script": "flutter/testing/benchmark/generate_metrics.sh", "parameters": ["ci/host_release_test"]}, {"language": "bash", "name": "Upload metrics dry-run", "script": "flutter/testing/benchmark/upload_metrics.sh", "parameters": ["ci/host_release_test", "--no-upload"]}]}]}