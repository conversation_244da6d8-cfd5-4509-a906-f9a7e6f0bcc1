#!/bin/bash
#
# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

set -e

# Needed because if it is set, cd may print the path it changed to.
unset CDPATH

# On Mac OS, readlink -f doesn't work, so follow_links traverses the path one
# link at a time, and then cds into the link destination and find out where it
# ends up.
#
# The function is enclosed in a subshell to avoid changing the working directory
# of the caller.
function follow_links() (
  cd -P "$(dirname -- "$1")"
  file="$PWD/$(basename -- "$1")"
  while [[ -h "$file" ]]; do
    cd -P "$(dirname -- "$file")"
    file="$(readlink -- "$file")"
    cd -P "$(dirname -- "$file")"
    file="$PWD/$(basename -- "$file")"
  done
  echo "$file"
)

SCRIPT_DIR=$(follow_links "$(dirname -- "${BASH_SOURCE[0]}")")
SRC_DIR="$(
  cd "$SCRIPT_DIR/../../.."
  pwd -P
)"
FLUTTER_DIR="$SRC_DIR/flutter"

# Creates a file named `GeneratedPluginRegistrant.java` in the project outside
# of it's expected location in shell/platform/android/test/... (where the exact
# name and location is required to get loaded).
#
# This file is typically generated by Flutter tooling and should not exist.
echo "Creating file ./src/flutter/GeneratedPluginRegistrant.java"
touch "$FLUTTER_DIR/GeneratedPluginRegistrant.java"

# Create a trap that, on exit, removes the temp files.
function cleanup() {
  rm -f "$SRC_DIR/third_party/GeneratedPluginRegistrant.java"
  rm -f "$FLUTTER_DIR/GeneratedPluginRegistrant.java"
  rm -f "$FLUTTER_DIR/third_party/GeneratedPluginRegistrant.java"
}
trap cleanup EXIT

# Run the ban script, expecting it to fail.
# Intercept the output, only check the exit code.
"$FLUTTER_DIR/ci/ban_generated_plugin_registrant_java.sh" > /dev/null 2>&1 && {
  echo "FAIL: ban_generated_plugin_registrant_java did not fail as expected"
  exit 1
}
echo "PASS: ban_generated_plugin_registrant_java failed as expected"

# Create a file in SRC_DIR/third_party, that should be OK.
echo "Creating file ./src/flutter/third_party/GeneratedPluginRegistrant.java"
touch "$SRC_DIR/flutter/third_party/GeneratedPluginRegistrant.java"

# Run the ban script, expecting it to succeed.
"$FLUTTER_DIR/ci/ban_generated_plugin_registrant_java.sh" > /dev/null 2>&1 || {
  echo "PASS: ban_generated_plugin_registrant_java ignored third_party"
}

# Create a file in SRC_DIR/flutter/third_party, that should be OK too.
echo "Creating file ./src/flutter/third_party/GeneratedPluginRegistrant.java"
touch "$FLUTTER_DIR/third_party/GeneratedPluginRegistrant.java"

# Run the ban script, expecting it to succeed.
"$FLUTTER_DIR/ci/ban_generated_plugin_registrant_java.sh" > /dev/null 2>&1 || {
  echo "PASS: ban_generated_plugin_registrant_java ignored flutter/third_party"
}
