# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/shell/config.gni")

_profiler_deps = [
  "//flutter/common",
  "//flutter/fml",
]

source_set("profiling") {
  sources = [
    "sampling_profiler.cc",
    "sampling_profiler.h",
  ]

  deps = _profiler_deps
}

source_set("profiling_unittests") {
  testonly = true
  sources = [ "sampling_profiler_unittest.cc" ]
  deps = [
    ":profiling",
    "//flutter/testing",
  ]
}
