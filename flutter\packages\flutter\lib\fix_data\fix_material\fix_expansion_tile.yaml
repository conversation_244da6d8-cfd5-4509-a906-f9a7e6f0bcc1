# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the flutter/packages/flutter/test_fixes/README.md
# file for instructions on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for ExpansionTile from the Material library. *
#   For fixes to
#     * AppBarTheme: fix_app_bar_theme.yaml
#     * AppBar: fix_app_bar.yaml
#     * ButtonBar: fix_button_bar.yaml
#     * ColorScheme: fix_color_scheme.yaml
#     * Material (general): fix_material.yaml
#     * SliverAppBar: fix_sliver_app_bar.yaml
#     * TextTheme: fix_text_theme.yaml
#     * ThemeData: fix_theme_data.yaml
version: 1
transforms:
  # Changes made in https://github.com/flutter/flutter/pull/
  - title: "Migrate to 'ExpansibleController'"
    date: 2025-04-01
    element:
      uris: [ 'material.dart', 'widgets.dart' ]
      class: 'ExpansionTileController'
    changes:
      - kind: 'rename'
        newName: 'ExpansibleController'

# Before adding a new fix: read instructions at the top of this file.
