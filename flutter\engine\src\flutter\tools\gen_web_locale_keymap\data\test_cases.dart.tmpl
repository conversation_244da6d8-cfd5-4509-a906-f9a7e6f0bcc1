//---------------------------------------------------------------------------------------------
//  Copyright (c) 2022 Google LLC
//  Licensed under the MIT License. See License.txt in the project root for license information.
//--------------------------------------------------------------------------------------------*/

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
//
// This file is auto generated by flutter/engine:flutter/tools/gen_web_keyboard_keymap based on
// https://github.com/microsoft/vscode/tree/@@@COMMIT_ID@@@/src/vs/workbench/services/keybinding/browser/keyboardLayouts
//
// Edit the following files instead:
//
//  - Script: lib/main.dart
//  - Templates: data/*.tmpl
//
// See flutter/engine:flutter/tools/gen_web_keyboard_keymap/README.md for more information.

import 'package:test/test.dart';
import 'package:web_locale_keymap/web_locale_keymap.dart';
import 'testing.dart';

void testWin(LocaleKeymap mapping) {
@@@WIN_CASES@@@
}

void testLinux(LocaleKeymap mapping) {
@@@LINUX_CASES@@@
}

void testDarwin(LocaleKeymap mapping) {
@@@DARWIN_CASES@@@
}
