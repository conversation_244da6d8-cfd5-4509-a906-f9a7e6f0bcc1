// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() {
  // Changes made in https://github.com/flutter/flutter/pull/86198
  SliverAppBar sliverAppBar = SliverAppBar();
  sliverAppBar = SliverAppBar(systemOverlayStyle: SystemUiOverlayStyle.dark);
  sliverAppBar = SliverAppBar(systemOverlayStyle: SystemUiOverlayStyle.light);
  sliverAppBar = SliverAppBar(error: '');
  sliverAppBar.systemOverlayStyle;

  TextTheme myTextTheme = TextTheme();
  SliverAppBar sliverAppBar = SliverAppBar();
  sliverAppBar = SliverAppBar(toolbarTextStyle: myTextTheme.bodyMedium, titleTextStyle: myTextTheme.titleLarge);

  SliverAppBar sliverAppBar = SliverAppBar();
  sliverAppBar = SliverAppBar();
  sliverAppBar = SliverAppBar();
  sliverAppBar
      .backwardsCompatibility; // Removing field reference not supported.
}
