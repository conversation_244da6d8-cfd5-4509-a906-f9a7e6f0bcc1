# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

dart_ui_files = [
  "//flutter/lib/ui/annotations.dart",
  "//flutter/lib/ui/channel_buffers.dart",
  "//flutter/lib/ui/compositing.dart",
  "//flutter/lib/ui/geometry.dart",
  "//flutter/lib/ui/hooks.dart",
  "//flutter/lib/ui/isolate_name_server.dart",
  "//flutter/lib/ui/key.dart",
  "//flutter/lib/ui/lerp.dart",
  "//flutter/lib/ui/math.dart",
  "//flutter/lib/ui/natives.dart",
  "//flutter/lib/ui/painting.dart",
  "//flutter/lib/ui/platform_dispatcher.dart",
  "//flutter/lib/ui/platform_isolate.dart",
  "//flutter/lib/ui/plugins.dart",
  "//flutter/lib/ui/pointer.dart",
  "//flutter/lib/ui/semantics.dart",
  "//flutter/lib/ui/text.dart",
  "//flutter/lib/ui/ui.dart",
  "//flutter/lib/ui/window.dart",
]

dart_ui_path = "//flutter/lib/ui/ui.dart"

dart_ui_web_files = [
  "$root_out_dir/flutter_web_sdk/lib/ui_web/ui_web/url_strategy.dart",
  "$root_out_dir/flutter_web_sdk/lib/ui_web/ui_web.dart",
]

dart_ui_web_path = "//flutter/lib/web_ui/lib/ui_web/src/ui_web.dart"
