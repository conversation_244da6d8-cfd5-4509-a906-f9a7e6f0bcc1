{"dialModeButtonLabel": "Zur Uhrzeitauswahl wechseln", "licensesPackageDetailTextOne": "1 Lizenz", "timePickerDialHelpText": "UHRZEIT AUSWÄHLEN", "timePickerInputHelpText": "ZEIT EINGEBEN", "timePickerHourLabel": "Stunde", "timePickerMinuteLabel": "Minute", "invalidTimeLabel": "Gib eine gültige Uhrzeit ein", "licensesPackageDetailTextOther": "$licenseCount <PERSON><PERSON><PERSON>", "inputTimeModeButtonLabel": "Zum Texteingabemodus wechseln", "dateSeparator": ".", "dateInputLabel": "<PERSON><PERSON> e<PERSON>", "calendarModeButtonLabel": "Zum Kalender wechseln", "dateRangePickerHelpText": "ZEITRAUM AUSWÄHLEN", "datePickerHelpText": "DATUM AUSWÄHLEN", "saveButtonLabel": "SPEICHERN", "dateOutOfRangeLabel": "Ausserhalb des Zeitraums.", "invalidDateRangeLabel": "Ungültiger Zeitraum.", "invalidDateFormatLabel": "Ungültiges Format.", "dateRangeEndDateSemanticLabel": "Enddatum $fullDate", "dateRangeStartDateSemanticLabel": "Startdatum $fullDate", "dateRangeEndLabel": "Enddatum", "dateRangeStartLabel": "Startdatum", "inputDateModeButtonLabel": "Zur Texteingabe wechseln", "unspecifiedDateRange": "Zeitraum", "unspecifiedDate": "Datum", "selectYearSemanticsLabel": "Jahr auswählen", "dateHelpText": "tt.mm.jjjj", "moreButtonTooltip": "<PERSON><PERSON>", "tabLabel": "Tab $tabIndex von $tabCount", "showAccountsLabel": "Konten anzeigen", "hideAccountsLabel": "Konten ausblenden", "signedInLabel": "Angemeldet", "timePickerMinuteModeAnnouncement": "Minuten auswählen", "timePickerHourModeAnnouncement": "Stunden auswählen", "scriptCategory": "English-like", "timeOfDayFormat": "HH:mm", "openAppDrawerTooltip": "Navigationsmenü ö<PERSON>nen", "backButtonTooltip": "Zurück", "closeButtonTooltip": "<PERSON><PERSON><PERSON><PERSON>", "deleteButtonTooltip": "Löschen", "nextMonthTooltip": "Nächster Monat", "previousMonthTooltip": "<PERSON><PERSON><PERSON><PERSON>", "nextPageTooltip": "Nächste Seite", "previousPageTooltip": "Vorherige Seite", "firstPageTooltip": "First page", "lastPageTooltip": "Last page", "showMenuTooltip": "<PERSON><PERSON> anzeigen", "aboutListTileTitle": "Über $applicationName", "licensesPageTitle": "<PERSON><PERSON><PERSON>", "pageRowsInfoTitle": "$firstRow–$lastRow von $rowCount", "pageRowsInfoTitleApproximate": "$firstRow–$lastRow von etwa $rowCount", "rowsPerPageTitle": "Zeilen pro Seite:", "selectedRowCountTitleOne": "1 Element ausgewählt", "selectedRowCountTitleOther": "$selectedRowCount Elemente ausgewählt", "cancelButtonLabel": "ABBRECHEN", "closeButtonLabel": "SCHLIEẞEN", "continueButtonLabel": "WEITER", "copyButtonLabel": "<PERSON><PERSON><PERSON>", "cutButtonLabel": "Ausschneiden", "okButtonLabel": "OK", "pasteButtonLabel": "Einsetzen", "selectAllButtonLabel": "Alle auswählen", "viewLicensesButtonLabel": "LIZENZEN ANZEIGEN", "anteMeridiemAbbreviation": "AM", "postMeridiemAbbreviation": "PM", "modalBarrierDismissLabel": "<PERSON><PERSON><PERSON><PERSON>", "drawerLabel": "Navigationsmenü", "popupMenuLabel": "Pop-up-<PERSON><PERSON>", "dialogLabel": "<PERSON><PERSON><PERSON>", "alertDialogLabel": "Benachrichtigung", "searchFieldLabel": "<PERSON><PERSON>", "reorderItemToStart": "An den Anfang verschieben", "reorderItemToEnd": "An das Ende verschieben", "reorderItemUp": "Nach oben verschieben", "reorderItemDown": "Nach unten verschieben", "reorderItemLeft": "Nach links verschieben", "reorderItemRight": "Nach rechts verschieben", "expandedIconTapHint": "Minimieren", "collapsedIconTapHint": "<PERSON><PERSON><PERSON>", "remainingTextFieldCharacterCountOne": "Noch 1 Zeichen", "remainingTextFieldCharacterCountOther": "Noch $remainingCount Zeichen", "refreshIndicatorSemanticLabel": "Aktualisieren"}