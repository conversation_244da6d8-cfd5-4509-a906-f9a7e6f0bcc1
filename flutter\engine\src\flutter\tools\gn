#!/usr/bin/env vpython3
# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import argparse
import ctypes
import multiprocessing
import os
import platform
import re
import subprocess
import sys

SRC_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def get_out_dir(args):
  if args.target_os is not None:
    target_dir = [args.target_os]
  elif args.web:
    target_dir = ['wasm']
  else:
    target_dir = ['host']

  target_dir.append(args.runtime_mode)

  if args.simulator:
    target_dir.append('sim')

  if args.unoptimized:
    target_dir.append('unopt')

  if args.android_cpu != 'arm':
    target_dir.append(args.android_cpu)

  if args.mac_cpu != 'x64':
    target_dir.append(args.mac_cpu)

  if args.simulator_cpu != 'x64':
    target_dir.append(args.simulator_cpu)

  if args.linux_cpu is not None:
    target_dir.append(args.linux_cpu)

  if args.windows_cpu != 'x64':
    target_dir.append(args.windows_cpu)

  if args.target_os == 'fuchsia' and args.fuchsia_cpu is not None:
    target_dir.append(args.fuchsia_cpu)

  # This exists for backwards compatibility of tests that are being run
  # on LUCI. This can be removed in coordination with a LUCI change:
  # https://github.com/flutter/flutter/issues/76547
  if args.macos_enable_metal:
    target_dir.append('metal')

  if args.darwin_extension_safe:
    target_dir.append('extension_safe')

  if args.slimpeller:
    target_dir.append('slimpeller')

  if args.target_dir != '':
    target_dir = [args.target_dir]

  return os.path.join(args.out_dir, 'out', '_'.join(target_dir))


def to_command_line(gn_args):
  """Converts the arguments dictionary to a list of command-line arguments.

    Args:
      gn_args: GN arguments dictionary generated by to_gn_args().
    """

  def merge(key, value):
    if isinstance(value, bool):
      return '%s=%s' % (key, 'true' if value else 'false')
    if isinstance(value, int):
      return '%s=%d' % (key, value)
    return '%s="%s"' % (key, value)

  return [merge(x, y) for x, y in gn_args.items()]


def is_host_build(args):
  # If target_os == None, then this is a host build.
  if args.target_os is None:
    return True
  # For linux arm64 builds, we cross compile from x64 hosts, so the
  # target_os='linux' and linux-cpu='arm64'
  if args.target_os == 'linux' and args.linux_cpu == 'arm64':
    return True
  # The Mac and host targets are redundant. Again, necessary to disambiguate
  # during cross-compilation.
  if args.target_os == 'mac':
    return True
  return False


# Determines whether a prebuilt Dart SDK can be used instead of building one.
def can_use_prebuilt_dart(args):
  prebuilt = None
  # When doing a 'host' build (args.target_os is None), or a build when the
  # target OS and host OS are different, the prebuilt Dart SDK is the Dart SDK
  # for the host system's OS and archetecture.
  if args.target_os is None or args.target_os in ['android', 'ios', 'fuchsia']:
    if sys.platform.startswith(('cygwin', 'win')):
      prebuilt = 'windows-x64'
    elif sys.platform == 'darwin':
      prebuilt = 'macos-x64'
    else:
      prebuilt = 'linux-x64'
  elif args.target_os == 'linux' and args.linux_cpu in ['x64', 'arm64']:
    prebuilt = 'linux-%s' % args.linux_cpu
  elif args.target_os == 'mac' and args.mac_cpu in ['x64', 'arm64']:
    prebuilt = 'macos-%s' % args.mac_cpu
  elif args.target_os == 'win' and args.windows_cpu in ['x64', 'arm64']:
    prebuilt = 'windows-%s' % args.windows_cpu

  prebuilts_dir = None
  if prebuilt is not None:
    prebuilts_dir = os.path.join(SRC_ROOT, 'flutter', 'prebuilts', prebuilt)
  return prebuilts_dir is not None and os.path.isdir(prebuilts_dir)


# Returns the host machine operating system.
def get_host_os():
  if sys.platform.startswith(('cygwin', 'win')):
    return 'win'
  if sys.platform == 'darwin':
    return 'mac'
  return 'linux'


# Runs true if the currently executing python interpreter is running under
# Rosetta. I.e., python3 is an x64 executable and we're on an arm64 Mac.
def is_rosetta():
  if platform.system() == 'Darwin':
    proc = subprocess.Popen(['sysctl', '-in', 'sysctl.proc_translated'],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.STDOUT)
    output, _ = proc.communicate()
    return output.decode('utf-8').strip() == '1'
  return False


# Returns the host machine CPU architecture.
def get_host_cpu():
  # If gn itself is running under Rosetta on an arm64 Mac, platform.machine()
  # will return x86_64; instead return the underlying host architecture.
  if is_rosetta():
    return 'arm64'
  machine = platform.machine()
  if machine in ['aarch64', 'arm64', 'ARM64']:
    return 'arm64'
  if machine in ['x86_64', 'AMD64', 'x64']:
    return 'x64'
  if machine in ['i686', 'i386', 'x86']:
    return 'x86'
  raise Exception('Unknown CPU architecture: %s' % machine)


# Returns the target CPU architecture.
#
# For macOS host builds where --mac-cpu is specified, returns that value.
# For windows host builds where --windows-cpu is specified, returns that value.
# For all other host builds, assumes 'x64'.
def get_target_cpu(args):
  if args.target_os == 'android':
    return args.android_cpu
  if args.target_os == 'ios':
    if args.simulator:
      return args.simulator_cpu
    return 'arm64'
  if args.target_os == 'mac':
    return args.mac_cpu
  if args.target_os == 'linux':
    return args.linux_cpu
  if args.target_os == 'fuchsia':
    return args.fuchsia_cpu
  if args.target_os == 'wasm':
    return 'wasm'
  if args.target_os == 'win':
    return args.windows_cpu

  # Host build. Default to x64 unless overridden.
  if get_host_os() == 'mac' and args.mac_cpu:
    return args.mac_cpu
  if get_host_os() == 'win' and args.windows_cpu:
    return args.windows_cpu

  return 'x64'


def buildtools_dir():
  host_os = get_host_os()
  host_cpu = get_host_cpu()
  if host_os == 'win':
    host_os = 'windows'
  return '%s-%s' % (host_os, host_cpu)


def get_dart_path():
  new_dart_path = os.path.join(SRC_ROOT, 'flutter', 'third_party', 'dart')
  if os.path.exists(new_dart_path):
    return new_dart_path
  return os.path.join(SRC_ROOT, 'third_party', 'dart')


def setup_rbe(args):
  rbe_gn_args = {}
  # RBE is default-off. If it is not asked for, then silently keep all default
  # flag values.
  if not args.rbe:
    return rbe_gn_args

  rbe_gn_args['use_rbe'] = True

  # When running in CI, the recipes use their own rbe install, and take
  # care of starting and stopping the compiler proxy.
  running_on_luci = os.environ.get('LUCI_CONTEXT') is not None

  # The default is 'racing', which eagerly attempts to use the local machine
  # to run build actions. This is not a performance improvement in CI where the
  # 'local machine' is nearly always a VM anyway, and is not any more powerful
  # than the 'remote' RBE workers. Only  attempt a 'local' build if the remote
  # worker times-out or otherwise fails. See also docs on RbeExecStrategy in the
  # engine_build_config package under tools/pkg.
  if running_on_luci:
    rbe_gn_args['rbe_exec_strategy'] = 'remote_local_fallback'

  if args.rbe_server_address:
    rbe_gn_args['rbe_server_address'] = args.rbe_server_address
  if args.rbe_exec_strategy:
    rbe_gn_args['rbe_exec_strategy'] = args.rbe_exec_strategy
  if args.rbe_dial_timeout:
    rbe_gn_args['rbe_dial_timeout'] = args.rbe_dial_timeout
  if args.rbe_platform:
    rbe_gn_args['rbe_platform'] = args.rbe_platform

  rbe_gn_args['rbe_dir'] = os.path.join(
      SRC_ROOT, 'flutter', 'buildtools', buildtools_dir(), 'reclient'
  )

  rbe_gn_args['rbe_cfg'] = os.path.join(
      SRC_ROOT, 'flutter', 'build', 'rbe', 'rewrapper-' + buildtools_dir() + '.cfg'
  )

  if sys.platform == 'darwin':
    if (not running_on_luci or args.xcode_symlinks or
        os.getenv('FLUTTER_GOMA_CREATE_XCODE_SYMLINKS', '0') == '1'):
      rbe_gn_args['create_xcode_symlinks'] = True

  return rbe_gn_args


# Find the locations of the macOS and iOS SDKs under flutter/prebuilts.
def setup_apple_sdks():
  sdks_gn_args = {}

  # These are needed on a macOS host regardless of target.
  # This value should agree with the 'mac_sdk_min' value in the DEPS file.
  sdks_gn_args['mac_sdk_min'] = '10.14'

  # The MACOSX_DEPLOYMENT_TARGET variable used when compiling.
  # Must be of the form x.x.x for Info.plist files.
  sdks_gn_args['mac_deployment_target'] = '10.14.0'

  running_on_luci = os.environ.get('LUCI_CONTEXT') is not None
  if running_on_luci:
    return sdks_gn_args

  prefixes = [
      ('MacOSX', 'mac_sdk_path'),
      ('iPhoneOS', 'ios_device_sdk_path'),
      ('iPhoneSimulator', 'ios_simulator_sdk_path'),
  ]
  sdks_path = os.path.join(SRC_ROOT, 'flutter', 'prebuilts', 'SDKs')
  sdk_entries = os.listdir(sdks_path)
  for (prefix, arg) in prefixes:
    for entry in sdk_entries:
      if entry.startswith(prefix):
        sdks_gn_args[arg] = os.path.join(sdks_path, entry)
  return sdks_gn_args


def get_repository_version(repository):
  'Returns the Git HEAD for the supplied repository path as a string.'
  if not os.path.exists(repository):
    raise IOError('path does not exist')

  git = 'git'
  if sys.platform.startswith(('cygwin', 'win')):
    git = 'git.bat'
  version = subprocess.check_output([
      git,
      '-C',
      repository,
      'rev-parse',
      'HEAD',
  ])

  return str(version.strip(), 'utf-8')


def setup_git_versions():
  revision_args = {}

  engine_path = os.path.join(SRC_ROOT, 'flutter')
  revision_args['engine_version'] = get_repository_version(engine_path)

  skia_path = os.path.join(SRC_ROOT, 'flutter', 'third_party', 'skia')
  revision_args['skia_version'] = get_repository_version(skia_path)

  revision_args['dart_version'] = get_repository_version(get_dart_path())

  return revision_args


# pylint: disable=line-too-long
# See https://docs.microsoft.com/en-us/windows/win32/api/sysinfoapi/nf-sysinfoapi-globalmemorystatusex
# and https://docs.microsoft.com/en-us/windows/win32/api/sysinfoapi/ns-sysinfoapi-memorystatusex
# pylint: enable=line-too-long
class MEMORYSTATUSEX(ctypes.Structure):
  _fields_ = [
      ('dwLength', ctypes.c_ulong),
      ('dwMemoryLoad', ctypes.c_ulong),
      ('ullTotalPhys', ctypes.c_ulonglong),
      ('ullAvailPhys', ctypes.c_ulonglong),
      ('ullTotalPageFile', ctypes.c_ulonglong),
      ('ullAvailPageFile', ctypes.c_ulonglong),
      ('ullTotalVirtual', ctypes.c_ulonglong),
      ('ullAvailVirtual', ctypes.c_ulonglong),
      ('sullAvailExtendedVirtual', ctypes.c_ulonglong),
  ]


def get_total_memory():
  if sys.platform in ('win32', 'cygwin'):
    stat = MEMORYSTATUSEX(dwLength=ctypes.sizeof(MEMORYSTATUSEX))
    success = ctypes.windll.kernel32.GlobalMemoryStatusEx(ctypes.byref(stat))
    return stat.ullTotalPhys if success else 0
  if sys.platform.startswith('linux'):
    if os.path.exists('/proc/meminfo'):
      with open('/proc/meminfo') as meminfo:
        memtotal_re = re.compile(r'^MemTotal:\s*(\d*)\s*kB')
        for line in meminfo:
          match = memtotal_re.match(line)
          if match:
            return float(match.group(1)) * 2**10
  if sys.platform == 'darwin':
    try:
      return int(subprocess.check_output(['sysctl', '-n', 'hw.memsize']))
    except:  # pylint: disable=bare-except
      return 0
  return 0


def parse_size(string):
  units = {'B': 1, 'KB': 2**10, 'MB': 2**20, 'GB': 2**30, 'TB': 2**40}
  i = next(i for (i, c) in enumerate(string) if not c.isdigit())
  number = string[:i].strip()
  unit = string[i:].strip()
  return int(float(number) * units[unit])


def get_concurrent_jobs(reserve_memory, memory_per_job):
  # reserve_memory = 2**30
  total_memory = get_total_memory()
  # Ensure the total memory used in the calculation below is at least 0
  mem_total_bytes = max(0, total_memory - parse_size(reserve_memory))
  # Ensure the number of cpus used in the calculation below is at least 1
  try:
    cpu_cap = multiprocessing.cpu_count()
  except:  # pylint: disable=bare-except
    cpu_cap = 1

  # Calculate the number of jobs that will fit in memory. Ensure the
  # value is at least 1.
  num_concurrent_jobs = int(max(1, mem_total_bytes / parse_size(memory_per_job)))
  # Cap the number of jobs by the number of cpus available.
  concurrent_jobs = min(num_concurrent_jobs, cpu_cap)

  return concurrent_jobs


def to_gn_args(args):
  if args.simulator:
    if args.target_os != 'ios':
      raise Exception('--simulator is only supported for iOS')

  if args.slimpeller and args.target_os != 'ios':
    raise Exception('--slimpeller is only supported for iOS.')

  runtime_mode = args.runtime_mode

  gn_args = {}

  gn_args['buildtools_path'] = '//flutter/buildtools'

  gn_args['is_debug'] = args.unoptimized

  gn_args.update(setup_git_versions())

  gn_args.update(setup_rbe(args))

  # If building for WASM, set the GN args using 'to_gn_wasm_args' as most
  # of the Flutter SDK specific arguments are unused.
  if args.target_os == 'wasm' or args.web:
    to_gn_wasm_args(args, gn_args)
    return gn_args

  gn_args['full_dart_sdk'] = args.full_dart_sdk

  if args.enable_unittests:
    # Ensure that Android and iOS are *not* used with --enable-unittests.
    # https://github.com/flutter/flutter/issues/132611
    if args.target_os == 'android' or args.target_os == 'ios':
      raise Exception(
          'Cannot use --enable-unittests with --target-os=android or ios. If ' +
          'you are trying to create an output directory to use for clangd ' +
          '(i.e. for VSCode integration), use a host build instead.\n\n' +
          'See https://github.com/flutter/flutter/wiki/' +
          'Setting-up-the-Engine-development-environment' + '#vscode-with-cc-intellisense-cc'
      )
    gn_args['enable_unittests'] = True
  if args.no_enable_unittests:
    gn_args['enable_unittests'] = False

  # Skia GN args.
  gn_args['skia_use_libpng_decode'] = True
  gn_args['skia_use_libpng_encode'] = True
  gn_args['skia_use_rust_png_decode'] = False
  gn_args['skia_use_rust_png_encode'] = False
  gn_args['skia_use_dng_sdk'] = False  # RAW image handling.
  gn_args['skia_enable_pdf'] = False  # PDF handling.
  gn_args['skia_use_x11'] = False  # Never add the X11 dependency (only takes effect on Linux).
  gn_args['skia_use_wuffs'] = True
  gn_args['skia_use_expat'] = True
  gn_args['skia_use_fontconfig'] = args.enable_fontconfig
  gn_args['skia_use_icu'] = True
  gn_args['is_official_build'] = True  # Disable Skia test utilities.
  gn_args['android_full_debug'] = args.target_os == 'android' and args.unoptimized
  if args.clang is None:
    gn_args['is_clang'] = True
  else:
    gn_args['is_clang'] = args.clang
  if args.target_os == 'ios' or args.slimpeller:
    gn_args['skia_enable_graphite'] = False
    gn_args['skia_enable_ganesh'] = False
    gn_args['slimpeller'] = True

  if args.target_os == 'android' or args.target_os == 'ios':
    gn_args['skia_gl_standard'] = 'gles'
  else:
    # We explicitly don't want to pick GL because we run GLES tests using SwiftShader.
    gn_args['skia_gl_standard'] = ''

  if not sys.platform.startswith(('cygwin', 'win')):
    gn_args['use_clang_static_analyzer'] = args.clang_static_analyzer

  gn_args['enable_coverage'] = args.coverage

  if args.operator_new_alignment is not None:
    gn_args['operator_new_alignment'] = args.operator_new_alignment

  enable_lto = args.lto
  if args.unoptimized:
    # There is no point in enabling LTO in unoptimized builds.
    enable_lto = False

  if not sys.platform.startswith('win'):
    # The GN arg is not available in the windows toolchain.
    gn_args['enable_lto'] = enable_lto

  # Set OS, CPU arch for host or target build.
  if is_host_build(args):
    gn_args['host_os'] = get_host_os()
    gn_args['host_cpu'] = get_host_cpu()
    gn_args['target_os'] = gn_args['host_os']
    gn_args['target_cpu'] = get_target_cpu(args)
    gn_args['dart_target_arch'] = gn_args['target_cpu']
  else:
    gn_args['target_os'] = args.target_os
    gn_args['target_cpu'] = get_target_cpu(args)
    gn_args['dart_target_arch'] = gn_args['target_cpu']

  if not args.build_engine_artifacts:
    gn_args['flutter_build_engine_artifacts'] = False

  # We cannot cross-compile for 32 bit arm on a Windows host. We work around
  # this by leaving 'target_cpu' and 'dart_target_arch' set to 'arm' so that
  # Dart tools such as gen_snapshot that are built for the host will correctly
  # target arm, but we hardcode the 'current_cpu' to always be the host arch
  # so that the GN build doesn't go looking for a Windows arm toolchain, which
  # does not exist. Further, we set the 'host_cpu' so that it shares the
  # bitwidth of the 32-bit arm target.
  if sys.platform.startswith(
      ('cygwin', 'win')) and args.target_os == 'android' and gn_args['target_cpu'] == 'arm':
    gn_args['host_cpu'] = 'x86'
    gn_args['current_cpu'] = 'x86'

  if gn_args['target_os'] == 'ios' or get_host_os() == 'mac':
    if gn_args['target_os'] == 'ios':
      gn_args['use_ios_simulator'] = args.simulator
    else:
      gn_args['use_ios_simulator'] = False
    gn_args['ios_use_simulator'] = gn_args['use_ios_simulator']
    gn_args.update(setup_apple_sdks())

  if args.dart_debug:
    gn_args['dart_debug'] = True

  if args.full_dart_debug:
    gn_args['dart_debug'] = True
    gn_args['dart_debug_optimization_level'] = '0'

  if args.dart_optimization_level:
    gn_args['dart_default_optimization_level'] = args.dart_optimization_level
  elif gn_args['target_os'] in ['android', 'ios']:
    gn_args['dart_default_optimization_level'] = '2'

  gn_args['flutter_use_fontconfig'] = args.enable_fontconfig
  gn_args['dart_component_kind'] = 'static_library'  # Always link Dart in statically.
  gn_args['embedder_for_target'] = args.embedder_for_target
  gn_args['dart_lib_export_symbols'] = False
  gn_args['flutter_runtime_mode'] = runtime_mode
  gn_args['dart_version_git_info'] = not args.no_dart_version_git_info

  gn_args['dart_lib_export_symbols'] = False
  if runtime_mode == 'debug':
    gn_args['dart_runtime_mode'] = 'develop'
  elif runtime_mode == 'jit_release':
    gn_args['dart_runtime_mode'] = 'release'
  else:
    gn_args['dart_runtime_mode'] = runtime_mode

  gn_args['concurrent_toolchain_jobs'] = get_concurrent_jobs('1GB', '100MB')

  # Hardcoding this avoids invoking a relatively expensive python script from
  # GN, but removes the ability to use git-worktrees in the Dart checkout from
  # within the Engine repo, but it's unlikely anyone is using that.
  gn_args['default_git_folder'] = os.path.join(get_dart_path(), '.git')

  # By default, the Dart GN build will invoke a relatively expensive python
  # script to calculate an SDK "hash" that ensures compatibility between the
  # Dart VM and the Dart front-end. This default hash calculation allows some
  # wiggle room in which a VM and kernel file will be compatible forwards and
  # backwards across a small range of git commits. Since Flutter doesn't need
  # the wiggle room, to avoid running the python script, we instead set the SDK
  # hash to the exact git commit.
  sdk_hash = gn_args['dart_version']
  gn_args['dart_sdk_verification_hash'] = sdk_hash if len(sdk_hash) <= 10 else sdk_hash[:10]

  # Desktop embeddings can have more dependencies than the engine library,
  # which can be problematic in some build environments (e.g., building on
  # Linux will bring in pkg-config dependencies at generation time). These
  # flags allow preventing those targets from being part of the build tree.
  gn_args['enable_desktop_embeddings'] = not args.disable_desktop_embeddings

  # Determine whether backtace support should be compiled in.
  if args.backtrace:
    gn_args['enable_backtrace'] = (
        args.target_os in ['linux', 'mac', 'win'] or
        args.target_os == 'ios' and runtime_mode == 'debug'
    )
  else:
    gn_args['enable_backtrace'] = False

  if args.allow_deprecated_api_calls:
    gn_args['allow_deprecated_api_calls'] = args.allow_deprecated_api_calls

  if args.target_os is None:
    if sys.platform.startswith(('cygwin', 'win')):
      gn_args['dart_use_fallback_root_certificates'] = True

  if args.target_sysroot:
    gn_args['target_sysroot'] = args.target_sysroot
    gn_args['custom_sysroot'] = args.target_sysroot

  if args.target_toolchain:
    gn_args['custom_toolchain'] = args.target_toolchain

  if args.target_triple:
    gn_args['custom_target_triple'] = args.target_triple

  # Enable Metal on iOS builds.
  if args.target_os == 'ios':
    gn_args['shell_enable_gl'] = False
    gn_args['skia_use_gl'] = False
    gn_args['shell_enable_metal'] = True
    gn_args['skia_use_metal'] = True
  else:
    gn_args['skia_use_gl'] = args.target_os != 'fuchsia'

  if sys.platform == 'darwin' and args.target_os not in ['android', 'fuchsia']:
    # OpenGL is deprecated on macOS > 10.11.
    # This is not necessarily needed but enabling this until we have a way to
    # build a macOS metal only shell and a gl only shell.
    gn_args['allow_deprecated_api_calls'] = True
    gn_args['skia_use_metal'] = True
    gn_args['shell_enable_metal'] = True

  # Enable Vulkan on all platforms except for iOS. This is just
  # to save on mobile binary size, as there's no reason the Vulkan embedder
  # features can't work on these platforms.
  if gn_args['target_os'] not in ['ios', 'mac']:
    gn_args['skia_use_vulkan'] = True
    gn_args['skia_use_vma'] = False
    gn_args['shell_enable_vulkan'] = True
    # Disable VMA's use of std::shared_mutex in environments where the
    # standard library doesn't support it.
    if args.target_os == 'ios' or sys.platform.startswith(('cygwin', 'win')):
      gn_args['disable_vma_stl_shared_mutex'] = True

  # We should not need a special case for x86, but this seems to introduce text relocations
  # even with -fPIC everywhere.
  # gn_args['enable_profiling'] = args.runtime_mode != 'release' and args.android_cpu != 'x86'

  # Make symbols visible in order to enable symbolization of unit test crash backtraces on Linux
  gn_args['disable_hidden_visibility'] = args.target_os == 'linux' and args.unoptimized

  if args.arm_float_abi:
    gn_args['arm_float_abi'] = args.arm_float_abi

  if args.dart_dynamic_modules:
    if not args.prebuilt_dart_sdk:
      print('--dart-dynamic-modules can only be used with --no-prebuilt-dart-sdk')
    gn_args['dart_dynamic_modules'] = True

  if args.no_enable_unittests:
    gn_args['dart_dynamic_modules'] = False

  # If we have a prebuilt for the Dart SDK for the target architecture, then
  # use it instead of building a new one.
  if args.prebuilt_dart_sdk:
    if can_use_prebuilt_dart(args):
      print(
          'Using prebuilt Dart SDK binary. If you are editing Dart sources '
          'and wish to compile the Dart SDK, set `--no-prebuilt-dart-sdk`.'
      )
      gn_args['flutter_prebuilt_dart_sdk'] = True
      gn_args['dart_sdk_output'] = 'built-dart-sdk'
    elif is_host_build(args):
      print(
          'Tried to download prebuilt Dart SDK but an appropriate version '
          'could not be found!'
      )
      print(
          'Either retry by running '
          'flutter/tools/download_dart_sdk.py manually or compile from '
          'source by setting `--no-prebuilt-dart-sdk` flag to tools/gn'
      )
  elif is_host_build(args):
    # If we are building the dart sdk in-tree, exclude the wasm-opt target, as
    # it doesn't build properly with our gn configuration.
    gn_args['dart_include_wasm_opt'] = False

    # dart_platform_sdk is only defined for host builds, linux arm host builds
    # specify target_os=linux.
    # dart_platform_sdk=True means exclude web-related files, e.g. dart2js,
    # dartdevc, web SDK kernel and source files.
    gn_args['dart_platform_sdk'] = not args.full_dart_sdk

  if args.build_glfw_shell is not None:
    gn_args['build_glfw_shell'] = args.build_glfw_shell

  if args.build_embedder_examples is not None:
    gn_args['build_embedder_examples'] = args.build_embedder_examples

  if args.stripped is True:
    gn_args['stripped_symbols'] = True
  elif args.stripped is False:
    gn_args['stripped_symbols'] = False
  else:
    gn_args['stripped_symbols'] = args.target_os != 'android'

  if args.msan:
    gn_args['is_msan'] = True

  if args.asan:
    gn_args['is_asan'] = True

  if args.tsan:
    gn_args['is_tsan'] = True

  if args.lsan:
    gn_args['is_lsan'] = True

  if args.ubsan:
    gn_args['is_ubsan'] = True

  if args.fstack_protector:
    gn_args['use_fstack_protector'] = True

  enable_vulkan_validation = args.enable_vulkan_validation_layers

  # Enable Vulkan validation layer automatically on debug builds for arm64.
  if args.unoptimized and args.target_os == 'android' and args.android_cpu == 'arm64':
    enable_vulkan_validation = True

  if enable_vulkan_validation:
    gn_args['enable_vulkan_validation_layers'] = True
    gn_args['impeller_enable_vulkan_validation_layers'] = True

  # Enable pointer compression on 64-bit mobile targets. iOS is excluded due to
  # its inability to allocate address space without allocating memory.
  if args.target_os in ['android'] and gn_args['target_cpu'] in ['x64', 'arm64']:
    gn_args['dart_use_compressed_pointers'] = True

  if args.target_os == 'fuchsia':
    gn_args['gn_configs_path'] = '//flutter/build/config/fuchsia/gn_configs.gni'
    gn_args['fuchsia_gn_sdk'] = '//flutter/tools/fuchsia/gn-sdk'

  # Flags for Dart features:
  if args.use_mallinfo2:
    gn_args['dart_use_mallinfo2'] = args.use_mallinfo2

  malioc_path = args.malioc_path
  if not malioc_path:
    malioc_path = os.environ.get('MALIOC_PATH')
  if malioc_path:
    gn_args['impeller_malioc_path'] = malioc_path

  if args.use_glfw_swiftshader:
    if get_host_os() == 'mac':
      gn_args['glfw_vulkan_library'] = r'\"libvulkan.dylib\"'
    elif get_host_os() == 'linux':
      gn_args['glfw_vulkan_library'] = r'\"libvulkan.so.1\"'

  # ANGLE is exclusively used for:
  #  - Windows at runtime
  #  - Non-fuchsia host unit tests (is_host_build evaluates to false).
  # Setting these variables creates warnings otherwise.
  # If we add ANGLE usage on other platforms, include them here.
  # There is a special case for Android on Windows because there we _only_ build
  # gen_snapshot, but the build defines otherwise make it look like the build is
  # for a host Windows build and make GN think we will be building ANGLE.
  # Angle is not used on Mac hosts as there are no tests for the OpenGL backend.
  if is_host_build(args) or (args.target_os == 'android' and get_host_os() == 'win'):
    # Don't include git commit information.
    gn_args['angle_enable_commit_id'] = False
    # Do not build unnecessary parts of the ANGLE tree.
    gn_args['angle_build_all'] = False
    gn_args['angle_has_astc_encoder'] = False
    # Force ANGLE context checks on Windows to prevent crashes.
    # TODO(loic-sharma): Remove this once ANGLE crashes have been fixed.
    # https://github.com/flutter/flutter/issues/114107
    if get_host_os() == 'win':
      gn_args['angle_force_context_check_every_call'] = True
    if get_host_os() == 'mac':
      gn_args['angle_enable_metal'] = True
      gn_args['angle_enable_gl'] = False
      gn_args['angle_enable_vulkan'] = False

    # ANGLE and SwiftShader share build flags to enable X11 and Wayland,
    # but we only need these enabled for SwiftShader.
    gn_args['angle_use_x11'] = False
    gn_args['angle_use_wayland'] = False

    # Requires RTTI. We may want to build this in debug modes, punting on that
    # for now.
    gn_args['angle_enable_vulkan_validation_layers'] = False
    gn_args['angle_vulkan_headers_dir'] = '//flutter/third_party/vulkan-deps/vulkan-headers/src'
    gn_args['angle_vulkan_loader_dir'] = '//flutter/third_party/vulkan-deps/vulkan-loader/src'
    gn_args['angle_vulkan_tools_dir'] = '//flutter/third_party/vulkan-deps/vulkan-tools/src'

  if args.darwin_extension_safe:
    gn_args['darwin_extension_safe'] = True

  return gn_args


# When building for WASM, almost all GN args used in the Flutter SDK
# build are unused. This method is used instead.
def to_gn_wasm_args(args, gn_args):
  gn_args['is_official_build'] = True
  gn_args['is_component_build'] = False
  gn_args['use_clang_static_analyzer'] = False
  gn_args['is_clang'] = True
  gn_args['target_os'] = 'wasm'
  gn_args['target_cpu'] = 'wasm'
  gn_args['wasm_use_dwarf'] = args.wasm_use_dwarf
  gn_args['skia_use_angle'] = False
  gn_args['skia_use_dng_sdk'] = False
  gn_args['skia_use_expat'] = False
  gn_args['skia_use_vulkan'] = False
  gn_args['skia_use_webgpu'] = False
  gn_args['skia_use_libheif'] = False
  gn_args['skia_use_libjpeg_turbo_encode'] = False
  gn_args['skia_use_no_jpeg_encode'] = True
  # TODO(yjbanov): https://github.com/flutter/flutter/issues/122759
  # Remove this and implement it through Canvas2d.
  gn_args['skia_use_libpng_encode'] = True
  gn_args['skia_use_libwebp_encode'] = False
  gn_args['skia_use_no_webp_encode'] = True
  gn_args['skia_use_lua'] = False
  gn_args['skia_use_wuffs'] = True
  gn_args['skia_use_zlib'] = True
  gn_args['skia_gl_standard'] = 'webgl'
  gn_args['skia_enable_ganesh'] = True
  gn_args['skia_enable_skottie'] = False
  gn_args['icu_use_data_file'] = False
  gn_args['skia_use_freetype'] = True
  gn_args['flutter_use_freetype_woff2'] = True
  gn_args['skia_use_harfbuzz'] = True
  gn_args['skia_use_fontconfig'] = False
  gn_args['skia_use_libheif'] = False
  gn_args['skia_enable_fontmgr_custom_directory'] = False
  gn_args['skia_enable_fontmgr_custom_embedded'] = True
  gn_args['skia_enable_fontmgr_custom_empty'] = True
  gn_args['skia_enable_skshaper'] = True
  gn_args['skia_enable_skparagraph'] = True
  gn_args['skia_canvaskit_force_tracing'] = False
  gn_args['skia_canvaskit_enable_skp_serialization'] = True
  gn_args['skia_canvaskit_enable_effects_deserialization'] = False
  gn_args['skia_canvaskit_include_viewer'] = False
  gn_args['skia_canvaskit_enable_pathops'] = True
  gn_args['skia_canvaskit_enable_rt_shader'] = True
  gn_args['skia_canvaskit_enable_matrix_helper'] = False
  gn_args['skia_canvaskit_enable_canvas_bindings'] = False
  gn_args['skia_canvaskit_enable_font'] = True
  gn_args['skia_canvaskit_enable_embedded_font'] = False
  gn_args['skia_canvaskit_enable_alias_font'] = True
  gn_args['skia_canvaskit_legacy_draw_vertices_blend_mode'] = False
  gn_args['skia_canvaskit_enable_debugger'] = False
  gn_args['skia_canvaskit_enable_paragraph'] = True
  gn_args['skia_canvaskit_enable_webgl'] = True
  gn_args['skia_canvaskit_enable_webgpu'] = False
  gn_args['skia_canvaskit_profile_build'] = args.runtime_mode == 'profile'
  gn_args['flutter_prebuilt_dart_sdk'] = True


def run_impeller_cmake(args):
  impeller_cmake_dir = os.path.join('flutter', 'third_party', 'impeller-cmake-example')
  if not os.path.isdir(os.path.join(SRC_ROOT, impeller_cmake_dir)):
    print('The Impeller cmake example directory "{}" does not exist'.format(impeller_cmake_dir))
    return 1
  cmake_cmd = [
      'python3',
      os.path.join(SRC_ROOT, 'flutter', 'ci', 'impeller_cmake_build_test.py'),
      '--path',
      impeller_cmake_dir,
      '--cmake',
  ]
  if args.xcode_symlinks:
    cmake_cmd = cmake_cmd + ['--xcode-symlinks']
  try:
    cmake_call_result = subprocess.call(cmake_cmd, cwd=SRC_ROOT)
  except subprocess.CalledProcessError as exc:
    print('Failed to generate cmake files: ', exc.returncode, exc.output)
    return 1
  return cmake_call_result


def parse_args(args):
  args = args[1:]
  parser = argparse.ArgumentParser(description='A script to run `gn gen`.')

  parser.add_argument('--unoptimized', default=False, action='store_true')

  parser.add_argument(
      '--enable-unittests',
      action='store_true',
      default=False,
      help='Force enable building unit test binaries.'
  )
  parser.add_argument(
      '--no-enable-unittests',
      default=False,
      action='store_true',
      help='Force disable building unit test binaries.'
  )

  parser.add_argument(
      '--runtime-mode',
      type=str,
      choices=['debug', 'profile', 'release', 'jit_release'],
      default='debug'
  )
  parser.add_argument(
      '--dart-debug',
      default=False,
      action='store_true',
      help='Enables assertions in the Dart VM. Does not affect optimization '
      'levels. If you need to disable optimizations in Dart, use '
      '--full-dart-debug'
  )
  parser.add_argument(
      '--no-dart-version-git-info',
      default=False,
      action='store_true',
      help='Set by default; if unset, turns off the dart SDK git hash check'
  )
  parser.add_argument(
      '--full-dart-debug',
      default=False,
      action='store_true',
      help='Implies --dart-debug and also disables optimizations in the Dart '
      'VM making it easier to step through VM code in the debugger.'
  )

  parser.add_argument(
      '--dart-optimization-level',
      type=str,
      help='The default optimization level for the Dart VM runtime.',
  )

  parser.add_argument(
      '--target-os', type=str, choices=['android', 'ios', 'mac', 'linux', 'fuchsia', 'wasm', 'win']
  )
  parser.add_argument('--android', dest='target_os', action='store_const', const='android')
  parser.add_argument(
      '--android-cpu', type=str, choices=['arm', 'x64', 'x86', 'arm64'], default='arm'
  )
  parser.add_argument('--ios', dest='target_os', action='store_const', const='ios')
  parser.add_argument('--mac', dest='target_os', action='store_const', const='mac')
  parser.add_argument('--mac-cpu', type=str, choices=['x64', 'arm64'], default='x64')
  parser.add_argument('--simulator', action='store_true', default=False)
  parser.add_argument('--linux', dest='target_os', action='store_const', const='linux')
  parser.add_argument('--fuchsia', dest='target_os', action='store_const', const='fuchsia')
  parser.add_argument('--wasm', dest='target_os', action='store_const', const='wasm')
  parser.add_argument(
      '--wasm-use-dwarf',
      action='store_true',
      default=False,
      help='Embed dwarf debugging info in the output module instead of using '
      'sourcemap files.'
  )
  parser.add_argument('--web', action='store_true', default=False)
  parser.add_argument('--windows', dest='target_os', action='store_const', const='win')

  parser.add_argument('--linux-cpu', type=str, choices=['x64', 'x86', 'arm64', 'arm'])
  parser.add_argument('--fuchsia-cpu', type=str, choices=['x64', 'arm64'], default='x64')
  parser.add_argument('--windows-cpu', type=str, choices=['x64', 'arm64', 'x86'], default='x64')
  parser.add_argument('--simulator-cpu', type=str, choices=['x64', 'arm64'], default='x64')
  parser.add_argument('--arm-float-abi', type=str, choices=['hard', 'soft', 'softfp'])

  # Whether to compile in backtrace support.
  # Available for Windows and POSIX platforms whose libc includes execinfo.h.
  # MUSL doesn't include execinfo.h should be build with --no-backtrace.
  parser.add_argument(
      '--backtrace',
      default=True,
      action='store_true',
      help='Whether OS support exists for collecting backtraces.'
  )
  parser.add_argument('--no-backtrace', dest='backtrace', action='store_false')

  parser.add_argument(
      '--build-engine-artifacts',
      default=True,
      action='store_true',
      help='Build the host-side development artifacts.'
  )
  parser.add_argument(
      '--no-build-engine-artifacts',
      dest='build_engine_artifacts',
      action='store_false',
      help='Do not build the host-side development artifacts.'
  )

  parser.add_argument('--rbe', default=None, action='store_true')
  parser.add_argument('--no-rbe', dest='rbe', action='store_false')
  parser.add_argument(
      '--rbe-server-address', default=None, type=str, help='The reproxy serveraddress'
  )
  parser.add_argument(
      '--rbe-exec-strategy',
      default=None,
      type=str,
      help='The RBE execution strategy.',
      choices=['local', 'remote', 'remote_local_fallback', 'racing']
  )
  parser.add_argument(
      '--rbe-dial-timeout',
      default=None,
      type=str,
      help='The timeout for connecting to the local reproxy server.',
  )
  parser.add_argument(
      '--rbe-platform',
      default=None,
      type=str,
      help='The RBE "platform" string. This is used to identify remote platform '
      'settings like the docker image to use to run the command.'
  )
  parser.add_argument(
      '--rbe-dir', default=None, type=str, help='The location of the reclient binaries.'
  )

  parser.add_argument('--goma', default=None, action='store_true')
  parser.add_argument('--no-goma', dest='goma', action='store_false')
  parser.add_argument(
      '--xcode-symlinks',
      action='store_true',
      help='Set to true for builds targeting macOS or iOS when using RBE. If '
      'set, symlinks to the Xcode provided sysroot and SDKs will be '
      'created in a generated folder, which will avoid potential backend '
      'errors in Fuchsia RBE. Instead of specifying the flag on each '
      'invocation the FLUTTER_GOMA_CREATE_XCODE_SYMLINKS environment '
      'variable may be set to 1 to achieve the same effect.'
  )
  parser.add_argument(
      '--no-xcode-symlinks', dest='xcode_symlinks', default=False, action='store_false'
  )
  parser.add_argument(
      '--depot-tools',
      default='~/depot_tools',
      type=str,
      help='Depot tools provides an alternative location for gomacc in ' +
      '/path/to/depot_tools/.cipd_bin'
  )

  parser.add_argument('--lto', default=True, action='store_true')
  parser.add_argument('--no-lto', dest='lto', action='store_false')

  parser.add_argument('--clang', action='store_const', const=True)
  parser.add_argument('--no-clang', dest='clang', action='store_const', const=False)

  parser.add_argument('--clang-static-analyzer', default=False, action='store_true')
  parser.add_argument(
      '--no-clang-static-analyzer', dest='clang_static_analyzer', action='store_false'
  )

  parser.add_argument('--target-sysroot', type=str)
  parser.add_argument('--target-toolchain', type=str)
  parser.add_argument('--target-triple', type=str)
  parser.add_argument(
      '--operator-new-alignment', dest='operator_new_alignment', type=str, default=None
  )

  parser.add_argument('--macos-enable-metal', action='store_true', default=False)
  parser.add_argument('--enable-vulkan', action='store_true', default=False)

  parser.add_argument('--enable-fontconfig', action='store_true', default=False)
  parser.add_argument('--enable-vulkan-validation-layers', action='store_true', default=False)

  parser.add_argument(
      '--embedder-for-target', dest='embedder_for_target', action='store_true', default=False
  )

  parser.add_argument('--coverage', default=False, action='store_true')

  parser.add_argument(
      '--out-dir',
      default='',
      type=str,
      help='Root out directory. Target specific gn files will be generated in ${out-dir}/'
  )
  parser.add_argument(
      '--target-dir',
      default='',
      type=str,
      help='Use the specified name for target out directory. By default this tool determines one.'
  )

  parser.add_argument(
      '--full-dart-sdk',
      default=False,
      action='store_true',
      help='include trained dart2js and dartdevc snapshots. Enable only on steps that create an SDK'
  )
  parser.add_argument('--no-full-dart-sdk', dest='full_dart_sdk', action='store_false')

  parser.add_argument(
      '--build-canvaskit',
      default=False,
      action='store_true',
      help='build canvaskit from source (DEPRECATED: use ninja targets to select what to build)'
  )

  parser.add_argument(
      '--ide',
      default='',
      type=str,
      help='The IDE files to generate using GN. Use `gn gen help` and look for the --ide flag to' +
      ' see supported IDEs. If this flag is not specified, a platform specific default is selected.'
  )

  parser.add_argument(
      '--allow-deprecated-api-calls',
      action='store_true',
      default=False,
      help='Turns off warnings about the usage of deprecated APIs.'
  )

  parser.add_argument(
      '--disable-desktop-embeddings',
      default=False,
      action='store_true',
      help='Do not include desktop embeddings in the build.'
  )
  parser.add_argument(
      '--build-glfw-shell',
      action='store_const',
      const=True,
      help='Build the GLFW shell on supported platforms where it is not built by default.'
  )
  parser.add_argument(
      '--no-build-glfw-shell',
      dest='build_glfw_shell',
      action='store_const',
      const=False,
      help='Do not build the GLFW shell on platforms where it is built by default.'
  )
  parser.add_argument(
      '--build-embedder-examples',
      action='store_const',
      const=True,
      help='Build the example embedders using the Embedder API.'
  )
  parser.add_argument(
      '--no-build-embedder-examples',
      dest='build_embedder_examples',
      action='store_const',
      const=False,
      help='Do not build the example embedders using the Embedder API.'
  )

  parser.add_argument(
      '--stripped',
      action='store_true',
      help='Strip debug symbols from the output. This defaults to true and has no effect on iOS.'
  )
  parser.add_argument('--no-stripped', dest='stripped', action='store_false')

  parser.add_argument(
      '--prebuilt-dart-sdk',
      default=True,
      action='store_true',
      help='Whether to use a prebuilt Dart SDK instead of building one. This defaults to ' +
      'true and is enabled on CI.'
  )
  parser.add_argument('--no-prebuilt-dart-sdk', dest='prebuilt_dart_sdk', action='store_false')

  # Flags for Dart features.
  parser.add_argument(
      '--use-mallinfo2',
      dest='use_mallinfo2',
      default=False,
      action='store_true',
      help='Use mallinfo2 to collect malloc stats.'
  )

  parser.add_argument(
      '--dart-dynamic-modules',
      default=False,
      action='store_true',
      help='Whether to include the Dart dynamic module interpreter in the Dart '
      'sdk build. Must be used with --no-prebuilt-dart-sdk.'
  )
  parser.add_argument(
      '--no-dart-dynamic-modules', dest='dart-dynamic-modules', action='store_false'
  )

  # Impeller flags.
  parser.add_argument(
      '--enable-impeller-3d',
      default=False,
      action='store_true',
      help='Enables experimental 3d support.'
  )

  parser.add_argument('--malioc-path', type=str, help='The path to the malioc tool.')

  parser.add_argument(
      '--impeller-cmake-example',
      default=False,
      action='store_true',
      help='Do not run GN. Instead configure the Impeller cmake example build.',
  )

  parser.add_argument(
      '--slimpeller',
      default=False,
      action='store_true',
      help='Enable optimizations that attempt to reduce binary size of the ' +
      'Flutter engine by assuming only the Impeller rendering engine is supported.' +
      'See [go/slimpeller-dashboard](https://github.com/orgs/flutter/projects/21) for details.'
  )

  # Sanitizers.
  parser.add_argument('--asan', default=False, action='store_true')
  parser.add_argument('--lsan', default=False, action='store_true')
  parser.add_argument('--msan', default=False, action='store_true')
  parser.add_argument('--tsan', default=False, action='store_true')
  parser.add_argument('--ubsan', default=False, action='store_true')

  parser.add_argument(
      '--fstack-protector',
      default=False,
      action='store_true',
      help='Whether the -fstack-protector flag should be passed unconditionally.'
  )

  parser.add_argument(
      '--darwin-extension-safe',
      default=False,
      action='store_true',
      help='Whether the produced Flutter.framework is app extension safe. Only for iOS.'
  )

  # Verbose output.
  parser.add_argument('--verbose', default=False, action='store_true')

  parser.add_argument(
      '--gn-args',
      action='append',
      help='Additional gn args to be passed to gn. If you '
      'need to use this, it should probably be another switch '
      'in //flutter/tools/gn.',
  )

  parser.add_argument(
      '--use-glfw-swiftshader',
      default=False,
      action='store_true',
      help='Forces glfw to use swiftshader.',
  )

  return parser.parse_args(args)


def validate_args(args):
  valid = True
  if args.simulator:
    if args.mac_cpu != 'x64':
      print(
          'Specified a non-default mac-cpu for a simulator build. Did you mean '
          'to use `--simulator-cpu`?'
      )
      valid = False

  if not valid:
    sys.exit(-1)


def main(argv):
  args = parse_args(argv)
  validate_args(args)

  if args.impeller_cmake_example:
    return run_impeller_cmake(args)

  exe = '.exe' if sys.platform.startswith(('cygwin', 'win')) else ''

  command = [
      '%s/flutter/third_party/gn/gn%s' % (SRC_ROOT, exe),
      'gen',
      '--check',
      '--export-compile-commands',
  ]

  if not args.web:
    if args.ide != '':
      command.append('--ide=%s' % args.ide)
    elif sys.platform == 'darwin':
      # On the Mac, generate an Xcode project by default.
      command.append('--ide=xcode')
      command.append('--xcode-project=flutter_engine')
      command.append('--xcode-build-system=new')
    elif sys.platform.startswith('win'):
      # On Windows, generate a Visual Studio project.
      command.append('--ide=vs')
      command.append('--ninja-executable=ninja')

  command.append('--export-compile-commands=default')

  gn_args = to_command_line(to_gn_args(args))
  gn_args.extend(args.gn_args or [])
  out_dir = get_out_dir(args)
  command.append(out_dir)
  command.append('--args=%s' % ' '.join(gn_args))
  command.append('--tracelog=%s/gn_trace.json' % out_dir)

  if args.verbose:
    command.append('-v')

  print('Generating GN files in: %s' % out_dir)
  try:
    gn_call_result = subprocess.call(command, cwd=SRC_ROOT)
  except subprocess.CalledProcessError as exc:
    print('Failed to generate gn files: ', exc.returncode, exc.output)
    sys.exit(1)

  return gn_call_result


if __name__ == '__main__':
  sys.exit(main(sys.argv))
