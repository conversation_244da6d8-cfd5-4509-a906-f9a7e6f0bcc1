<<skip until matching line>>
══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════
The following message was thrown running a test:
Who lives, who dies, who tells your story\?

When the exception was thrown, this was the stack:
#0      main.<anonymous closure> \(.+[/\\]dev[/\\]automated_tests[/\\]integration_test[/\\]exception_handling_test\.dart:10:5\)
<<skip until matching line>>
The test description was:
Exception handling in test harness - string
════════════════════════════════════════════════════════════════════════════════════════════════════
\d\d:\d\d \+0 -1: Exception handling in test harness - string \[E\]
  Test failed. See exception logs above.
  The test description was: Exception handling in test harness - string
<<skip until matching line>>
══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════
The following assertion was thrown running a test:
Who lives, who dies, who tells your story\?

When the exception was thrown, this was the stack:
#0      main.<anonymous closure> \(.+[/\\]dev[/\\]automated_tests[/\\]integration_test[/\\]exception_handling_test\.dart:13:5\)
<<skip until matching line>>
The test description was:
  Exception handling in test harness - FlutterError
════════════════════════════════════════════════════════════════════════════════════════════════════
\d\d:\d\d \+0 -2: Exception handling in test harness - FlutterError \[E\]
  Test failed. See exception logs above.
  The test description was: Exception handling in test harness - FlutterError
<<skip until matching line>>
══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════
The following message was thrown running a test:
Who lives, who dies, who tells your story\?

When the exception was thrown, this was the stack:
#4      main.<anonymous closure> \(.+[/\\]dev[/\\]automated_tests[/\\]integration_test[/\\]exception_handling_test\.dart:18:5\)
<<skip until matching line>>
The test description was:
  Exception handling in test harness - uncaught Future error
════════════════════════════════════════════════════════════════════════════════════════════════════
\d\d:\d\d \+0 -3: Exception handling in test harness - uncaught Future error \[E\]
  Test failed. See exception logs above.
  The test description was: Exception handling in test harness - uncaught Future error
.*(TODO\(jiahaog\): Remove the next line once https://github.com/flutter/flutter/issues/81521 is fixed)?
<<skip until matching line>>
.*..:.. \+0 -3: Some tests failed\. *
