// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

namespace impeller.fb;

enum Stage:byte {
  kVertex,
  kFragment,
  kCompute,
}

table ShaderBlob {
  stage: Stage;
  name: string;
  mapping: [ubyte];
}

table ShaderArchive {
  items: [ShaderBlob];
}

root_type ShaderArchive;
file_identifier "SHAR";
