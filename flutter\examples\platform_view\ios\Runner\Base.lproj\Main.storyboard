<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="12120" systemVersion="16E195" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="12088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Flutter View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="FlutterViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="y3c-jy-aDJ"/>
                        <viewControllerLayoutGuide type="bottom" id="wfy-db-euE"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
        </scene>
        <!--Platform View Controller-->
        <scene sceneID="qxR-Ib-h1B">
            <objects>
                <viewController storyboardIdentifier="PlatformView" id="l7k-TM-87m" customClass="PlatformViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="aNK-cG-KHz"/>
                        <viewControllerLayoutGuide type="bottom" id="KQ0-mI-uQk"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="9Pa-2h-BJY" userLabel="ParentView">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="O5w-dx-5ca">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" restorationIdentifier="PVController" translatesAutoresizingMaskIntoConstraints="NO" id="PVo-OY-IKE" userLabel="Top">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="597"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Button tapped 0 times." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qaB-rs-mWp">
                                                <rect key="frame" x="95.5" y="288" width="185" height="21.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="18"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="K7X-Ru-0N7">
                                                <rect key="frame" x="94" y="325.5" width="188" height="38"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="188" id="W0o-wA-IUz"/>
                                                    <constraint firstAttribute="height" constant="38" id="tEK-W1-2uy"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <state key="normal" title="Continue in Flutter view">
                                                    <color key="titleColor" cocoaTouchSystemColor="darkTextColor"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="type">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="switchToFlutterView:" destination="l7k-TM-87m" eventType="touchUpInside" id="Q9G-Ua-HnS"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="qaB-rs-mWp" firstAttribute="centerX" secondItem="PVo-OY-IKE" secondAttribute="centerX" id="3XU-jp-cMf"/>
                                            <constraint firstItem="qaB-rs-mWp" firstAttribute="centerY" secondItem="PVo-OY-IKE" secondAttribute="centerY" id="GEk-gm-nLl"/>
                                            <constraint firstItem="K7X-Ru-0N7" firstAttribute="top" secondItem="qaB-rs-mWp" secondAttribute="bottom" constant="16" id="Gwc-TH-45z"/>
                                            <constraint firstItem="K7X-Ru-0N7" firstAttribute="centerX" secondItem="qaB-rs-mWp" secondAttribute="centerX" id="QqH-9r-NM7"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" restorationIdentifier="Bottom" translatesAutoresizingMaskIntoConstraints="NO" id="9iX-D6-aiy" userLabel="Bottom">
                                        <rect key="frame" x="0.0" y="597" width="375" height="70"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="iOS" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CTc-Jz-yjr">
                                                <rect key="frame" x="20" y="14" width="47.5" height="36"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="30"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6yL-sX-bUL">
                                                <rect key="frame" x="300" y="-5" width="55" height="55"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="55" id="h4F-29-Y6n"/>
                                                    <constraint firstAttribute="height" constant="55" id="q45-t3-9DA"/>
                                                </constraints>
                                                <color key="tintColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <state key="normal" image="ic_add.png"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="type">
                                                        <integer key="value" value="2"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="imageNormal" value="ic_add.png"/>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="rippleColor">
                                                        <color key="value" red="0.82337594749999998" green="0.83186435940000003" blue="0.83186435940000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="handleIncrement:" destination="l7k-TM-87m" eventType="touchUpInside" id="n9c-Ap-rPx"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="6yL-sX-bUL" secondAttribute="bottom" constant="20" symbolic="YES" id="RqS-NP-k0w"/>
                                            <constraint firstAttribute="trailing" secondItem="6yL-sX-bUL" secondAttribute="trailing" constant="20" symbolic="YES" id="gvv-UZ-pAw"/>
                                            <constraint firstAttribute="bottom" secondItem="CTc-Jz-yjr" secondAttribute="bottom" constant="20" symbolic="YES" id="p6W-U8-gLe"/>
                                            <constraint firstAttribute="height" constant="70" id="v60-11-5Pg"/>
                                            <constraint firstItem="CTc-Jz-yjr" firstAttribute="leading" secondItem="9iX-D6-aiy" secondAttribute="leading" constant="20" symbolic="YES" id="vmT-3b-AxF"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" red="0.66666666669999997" green="0.66666666669999997" blue="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="O5w-dx-5ca" firstAttribute="top" secondItem="9Pa-2h-BJY" secondAttribute="top" id="D5u-Ck-lxI"/>
                            <constraint firstItem="O5w-dx-5ca" firstAttribute="leading" secondItem="9Pa-2h-BJY" secondAttribute="leading" id="Djg-Hg-SBg"/>
                            <constraint firstAttribute="trailing" secondItem="O5w-dx-5ca" secondAttribute="trailing" id="RDC-2b-jUw"/>
                            <constraint firstAttribute="bottom" secondItem="O5w-dx-5ca" secondAttribute="bottom" id="YEU-9J-aTb"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="countLabel" destination="qaB-rs-mWp" id="bFY-of-WoI"/>
                        <outlet property="incrementButton" destination="6yL-sX-bUL" id="aQR-ap-BrT"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="7qW-nP-WZA" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="805.60000000000002" y="197.45127436281859"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_add.png" width="24" height="24"/>
    </resources>
</document>
