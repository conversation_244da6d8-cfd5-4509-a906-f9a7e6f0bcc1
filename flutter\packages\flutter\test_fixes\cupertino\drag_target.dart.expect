// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/cupertino.dart';

void main() {
  // Changes made in https://github.com/flutter/flutter/pull/133691
  const dragTarget = DragTarget();
  dragTarget = DragTarget(onWillAcceptWithDetails: (data) => ());
  dragTarget = DragTarget(onWillAcceptWithDetails: (data) => ());

  // Changes made in https://github.com/flutter/flutter/pull/133691
  const dragTarget = DragTarget();
  dragTarget = DragTarget(onAcceptWithDetails: (data) => ());
  dragTarget = DragTarget(onAcceptWithDetails: (data) => ());
}
