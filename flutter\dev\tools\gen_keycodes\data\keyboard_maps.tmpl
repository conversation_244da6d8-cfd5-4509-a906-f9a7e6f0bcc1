// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by dev/tools/gen_keycodes/bin/gen_keycodes.dart and
// should not be edited directly.
//
// Edit the template dev/tools/gen_keycodes/data/keyboard_maps.tmpl instead.
// See dev/tools/gen_keycodes/README.md for more information.

import 'keyboard_key.g.dart';

export 'keyboard_key.g.dart' show LogicalKeyboardKey, PhysicalKeyboardKey;

/// Maps Android-specific key codes to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kAndroidToLogicalKey = <int, LogicalKeyboardKey>{
@@@ANDROID_KEY_CODE_MAP@@@
};

/// Maps Android-specific scan codes to the matching [PhysicalKeyboardKey].
const Map<int, PhysicalKeyboardKey> kAndroidToPhysicalKey = <int, PhysicalKeyboardKey>{
@@@ANDROID_SCAN_CODE_MAP@@@
};

/// A map of Android key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kAndroidNumPadMap = <int, LogicalKeyboardKey>{
@@@ANDROID_NUMPAD_MAP@@@
};

/// Maps Fuchsia-specific IDs to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kFuchsiaToLogicalKey = <int, LogicalKeyboardKey>{
@@@FUCHSIA_KEY_CODE_MAP@@@
};

/// Maps Fuchsia-specific USB HID Usage IDs to the matching
/// [PhysicalKeyboardKey].
const Map<int, PhysicalKeyboardKey> kFuchsiaToPhysicalKey = <int, PhysicalKeyboardKey>{
@@@FUCHSIA_SCAN_CODE_MAP@@@
};

/// Maps macOS-specific key code values representing [PhysicalKeyboardKey].
///
/// MacOS doesn't provide a scan code, but a virtual keycode to represent a physical key.
const Map<int, PhysicalKeyboardKey> kMacOsToPhysicalKey = <int, PhysicalKeyboardKey>{
@@@MACOS_SCAN_CODE_MAP@@@
};

/// A map of macOS key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kMacOsNumPadMap = <int, LogicalKeyboardKey>{
@@@MACOS_NUMPAD_MAP@@@
};

/// A map of macOS key codes which are numbered function keys, so that they
/// can be excluded when asking "is the Fn modifier down?".
const Map<int, LogicalKeyboardKey> kMacOsFunctionKeyMap = <int, LogicalKeyboardKey>{
@@@MACOS_FUNCTION_KEY_MAP@@@
};

/// A map of macOS key codes presenting [LogicalKeyboardKey].
///
/// Logical key codes are not available in macOS key events. Most of the logical keys
/// are derived from its `characterIgnoringModifiers`, but those keys that don't
/// have a character representation will be derived from their key codes using
/// this map.
const Map<int, LogicalKeyboardKey> kMacOsToLogicalKey = <int, LogicalKeyboardKey>{
@@@MACOS_KEY_CODE_MAP@@@
};

/// Maps iOS-specific key code values representing [PhysicalKeyboardKey].
///
/// iOS doesn't provide a scan code, but a virtual keycode to represent a physical key.
const Map<int, PhysicalKeyboardKey> kIosToPhysicalKey = <int, PhysicalKeyboardKey>{
@@@IOS_SCAN_CODE_MAP@@@
};

/// Maps iOS specific string values of nonvisible keys to logical keys
///
/// Some unprintable keys on iOS has literal names on their key label, such as
/// "UIKeyInputEscape". See:
/// https://developer.apple.com/documentation/uikit/uikeycommand/input_strings_for_special_keys?language=objc
const Map<String, LogicalKeyboardKey> kIosSpecialLogicalMap = <String, LogicalKeyboardKey>{
@@@IOS_SPECIAL_MAP@@@
};

/// A map of iOS key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kIosNumPadMap = <int, LogicalKeyboardKey>{
@@@IOS_NUMPAD_MAP@@@
};

/// A map of iOS key codes presenting [LogicalKeyboardKey].
///
/// Logical key codes are not available in iOS key events. Most of the logical keys
/// are derived from its `characterIgnoringModifiers`, but those keys that don't
/// have a character representation will be derived from their key codes using
/// this map.
const Map<int, LogicalKeyboardKey> kIosToLogicalKey = <int, LogicalKeyboardKey>{
@@@IOS_KEY_CODE_MAP@@@
};

/// Maps GLFW-specific key codes to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kGlfwToLogicalKey = <int, LogicalKeyboardKey>{
@@@GLFW_KEY_CODE_MAP@@@
};

/// A map of GLFW key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kGlfwNumpadMap = <int, LogicalKeyboardKey>{
@@@GLFW_NUMPAD_MAP@@@
};

/// Maps GTK-specific key codes to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kGtkToLogicalKey = <int, LogicalKeyboardKey>{
@@@GTK_KEY_CODE_MAP@@@
};

/// A map of GTK key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kGtkNumpadMap = <int, LogicalKeyboardKey>{
@@@GTK_NUMPAD_MAP@@@
};

/// Maps XKB specific key code values representing [PhysicalKeyboardKey].
const Map<int, PhysicalKeyboardKey> kLinuxToPhysicalKey = <int, PhysicalKeyboardKey>{
@@@XKB_SCAN_CODE_MAP@@@
};

/// Maps Web KeyboardEvent codes to the matching [LogicalKeyboardKey].
const Map<String, LogicalKeyboardKey> kWebToLogicalKey = <String, LogicalKeyboardKey>{
@@@WEB_LOGICAL_KEY_MAP@@@
};

/// Maps Web KeyboardEvent codes to the matching [PhysicalKeyboardKey].
const Map<String, PhysicalKeyboardKey> kWebToPhysicalKey = <String, PhysicalKeyboardKey>{
@@@WEB_PHYSICAL_KEY_MAP@@@
};

/// A map of Web KeyboardEvent codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<String, LogicalKeyboardKey> kWebNumPadMap = <String, LogicalKeyboardKey>{
@@@WEB_NUMPAD_MAP@@@
};

/// A map of Web KeyboardEvent keys which needs to be decided based on location,
/// typically for numpad keys and modifier keys. Used to provide different key
/// objects for keys like KEY_EQUALS and NUMPAD_EQUALS.
const Map<String, List<LogicalKeyboardKey?>> kWebLocationMap = <String, List<LogicalKeyboardKey?>>{
@@@WEB_LOCATION_MAP@@@
};

/// Maps Windows KeyboardEvent codes to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kWindowsToLogicalKey = <int, LogicalKeyboardKey>{
@@@WINDOWS_LOGICAL_KEY_MAP@@@
};

/// Maps Windows KeyboardEvent codes to the matching [PhysicalKeyboardKey].
const Map<int, PhysicalKeyboardKey> kWindowsToPhysicalKey = <int, PhysicalKeyboardKey>{
@@@WINDOWS_PHYSICAL_KEY_MAP@@@
};

/// A map of Windows KeyboardEvent codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kWindowsNumPadMap = <int, LogicalKeyboardKey>{
@@@WINDOWS_NUMPAD_MAP@@@
};
