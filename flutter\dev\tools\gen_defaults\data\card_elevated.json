{"version": "6_1_0", "md.comp.elevated-card.container.color": "surfaceContainerLow", "md.comp.elevated-card.container.elevation": "md.sys.elevation.level1", "md.comp.elevated-card.container.shadow-color": "shadow", "md.comp.elevated-card.container.shape": "md.sys.shape.corner.medium", "md.comp.elevated-card.disabled.container.color": "surface", "md.comp.elevated-card.disabled.container.elevation": "md.sys.elevation.level1", "md.comp.elevated-card.disabled.container.opacity": 0.38, "md.comp.elevated-card.dragged.container.elevation": "md.sys.elevation.level4", "md.comp.elevated-card.dragged.state-layer.color": "onSurface", "md.comp.elevated-card.dragged.state-layer.opacity": "md.sys.state.dragged.state-layer-opacity", "md.comp.elevated-card.focus.container.elevation": "md.sys.elevation.level1", "md.comp.elevated-card.focus.indicator.color": "secondary", "md.comp.elevated-card.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.elevated-card.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.elevated-card.focus.state-layer.color": "onSurface", "md.comp.elevated-card.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.elevated-card.hover.container.elevation": "md.sys.elevation.level2", "md.comp.elevated-card.hover.state-layer.color": "onSurface", "md.comp.elevated-card.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.elevated-card.icon.color": "primary", "md.comp.elevated-card.icon.size": 24.0, "md.comp.elevated-card.pressed.container.elevation": "md.sys.elevation.level1", "md.comp.elevated-card.pressed.state-layer.color": "onSurface", "md.comp.elevated-card.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity"}