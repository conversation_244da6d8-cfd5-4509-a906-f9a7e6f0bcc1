# Web Locale Keymap

This package maps Web's KeyboardEvent to <PERSON><PERSON><PERSON>'s logical keys. It only
includes "locale sensitive" keys, which are all the letters, regular digits, and
symbols. It works for all layouts shown in [Microsoft/VSCode](https://github.com/microsoft/vscode) repo.

The mapping data and test cases are generated by `gen_web_locale_keymap` package.
Do not edit them manually.

# Usage

1. Ensure that the key is a locale key.
2. Get the logical key from `getLogicalKey()`.
3. If the return value is null, then the key can not be mapped as a character
either. Mint the logical key value.

```dart
import 'package:web_locale_keymap/web_locale_keymap.dart' as locale_keymap;

final locale_keymap.LocaleKeymap mapping =
    locale_keymap.LocaleKeymap.win(); // Or .darwin() or .linux()

/* ... */

int getLogicalKey(html.KeyboardEvent event) {
  int? result = _convertToDeadKey(event)
             ?? _convertToUnprintableKey(event)
             ?? _convertToNumpadKey(event);
  if (result != null) {
    return result;
  }
  result = mapping.getLogicalKey(event.code, event.key, event.keyCode);
  if (result != null) {
    return result;
  }
  return _mintLogicalKey(event);
}
```
