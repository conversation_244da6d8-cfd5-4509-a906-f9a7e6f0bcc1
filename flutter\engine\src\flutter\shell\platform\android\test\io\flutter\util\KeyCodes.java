package io.flutter.util;

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by
// flutter/flutter:dev/tools/gen_keycodes/bin/gen_keycodes.dart and should not
// be edited directly.
//
// Edit the template
// flutter/flutter:dev/tools/gen_keycodes/data/key_codes_java.tmpl
// instead.
//
// See flutter/flutter:dev/tools/gen_keycodes/README.md for more information.

/**
 * This class contains keyboard constants to be used in unit tests. They should not be used in
 * production code.
 */
public class KeyCodes {
  public static final long PHYSICAL_HYPER = 0x00000010L;
  public static final long PHYSICAL_SUPER_KEY = 0x00000011L;
  public static final long PHYSICAL_FN = 0x00000012L;
  public static final long PHYSICAL_FN_LOCK = 0x00000013L;
  public static final long PHYSICAL_SUSPEND = 0x00000014L;
  public static final long PHYSICAL_RESUME = 0x00000015L;
  public static final long PHYSICAL_TURBO = 0x00000016L;
  public static final long PHYSICAL_PRIVACY_SCREEN_TOGGLE = 0x00000017L;
  public static final long PHYSICAL_MICROPHONE_MUTE_TOGGLE = 0x00000018L;
  public static final long PHYSICAL_SLEEP = 0x00010082L;
  public static final long PHYSICAL_WAKE_UP = 0x00010083L;
  public static final long PHYSICAL_DISPLAY_TOGGLE_INT_EXT = 0x000100b5L;
  public static final long PHYSICAL_GAME_BUTTON1 = 0x0005ff01L;
  public static final long PHYSICAL_GAME_BUTTON2 = 0x0005ff02L;
  public static final long PHYSICAL_GAME_BUTTON3 = 0x0005ff03L;
  public static final long PHYSICAL_GAME_BUTTON4 = 0x0005ff04L;
  public static final long PHYSICAL_GAME_BUTTON5 = 0x0005ff05L;
  public static final long PHYSICAL_GAME_BUTTON6 = 0x0005ff06L;
  public static final long PHYSICAL_GAME_BUTTON7 = 0x0005ff07L;
  public static final long PHYSICAL_GAME_BUTTON8 = 0x0005ff08L;
  public static final long PHYSICAL_GAME_BUTTON9 = 0x0005ff09L;
  public static final long PHYSICAL_GAME_BUTTON10 = 0x0005ff0aL;
  public static final long PHYSICAL_GAME_BUTTON11 = 0x0005ff0bL;
  public static final long PHYSICAL_GAME_BUTTON12 = 0x0005ff0cL;
  public static final long PHYSICAL_GAME_BUTTON13 = 0x0005ff0dL;
  public static final long PHYSICAL_GAME_BUTTON14 = 0x0005ff0eL;
  public static final long PHYSICAL_GAME_BUTTON15 = 0x0005ff0fL;
  public static final long PHYSICAL_GAME_BUTTON16 = 0x0005ff10L;
  public static final long PHYSICAL_GAME_BUTTON_A = 0x0005ff11L;
  public static final long PHYSICAL_GAME_BUTTON_B = 0x0005ff12L;
  public static final long PHYSICAL_GAME_BUTTON_C = 0x0005ff13L;
  public static final long PHYSICAL_GAME_BUTTON_LEFT1 = 0x0005ff14L;
  public static final long PHYSICAL_GAME_BUTTON_LEFT2 = 0x0005ff15L;
  public static final long PHYSICAL_GAME_BUTTON_MODE = 0x0005ff16L;
  public static final long PHYSICAL_GAME_BUTTON_RIGHT1 = 0x0005ff17L;
  public static final long PHYSICAL_GAME_BUTTON_RIGHT2 = 0x0005ff18L;
  public static final long PHYSICAL_GAME_BUTTON_SELECT = 0x0005ff19L;
  public static final long PHYSICAL_GAME_BUTTON_START = 0x0005ff1aL;
  public static final long PHYSICAL_GAME_BUTTON_THUMB_LEFT = 0x0005ff1bL;
  public static final long PHYSICAL_GAME_BUTTON_THUMB_RIGHT = 0x0005ff1cL;
  public static final long PHYSICAL_GAME_BUTTON_X = 0x0005ff1dL;
  public static final long PHYSICAL_GAME_BUTTON_Y = 0x0005ff1eL;
  public static final long PHYSICAL_GAME_BUTTON_Z = 0x0005ff1fL;
  public static final long PHYSICAL_USB_RESERVED = 0x00070000L;
  public static final long PHYSICAL_USB_ERROR_ROLL_OVER = 0x00070001L;
  public static final long PHYSICAL_USB_POST_FAIL = 0x00070002L;
  public static final long PHYSICAL_USB_ERROR_UNDEFINED = 0x00070003L;
  public static final long PHYSICAL_KEY_A = 0x00070004L;
  public static final long PHYSICAL_KEY_B = 0x00070005L;
  public static final long PHYSICAL_KEY_C = 0x00070006L;
  public static final long PHYSICAL_KEY_D = 0x00070007L;
  public static final long PHYSICAL_KEY_E = 0x00070008L;
  public static final long PHYSICAL_KEY_F = 0x00070009L;
  public static final long PHYSICAL_KEY_G = 0x0007000aL;
  public static final long PHYSICAL_KEY_H = 0x0007000bL;
  public static final long PHYSICAL_KEY_I = 0x0007000cL;
  public static final long PHYSICAL_KEY_J = 0x0007000dL;
  public static final long PHYSICAL_KEY_K = 0x0007000eL;
  public static final long PHYSICAL_KEY_L = 0x0007000fL;
  public static final long PHYSICAL_KEY_M = 0x00070010L;
  public static final long PHYSICAL_KEY_N = 0x00070011L;
  public static final long PHYSICAL_KEY_O = 0x00070012L;
  public static final long PHYSICAL_KEY_P = 0x00070013L;
  public static final long PHYSICAL_KEY_Q = 0x00070014L;
  public static final long PHYSICAL_KEY_R = 0x00070015L;
  public static final long PHYSICAL_KEY_S = 0x00070016L;
  public static final long PHYSICAL_KEY_T = 0x00070017L;
  public static final long PHYSICAL_KEY_U = 0x00070018L;
  public static final long PHYSICAL_KEY_V = 0x00070019L;
  public static final long PHYSICAL_KEY_W = 0x0007001aL;
  public static final long PHYSICAL_KEY_X = 0x0007001bL;
  public static final long PHYSICAL_KEY_Y = 0x0007001cL;
  public static final long PHYSICAL_KEY_Z = 0x0007001dL;
  public static final long PHYSICAL_DIGIT1 = 0x0007001eL;
  public static final long PHYSICAL_DIGIT2 = 0x0007001fL;
  public static final long PHYSICAL_DIGIT3 = 0x00070020L;
  public static final long PHYSICAL_DIGIT4 = 0x00070021L;
  public static final long PHYSICAL_DIGIT5 = 0x00070022L;
  public static final long PHYSICAL_DIGIT6 = 0x00070023L;
  public static final long PHYSICAL_DIGIT7 = 0x00070024L;
  public static final long PHYSICAL_DIGIT8 = 0x00070025L;
  public static final long PHYSICAL_DIGIT9 = 0x00070026L;
  public static final long PHYSICAL_DIGIT0 = 0x00070027L;
  public static final long PHYSICAL_ENTER = 0x00070028L;
  public static final long PHYSICAL_ESCAPE = 0x00070029L;
  public static final long PHYSICAL_BACKSPACE = 0x0007002aL;
  public static final long PHYSICAL_TAB = 0x0007002bL;
  public static final long PHYSICAL_SPACE = 0x0007002cL;
  public static final long PHYSICAL_MINUS = 0x0007002dL;
  public static final long PHYSICAL_EQUAL = 0x0007002eL;
  public static final long PHYSICAL_BRACKET_LEFT = 0x0007002fL;
  public static final long PHYSICAL_BRACKET_RIGHT = 0x00070030L;
  public static final long PHYSICAL_BACKSLASH = 0x00070031L;
  public static final long PHYSICAL_SEMICOLON = 0x00070033L;
  public static final long PHYSICAL_QUOTE = 0x00070034L;
  public static final long PHYSICAL_BACKQUOTE = 0x00070035L;
  public static final long PHYSICAL_COMMA = 0x00070036L;
  public static final long PHYSICAL_PERIOD = 0x00070037L;
  public static final long PHYSICAL_SLASH = 0x00070038L;
  public static final long PHYSICAL_CAPS_LOCK = 0x00070039L;
  public static final long PHYSICAL_F1 = 0x0007003aL;
  public static final long PHYSICAL_F2 = 0x0007003bL;
  public static final long PHYSICAL_F3 = 0x0007003cL;
  public static final long PHYSICAL_F4 = 0x0007003dL;
  public static final long PHYSICAL_F5 = 0x0007003eL;
  public static final long PHYSICAL_F6 = 0x0007003fL;
  public static final long PHYSICAL_F7 = 0x00070040L;
  public static final long PHYSICAL_F8 = 0x00070041L;
  public static final long PHYSICAL_F9 = 0x00070042L;
  public static final long PHYSICAL_F10 = 0x00070043L;
  public static final long PHYSICAL_F11 = 0x00070044L;
  public static final long PHYSICAL_F12 = 0x00070045L;
  public static final long PHYSICAL_PRINT_SCREEN = 0x00070046L;
  public static final long PHYSICAL_SCROLL_LOCK = 0x00070047L;
  public static final long PHYSICAL_PAUSE = 0x00070048L;
  public static final long PHYSICAL_INSERT = 0x00070049L;
  public static final long PHYSICAL_HOME = 0x0007004aL;
  public static final long PHYSICAL_PAGE_UP = 0x0007004bL;
  public static final long PHYSICAL_DELETE = 0x0007004cL;
  public static final long PHYSICAL_END = 0x0007004dL;
  public static final long PHYSICAL_PAGE_DOWN = 0x0007004eL;
  public static final long PHYSICAL_ARROW_RIGHT = 0x0007004fL;
  public static final long PHYSICAL_ARROW_LEFT = 0x00070050L;
  public static final long PHYSICAL_ARROW_DOWN = 0x00070051L;
  public static final long PHYSICAL_ARROW_UP = 0x00070052L;
  public static final long PHYSICAL_NUM_LOCK = 0x00070053L;
  public static final long PHYSICAL_NUMPAD_DIVIDE = 0x00070054L;
  public static final long PHYSICAL_NUMPAD_MULTIPLY = 0x00070055L;
  public static final long PHYSICAL_NUMPAD_SUBTRACT = 0x00070056L;
  public static final long PHYSICAL_NUMPAD_ADD = 0x00070057L;
  public static final long PHYSICAL_NUMPAD_ENTER = 0x00070058L;
  public static final long PHYSICAL_NUMPAD1 = 0x00070059L;
  public static final long PHYSICAL_NUMPAD2 = 0x0007005aL;
  public static final long PHYSICAL_NUMPAD3 = 0x0007005bL;
  public static final long PHYSICAL_NUMPAD4 = 0x0007005cL;
  public static final long PHYSICAL_NUMPAD5 = 0x0007005dL;
  public static final long PHYSICAL_NUMPAD6 = 0x0007005eL;
  public static final long PHYSICAL_NUMPAD7 = 0x0007005fL;
  public static final long PHYSICAL_NUMPAD8 = 0x00070060L;
  public static final long PHYSICAL_NUMPAD9 = 0x00070061L;
  public static final long PHYSICAL_NUMPAD0 = 0x00070062L;
  public static final long PHYSICAL_NUMPAD_DECIMAL = 0x00070063L;
  public static final long PHYSICAL_INTL_BACKSLASH = 0x00070064L;
  public static final long PHYSICAL_CONTEXT_MENU = 0x00070065L;
  public static final long PHYSICAL_POWER = 0x00070066L;
  public static final long PHYSICAL_NUMPAD_EQUAL = 0x00070067L;
  public static final long PHYSICAL_F13 = 0x00070068L;
  public static final long PHYSICAL_F14 = 0x00070069L;
  public static final long PHYSICAL_F15 = 0x0007006aL;
  public static final long PHYSICAL_F16 = 0x0007006bL;
  public static final long PHYSICAL_F17 = 0x0007006cL;
  public static final long PHYSICAL_F18 = 0x0007006dL;
  public static final long PHYSICAL_F19 = 0x0007006eL;
  public static final long PHYSICAL_F20 = 0x0007006fL;
  public static final long PHYSICAL_F21 = 0x00070070L;
  public static final long PHYSICAL_F22 = 0x00070071L;
  public static final long PHYSICAL_F23 = 0x00070072L;
  public static final long PHYSICAL_F24 = 0x00070073L;
  public static final long PHYSICAL_OPEN = 0x00070074L;
  public static final long PHYSICAL_HELP = 0x00070075L;
  public static final long PHYSICAL_SELECT = 0x00070077L;
  public static final long PHYSICAL_AGAIN = 0x00070079L;
  public static final long PHYSICAL_UNDO = 0x0007007aL;
  public static final long PHYSICAL_CUT = 0x0007007bL;
  public static final long PHYSICAL_COPY = 0x0007007cL;
  public static final long PHYSICAL_PASTE = 0x0007007dL;
  public static final long PHYSICAL_FIND = 0x0007007eL;
  public static final long PHYSICAL_AUDIO_VOLUME_MUTE = 0x0007007fL;
  public static final long PHYSICAL_AUDIO_VOLUME_UP = 0x00070080L;
  public static final long PHYSICAL_AUDIO_VOLUME_DOWN = 0x00070081L;
  public static final long PHYSICAL_NUMPAD_COMMA = 0x00070085L;
  public static final long PHYSICAL_INTL_RO = 0x00070087L;
  public static final long PHYSICAL_KANA_MODE = 0x00070088L;
  public static final long PHYSICAL_INTL_YEN = 0x00070089L;
  public static final long PHYSICAL_CONVERT = 0x0007008aL;
  public static final long PHYSICAL_NON_CONVERT = 0x0007008bL;
  public static final long PHYSICAL_LANG1 = 0x00070090L;
  public static final long PHYSICAL_LANG2 = 0x00070091L;
  public static final long PHYSICAL_LANG3 = 0x00070092L;
  public static final long PHYSICAL_LANG4 = 0x00070093L;
  public static final long PHYSICAL_LANG5 = 0x00070094L;
  public static final long PHYSICAL_ABORT = 0x0007009bL;
  public static final long PHYSICAL_PROPS = 0x000700a3L;
  public static final long PHYSICAL_NUMPAD_PAREN_LEFT = 0x000700b6L;
  public static final long PHYSICAL_NUMPAD_PAREN_RIGHT = 0x000700b7L;
  public static final long PHYSICAL_NUMPAD_BACKSPACE = 0x000700bbL;
  public static final long PHYSICAL_NUMPAD_MEMORY_STORE = 0x000700d0L;
  public static final long PHYSICAL_NUMPAD_MEMORY_RECALL = 0x000700d1L;
  public static final long PHYSICAL_NUMPAD_MEMORY_CLEAR = 0x000700d2L;
  public static final long PHYSICAL_NUMPAD_MEMORY_ADD = 0x000700d3L;
  public static final long PHYSICAL_NUMPAD_MEMORY_SUBTRACT = 0x000700d4L;
  public static final long PHYSICAL_NUMPAD_SIGN_CHANGE = 0x000700d7L;
  public static final long PHYSICAL_NUMPAD_CLEAR = 0x000700d8L;
  public static final long PHYSICAL_NUMPAD_CLEAR_ENTRY = 0x000700d9L;
  public static final long PHYSICAL_CONTROL_LEFT = 0x000700e0L;
  public static final long PHYSICAL_SHIFT_LEFT = 0x000700e1L;
  public static final long PHYSICAL_ALT_LEFT = 0x000700e2L;
  public static final long PHYSICAL_META_LEFT = 0x000700e3L;
  public static final long PHYSICAL_CONTROL_RIGHT = 0x000700e4L;
  public static final long PHYSICAL_SHIFT_RIGHT = 0x000700e5L;
  public static final long PHYSICAL_ALT_RIGHT = 0x000700e6L;
  public static final long PHYSICAL_META_RIGHT = 0x000700e7L;
  public static final long PHYSICAL_INFO = 0x000c0060L;
  public static final long PHYSICAL_CLOSED_CAPTION_TOGGLE = 0x000c0061L;
  public static final long PHYSICAL_BRIGHTNESS_UP = 0x000c006fL;
  public static final long PHYSICAL_BRIGHTNESS_DOWN = 0x000c0070L;
  public static final long PHYSICAL_BRIGHTNESS_TOGGLE = 0x000c0072L;
  public static final long PHYSICAL_BRIGHTNESS_MINIMUM = 0x000c0073L;
  public static final long PHYSICAL_BRIGHTNESS_MAXIMUM = 0x000c0074L;
  public static final long PHYSICAL_BRIGHTNESS_AUTO = 0x000c0075L;
  public static final long PHYSICAL_KBD_ILLUM_UP = 0x000c0079L;
  public static final long PHYSICAL_KBD_ILLUM_DOWN = 0x000c007aL;
  public static final long PHYSICAL_MEDIA_LAST = 0x000c0083L;
  public static final long PHYSICAL_LAUNCH_PHONE = 0x000c008cL;
  public static final long PHYSICAL_PROGRAM_GUIDE = 0x000c008dL;
  public static final long PHYSICAL_EXIT = 0x000c0094L;
  public static final long PHYSICAL_CHANNEL_UP = 0x000c009cL;
  public static final long PHYSICAL_CHANNEL_DOWN = 0x000c009dL;
  public static final long PHYSICAL_MEDIA_PLAY = 0x000c00b0L;
  public static final long PHYSICAL_MEDIA_PAUSE = 0x000c00b1L;
  public static final long PHYSICAL_MEDIA_RECORD = 0x000c00b2L;
  public static final long PHYSICAL_MEDIA_FAST_FORWARD = 0x000c00b3L;
  public static final long PHYSICAL_MEDIA_REWIND = 0x000c00b4L;
  public static final long PHYSICAL_MEDIA_TRACK_NEXT = 0x000c00b5L;
  public static final long PHYSICAL_MEDIA_TRACK_PREVIOUS = 0x000c00b6L;
  public static final long PHYSICAL_MEDIA_STOP = 0x000c00b7L;
  public static final long PHYSICAL_EJECT = 0x000c00b8L;
  public static final long PHYSICAL_MEDIA_PLAY_PAUSE = 0x000c00cdL;
  public static final long PHYSICAL_SPEECH_INPUT_TOGGLE = 0x000c00cfL;
  public static final long PHYSICAL_BASS_BOOST = 0x000c00e5L;
  public static final long PHYSICAL_MEDIA_SELECT = 0x000c0183L;
  public static final long PHYSICAL_LAUNCH_WORD_PROCESSOR = 0x000c0184L;
  public static final long PHYSICAL_LAUNCH_SPREADSHEET = 0x000c0186L;
  public static final long PHYSICAL_LAUNCH_MAIL = 0x000c018aL;
  public static final long PHYSICAL_LAUNCH_CONTACTS = 0x000c018dL;
  public static final long PHYSICAL_LAUNCH_CALENDAR = 0x000c018eL;
  public static final long PHYSICAL_LAUNCH_APP2 = 0x000c0192L;
  public static final long PHYSICAL_LAUNCH_APP1 = 0x000c0194L;
  public static final long PHYSICAL_LAUNCH_INTERNET_BROWSER = 0x000c0196L;
  public static final long PHYSICAL_LOG_OFF = 0x000c019cL;
  public static final long PHYSICAL_LOCK_SCREEN = 0x000c019eL;
  public static final long PHYSICAL_LAUNCH_CONTROL_PANEL = 0x000c019fL;
  public static final long PHYSICAL_SELECT_TASK = 0x000c01a2L;
  public static final long PHYSICAL_LAUNCH_DOCUMENTS = 0x000c01a7L;
  public static final long PHYSICAL_SPELL_CHECK = 0x000c01abL;
  public static final long PHYSICAL_LAUNCH_KEYBOARD_LAYOUT = 0x000c01aeL;
  public static final long PHYSICAL_LAUNCH_SCREEN_SAVER = 0x000c01b1L;
  public static final long PHYSICAL_LAUNCH_AUDIO_BROWSER = 0x000c01b7L;
  public static final long PHYSICAL_LAUNCH_ASSISTANT = 0x000c01cbL;
  public static final long PHYSICAL_NEW_KEY = 0x000c0201L;
  public static final long PHYSICAL_CLOSE = 0x000c0203L;
  public static final long PHYSICAL_SAVE = 0x000c0207L;
  public static final long PHYSICAL_PRINT = 0x000c0208L;
  public static final long PHYSICAL_BROWSER_SEARCH = 0x000c0221L;
  public static final long PHYSICAL_BROWSER_HOME = 0x000c0223L;
  public static final long PHYSICAL_BROWSER_BACK = 0x000c0224L;
  public static final long PHYSICAL_BROWSER_FORWARD = 0x000c0225L;
  public static final long PHYSICAL_BROWSER_STOP = 0x000c0226L;
  public static final long PHYSICAL_BROWSER_REFRESH = 0x000c0227L;
  public static final long PHYSICAL_BROWSER_FAVORITES = 0x000c022aL;
  public static final long PHYSICAL_ZOOM_IN = 0x000c022dL;
  public static final long PHYSICAL_ZOOM_OUT = 0x000c022eL;
  public static final long PHYSICAL_ZOOM_TOGGLE = 0x000c0232L;
  public static final long PHYSICAL_REDO = 0x000c0279L;
  public static final long PHYSICAL_MAIL_REPLY = 0x000c0289L;
  public static final long PHYSICAL_MAIL_FORWARD = 0x000c028bL;
  public static final long PHYSICAL_MAIL_SEND = 0x000c028cL;
  public static final long PHYSICAL_KEYBOARD_LAYOUT_SELECT = 0x000c029dL;
  public static final long PHYSICAL_SHOW_ALL_WINDOWS = 0x000c029fL;

  public static final long LOGICAL_SPACE = 0x00000000020L;
  public static final long LOGICAL_EXCLAMATION = 0x00000000021L;
  public static final long LOGICAL_QUOTE = 0x00000000022L;
  public static final long LOGICAL_NUMBER_SIGN = 0x00000000023L;
  public static final long LOGICAL_DOLLAR = 0x00000000024L;
  public static final long LOGICAL_PERCENT = 0x00000000025L;
  public static final long LOGICAL_AMPERSAND = 0x00000000026L;
  public static final long LOGICAL_QUOTE_SINGLE = 0x00000000027L;
  public static final long LOGICAL_PARENTHESIS_LEFT = 0x00000000028L;
  public static final long LOGICAL_PARENTHESIS_RIGHT = 0x00000000029L;
  public static final long LOGICAL_ASTERISK = 0x0000000002aL;
  public static final long LOGICAL_ADD = 0x0000000002bL;
  public static final long LOGICAL_COMMA = 0x0000000002cL;
  public static final long LOGICAL_MINUS = 0x0000000002dL;
  public static final long LOGICAL_PERIOD = 0x0000000002eL;
  public static final long LOGICAL_SLASH = 0x0000000002fL;
  public static final long LOGICAL_DIGIT0 = 0x00000000030L;
  public static final long LOGICAL_DIGIT1 = 0x00000000031L;
  public static final long LOGICAL_DIGIT2 = 0x00000000032L;
  public static final long LOGICAL_DIGIT3 = 0x00000000033L;
  public static final long LOGICAL_DIGIT4 = 0x00000000034L;
  public static final long LOGICAL_DIGIT5 = 0x00000000035L;
  public static final long LOGICAL_DIGIT6 = 0x00000000036L;
  public static final long LOGICAL_DIGIT7 = 0x00000000037L;
  public static final long LOGICAL_DIGIT8 = 0x00000000038L;
  public static final long LOGICAL_DIGIT9 = 0x00000000039L;
  public static final long LOGICAL_COLON = 0x0000000003aL;
  public static final long LOGICAL_SEMICOLON = 0x0000000003bL;
  public static final long LOGICAL_LESS = 0x0000000003cL;
  public static final long LOGICAL_EQUAL = 0x0000000003dL;
  public static final long LOGICAL_GREATER = 0x0000000003eL;
  public static final long LOGICAL_QUESTION = 0x0000000003fL;
  public static final long LOGICAL_AT = 0x00000000040L;
  public static final long LOGICAL_BRACKET_LEFT = 0x0000000005bL;
  public static final long LOGICAL_BACKSLASH = 0x0000000005cL;
  public static final long LOGICAL_BRACKET_RIGHT = 0x0000000005dL;
  public static final long LOGICAL_CARET = 0x0000000005eL;
  public static final long LOGICAL_UNDERSCORE = 0x0000000005fL;
  public static final long LOGICAL_BACKQUOTE = 0x00000000060L;
  public static final long LOGICAL_KEY_A = 0x00000000061L;
  public static final long LOGICAL_KEY_B = 0x00000000062L;
  public static final long LOGICAL_KEY_C = 0x00000000063L;
  public static final long LOGICAL_KEY_D = 0x00000000064L;
  public static final long LOGICAL_KEY_E = 0x00000000065L;
  public static final long LOGICAL_KEY_F = 0x00000000066L;
  public static final long LOGICAL_KEY_G = 0x00000000067L;
  public static final long LOGICAL_KEY_H = 0x00000000068L;
  public static final long LOGICAL_KEY_I = 0x00000000069L;
  public static final long LOGICAL_KEY_J = 0x0000000006aL;
  public static final long LOGICAL_KEY_K = 0x0000000006bL;
  public static final long LOGICAL_KEY_L = 0x0000000006cL;
  public static final long LOGICAL_KEY_M = 0x0000000006dL;
  public static final long LOGICAL_KEY_N = 0x0000000006eL;
  public static final long LOGICAL_KEY_O = 0x0000000006fL;
  public static final long LOGICAL_KEY_P = 0x00000000070L;
  public static final long LOGICAL_KEY_Q = 0x00000000071L;
  public static final long LOGICAL_KEY_R = 0x00000000072L;
  public static final long LOGICAL_KEY_S = 0x00000000073L;
  public static final long LOGICAL_KEY_T = 0x00000000074L;
  public static final long LOGICAL_KEY_U = 0x00000000075L;
  public static final long LOGICAL_KEY_V = 0x00000000076L;
  public static final long LOGICAL_KEY_W = 0x00000000077L;
  public static final long LOGICAL_KEY_X = 0x00000000078L;
  public static final long LOGICAL_KEY_Y = 0x00000000079L;
  public static final long LOGICAL_KEY_Z = 0x0000000007aL;
  public static final long LOGICAL_BRACE_LEFT = 0x0000000007bL;
  public static final long LOGICAL_BAR = 0x0000000007cL;
  public static final long LOGICAL_BRACE_RIGHT = 0x0000000007dL;
  public static final long LOGICAL_TILDE = 0x0000000007eL;
  public static final long LOGICAL_UNIDENTIFIED = 0x00100000001L;
  public static final long LOGICAL_BACKSPACE = 0x00100000008L;
  public static final long LOGICAL_TAB = 0x00100000009L;
  public static final long LOGICAL_ENTER = 0x0010000000dL;
  public static final long LOGICAL_ESCAPE = 0x0010000001bL;
  public static final long LOGICAL_DELETE = 0x0010000007fL;
  public static final long LOGICAL_ACCEL = 0x00100000101L;
  public static final long LOGICAL_ALT_GRAPH = 0x00100000103L;
  public static final long LOGICAL_CAPS_LOCK = 0x00100000104L;
  public static final long LOGICAL_FN = 0x00100000106L;
  public static final long LOGICAL_FN_LOCK = 0x00100000107L;
  public static final long LOGICAL_HYPER = 0x00100000108L;
  public static final long LOGICAL_NUM_LOCK = 0x0010000010aL;
  public static final long LOGICAL_SCROLL_LOCK = 0x0010000010cL;
  public static final long LOGICAL_SUPER_KEY = 0x0010000010eL;
  public static final long LOGICAL_SYMBOL = 0x0010000010fL;
  public static final long LOGICAL_SYMBOL_LOCK = 0x00100000110L;
  public static final long LOGICAL_SHIFT_LEVEL5 = 0x00100000111L;
  public static final long LOGICAL_ARROW_DOWN = 0x00100000301L;
  public static final long LOGICAL_ARROW_LEFT = 0x00100000302L;
  public static final long LOGICAL_ARROW_RIGHT = 0x00100000303L;
  public static final long LOGICAL_ARROW_UP = 0x00100000304L;
  public static final long LOGICAL_END = 0x00100000305L;
  public static final long LOGICAL_HOME = 0x00100000306L;
  public static final long LOGICAL_PAGE_DOWN = 0x00100000307L;
  public static final long LOGICAL_PAGE_UP = 0x00100000308L;
  public static final long LOGICAL_CLEAR = 0x00100000401L;
  public static final long LOGICAL_COPY = 0x00100000402L;
  public static final long LOGICAL_CR_SEL = 0x00100000403L;
  public static final long LOGICAL_CUT = 0x00100000404L;
  public static final long LOGICAL_ERASE_EOF = 0x00100000405L;
  public static final long LOGICAL_EX_SEL = 0x00100000406L;
  public static final long LOGICAL_INSERT = 0x00100000407L;
  public static final long LOGICAL_PASTE = 0x00100000408L;
  public static final long LOGICAL_REDO = 0x00100000409L;
  public static final long LOGICAL_UNDO = 0x0010000040aL;
  public static final long LOGICAL_ACCEPT = 0x00100000501L;
  public static final long LOGICAL_AGAIN = 0x00100000502L;
  public static final long LOGICAL_ATTN = 0x00100000503L;
  public static final long LOGICAL_CANCEL = 0x00100000504L;
  public static final long LOGICAL_CONTEXT_MENU = 0x00100000505L;
  public static final long LOGICAL_EXECUTE = 0x00100000506L;
  public static final long LOGICAL_FIND = 0x00100000507L;
  public static final long LOGICAL_HELP = 0x00100000508L;
  public static final long LOGICAL_PAUSE = 0x00100000509L;
  public static final long LOGICAL_PLAY = 0x0010000050aL;
  public static final long LOGICAL_PROPS = 0x0010000050bL;
  public static final long LOGICAL_SELECT = 0x0010000050cL;
  public static final long LOGICAL_ZOOM_IN = 0x0010000050dL;
  public static final long LOGICAL_ZOOM_OUT = 0x0010000050eL;
  public static final long LOGICAL_BRIGHTNESS_DOWN = 0x00100000601L;
  public static final long LOGICAL_BRIGHTNESS_UP = 0x00100000602L;
  public static final long LOGICAL_CAMERA = 0x00100000603L;
  public static final long LOGICAL_EJECT = 0x00100000604L;
  public static final long LOGICAL_LOG_OFF = 0x00100000605L;
  public static final long LOGICAL_POWER = 0x00100000606L;
  public static final long LOGICAL_POWER_OFF = 0x00100000607L;
  public static final long LOGICAL_PRINT_SCREEN = 0x00100000608L;
  public static final long LOGICAL_HIBERNATE = 0x00100000609L;
  public static final long LOGICAL_STANDBY = 0x0010000060aL;
  public static final long LOGICAL_WAKE_UP = 0x0010000060bL;
  public static final long LOGICAL_ALL_CANDIDATES = 0x00100000701L;
  public static final long LOGICAL_ALPHANUMERIC = 0x00100000702L;
  public static final long LOGICAL_CODE_INPUT = 0x00100000703L;
  public static final long LOGICAL_COMPOSE = 0x00100000704L;
  public static final long LOGICAL_CONVERT = 0x00100000705L;
  public static final long LOGICAL_FINAL_MODE = 0x00100000706L;
  public static final long LOGICAL_GROUP_FIRST = 0x00100000707L;
  public static final long LOGICAL_GROUP_LAST = 0x00100000708L;
  public static final long LOGICAL_GROUP_NEXT = 0x00100000709L;
  public static final long LOGICAL_GROUP_PREVIOUS = 0x0010000070aL;
  public static final long LOGICAL_MODE_CHANGE = 0x0010000070bL;
  public static final long LOGICAL_NEXT_CANDIDATE = 0x0010000070cL;
  public static final long LOGICAL_NON_CONVERT = 0x0010000070dL;
  public static final long LOGICAL_PREVIOUS_CANDIDATE = 0x0010000070eL;
  public static final long LOGICAL_PROCESS = 0x0010000070fL;
  public static final long LOGICAL_SINGLE_CANDIDATE = 0x00100000710L;
  public static final long LOGICAL_HANGUL_MODE = 0x00100000711L;
  public static final long LOGICAL_HANJA_MODE = 0x00100000712L;
  public static final long LOGICAL_JUNJA_MODE = 0x00100000713L;
  public static final long LOGICAL_EISU = 0x00100000714L;
  public static final long LOGICAL_HANKAKU = 0x00100000715L;
  public static final long LOGICAL_HIRAGANA = 0x00100000716L;
  public static final long LOGICAL_HIRAGANA_KATAKANA = 0x00100000717L;
  public static final long LOGICAL_KANA_MODE = 0x00100000718L;
  public static final long LOGICAL_KANJI_MODE = 0x00100000719L;
  public static final long LOGICAL_KATAKANA = 0x0010000071aL;
  public static final long LOGICAL_ROMAJI = 0x0010000071bL;
  public static final long LOGICAL_ZENKAKU = 0x0010000071cL;
  public static final long LOGICAL_ZENKAKU_HANKAKU = 0x0010000071dL;
  public static final long LOGICAL_F1 = 0x00100000801L;
  public static final long LOGICAL_F2 = 0x00100000802L;
  public static final long LOGICAL_F3 = 0x00100000803L;
  public static final long LOGICAL_F4 = 0x00100000804L;
  public static final long LOGICAL_F5 = 0x00100000805L;
  public static final long LOGICAL_F6 = 0x00100000806L;
  public static final long LOGICAL_F7 = 0x00100000807L;
  public static final long LOGICAL_F8 = 0x00100000808L;
  public static final long LOGICAL_F9 = 0x00100000809L;
  public static final long LOGICAL_F10 = 0x0010000080aL;
  public static final long LOGICAL_F11 = 0x0010000080bL;
  public static final long LOGICAL_F12 = 0x0010000080cL;
  public static final long LOGICAL_F13 = 0x0010000080dL;
  public static final long LOGICAL_F14 = 0x0010000080eL;
  public static final long LOGICAL_F15 = 0x0010000080fL;
  public static final long LOGICAL_F16 = 0x00100000810L;
  public static final long LOGICAL_F17 = 0x00100000811L;
  public static final long LOGICAL_F18 = 0x00100000812L;
  public static final long LOGICAL_F19 = 0x00100000813L;
  public static final long LOGICAL_F20 = 0x00100000814L;
  public static final long LOGICAL_F21 = 0x00100000815L;
  public static final long LOGICAL_F22 = 0x00100000816L;
  public static final long LOGICAL_F23 = 0x00100000817L;
  public static final long LOGICAL_F24 = 0x00100000818L;
  public static final long LOGICAL_SOFT1 = 0x00100000901L;
  public static final long LOGICAL_SOFT2 = 0x00100000902L;
  public static final long LOGICAL_SOFT3 = 0x00100000903L;
  public static final long LOGICAL_SOFT4 = 0x00100000904L;
  public static final long LOGICAL_SOFT5 = 0x00100000905L;
  public static final long LOGICAL_SOFT6 = 0x00100000906L;
  public static final long LOGICAL_SOFT7 = 0x00100000907L;
  public static final long LOGICAL_SOFT8 = 0x00100000908L;
  public static final long LOGICAL_CLOSE = 0x00100000a01L;
  public static final long LOGICAL_MAIL_FORWARD = 0x00100000a02L;
  public static final long LOGICAL_MAIL_REPLY = 0x00100000a03L;
  public static final long LOGICAL_MAIL_SEND = 0x00100000a04L;
  public static final long LOGICAL_MEDIA_PLAY_PAUSE = 0x00100000a05L;
  public static final long LOGICAL_MEDIA_STOP = 0x00100000a07L;
  public static final long LOGICAL_MEDIA_TRACK_NEXT = 0x00100000a08L;
  public static final long LOGICAL_MEDIA_TRACK_PREVIOUS = 0x00100000a09L;
  public static final long LOGICAL_NEW_KEY = 0x00100000a0aL;
  public static final long LOGICAL_OPEN = 0x00100000a0bL;
  public static final long LOGICAL_PRINT = 0x00100000a0cL;
  public static final long LOGICAL_SAVE = 0x00100000a0dL;
  public static final long LOGICAL_SPELL_CHECK = 0x00100000a0eL;
  public static final long LOGICAL_AUDIO_VOLUME_DOWN = 0x00100000a0fL;
  public static final long LOGICAL_AUDIO_VOLUME_UP = 0x00100000a10L;
  public static final long LOGICAL_AUDIO_VOLUME_MUTE = 0x00100000a11L;
  public static final long LOGICAL_LAUNCH_APPLICATION2 = 0x00100000b01L;
  public static final long LOGICAL_LAUNCH_CALENDAR = 0x00100000b02L;
  public static final long LOGICAL_LAUNCH_MAIL = 0x00100000b03L;
  public static final long LOGICAL_LAUNCH_MEDIA_PLAYER = 0x00100000b04L;
  public static final long LOGICAL_LAUNCH_MUSIC_PLAYER = 0x00100000b05L;
  public static final long LOGICAL_LAUNCH_APPLICATION1 = 0x00100000b06L;
  public static final long LOGICAL_LAUNCH_SCREEN_SAVER = 0x00100000b07L;
  public static final long LOGICAL_LAUNCH_SPREADSHEET = 0x00100000b08L;
  public static final long LOGICAL_LAUNCH_WEB_BROWSER = 0x00100000b09L;
  public static final long LOGICAL_LAUNCH_WEB_CAM = 0x00100000b0aL;
  public static final long LOGICAL_LAUNCH_WORD_PROCESSOR = 0x00100000b0bL;
  public static final long LOGICAL_LAUNCH_CONTACTS = 0x00100000b0cL;
  public static final long LOGICAL_LAUNCH_PHONE = 0x00100000b0dL;
  public static final long LOGICAL_LAUNCH_ASSISTANT = 0x00100000b0eL;
  public static final long LOGICAL_LAUNCH_CONTROL_PANEL = 0x00100000b0fL;
  public static final long LOGICAL_BROWSER_BACK = 0x00100000c01L;
  public static final long LOGICAL_BROWSER_FAVORITES = 0x00100000c02L;
  public static final long LOGICAL_BROWSER_FORWARD = 0x00100000c03L;
  public static final long LOGICAL_BROWSER_HOME = 0x00100000c04L;
  public static final long LOGICAL_BROWSER_REFRESH = 0x00100000c05L;
  public static final long LOGICAL_BROWSER_SEARCH = 0x00100000c06L;
  public static final long LOGICAL_BROWSER_STOP = 0x00100000c07L;
  public static final long LOGICAL_AUDIO_BALANCE_LEFT = 0x00100000d01L;
  public static final long LOGICAL_AUDIO_BALANCE_RIGHT = 0x00100000d02L;
  public static final long LOGICAL_AUDIO_BASS_BOOST_DOWN = 0x00100000d03L;
  public static final long LOGICAL_AUDIO_BASS_BOOST_UP = 0x00100000d04L;
  public static final long LOGICAL_AUDIO_FADER_FRONT = 0x00100000d05L;
  public static final long LOGICAL_AUDIO_FADER_REAR = 0x00100000d06L;
  public static final long LOGICAL_AUDIO_SURROUND_MODE_NEXT = 0x00100000d07L;
  public static final long LOGICAL_AVR_INPUT = 0x00100000d08L;
  public static final long LOGICAL_AVR_POWER = 0x00100000d09L;
  public static final long LOGICAL_CHANNEL_DOWN = 0x00100000d0aL;
  public static final long LOGICAL_CHANNEL_UP = 0x00100000d0bL;
  public static final long LOGICAL_COLOR_F0_RED = 0x00100000d0cL;
  public static final long LOGICAL_COLOR_F1_GREEN = 0x00100000d0dL;
  public static final long LOGICAL_COLOR_F2_YELLOW = 0x00100000d0eL;
  public static final long LOGICAL_COLOR_F3_BLUE = 0x00100000d0fL;
  public static final long LOGICAL_COLOR_F4_GREY = 0x00100000d10L;
  public static final long LOGICAL_COLOR_F5_BROWN = 0x00100000d11L;
  public static final long LOGICAL_CLOSED_CAPTION_TOGGLE = 0x00100000d12L;
  public static final long LOGICAL_DIMMER = 0x00100000d13L;
  public static final long LOGICAL_DISPLAY_SWAP = 0x00100000d14L;
  public static final long LOGICAL_EXIT = 0x00100000d15L;
  public static final long LOGICAL_FAVORITE_CLEAR0 = 0x00100000d16L;
  public static final long LOGICAL_FAVORITE_CLEAR1 = 0x00100000d17L;
  public static final long LOGICAL_FAVORITE_CLEAR2 = 0x00100000d18L;
  public static final long LOGICAL_FAVORITE_CLEAR3 = 0x00100000d19L;
  public static final long LOGICAL_FAVORITE_RECALL0 = 0x00100000d1aL;
  public static final long LOGICAL_FAVORITE_RECALL1 = 0x00100000d1bL;
  public static final long LOGICAL_FAVORITE_RECALL2 = 0x00100000d1cL;
  public static final long LOGICAL_FAVORITE_RECALL3 = 0x00100000d1dL;
  public static final long LOGICAL_FAVORITE_STORE0 = 0x00100000d1eL;
  public static final long LOGICAL_FAVORITE_STORE1 = 0x00100000d1fL;
  public static final long LOGICAL_FAVORITE_STORE2 = 0x00100000d20L;
  public static final long LOGICAL_FAVORITE_STORE3 = 0x00100000d21L;
  public static final long LOGICAL_GUIDE = 0x00100000d22L;
  public static final long LOGICAL_GUIDE_NEXT_DAY = 0x00100000d23L;
  public static final long LOGICAL_GUIDE_PREVIOUS_DAY = 0x00100000d24L;
  public static final long LOGICAL_INFO = 0x00100000d25L;
  public static final long LOGICAL_INSTANT_REPLAY = 0x00100000d26L;
  public static final long LOGICAL_LINK = 0x00100000d27L;
  public static final long LOGICAL_LIST_PROGRAM = 0x00100000d28L;
  public static final long LOGICAL_LIVE_CONTENT = 0x00100000d29L;
  public static final long LOGICAL_LOCK = 0x00100000d2aL;
  public static final long LOGICAL_MEDIA_APPS = 0x00100000d2bL;
  public static final long LOGICAL_MEDIA_FAST_FORWARD = 0x00100000d2cL;
  public static final long LOGICAL_MEDIA_LAST = 0x00100000d2dL;
  public static final long LOGICAL_MEDIA_PAUSE = 0x00100000d2eL;
  public static final long LOGICAL_MEDIA_PLAY = 0x00100000d2fL;
  public static final long LOGICAL_MEDIA_RECORD = 0x00100000d30L;
  public static final long LOGICAL_MEDIA_REWIND = 0x00100000d31L;
  public static final long LOGICAL_MEDIA_SKIP = 0x00100000d32L;
  public static final long LOGICAL_NEXT_FAVORITE_CHANNEL = 0x00100000d33L;
  public static final long LOGICAL_NEXT_USER_PROFILE = 0x00100000d34L;
  public static final long LOGICAL_ON_DEMAND = 0x00100000d35L;
  public static final long LOGICAL_P_IN_P_DOWN = 0x00100000d36L;
  public static final long LOGICAL_P_IN_P_MOVE = 0x00100000d37L;
  public static final long LOGICAL_P_IN_P_TOGGLE = 0x00100000d38L;
  public static final long LOGICAL_P_IN_P_UP = 0x00100000d39L;
  public static final long LOGICAL_PLAY_SPEED_DOWN = 0x00100000d3aL;
  public static final long LOGICAL_PLAY_SPEED_RESET = 0x00100000d3bL;
  public static final long LOGICAL_PLAY_SPEED_UP = 0x00100000d3cL;
  public static final long LOGICAL_RANDOM_TOGGLE = 0x00100000d3dL;
  public static final long LOGICAL_RC_LOW_BATTERY = 0x00100000d3eL;
  public static final long LOGICAL_RECORD_SPEED_NEXT = 0x00100000d3fL;
  public static final long LOGICAL_RF_BYPASS = 0x00100000d40L;
  public static final long LOGICAL_SCAN_CHANNELS_TOGGLE = 0x00100000d41L;
  public static final long LOGICAL_SCREEN_MODE_NEXT = 0x00100000d42L;
  public static final long LOGICAL_SETTINGS = 0x00100000d43L;
  public static final long LOGICAL_SPLIT_SCREEN_TOGGLE = 0x00100000d44L;
  public static final long LOGICAL_STB_INPUT = 0x00100000d45L;
  public static final long LOGICAL_STB_POWER = 0x00100000d46L;
  public static final long LOGICAL_SUBTITLE = 0x00100000d47L;
  public static final long LOGICAL_TELETEXT = 0x00100000d48L;
  public static final long LOGICAL_TV = 0x00100000d49L;
  public static final long LOGICAL_TV_INPUT = 0x00100000d4aL;
  public static final long LOGICAL_TV_POWER = 0x00100000d4bL;
  public static final long LOGICAL_VIDEO_MODE_NEXT = 0x00100000d4cL;
  public static final long LOGICAL_WINK = 0x00100000d4dL;
  public static final long LOGICAL_ZOOM_TOGGLE = 0x00100000d4eL;
  public static final long LOGICAL_DVR = 0x00100000d4fL;
  public static final long LOGICAL_MEDIA_AUDIO_TRACK = 0x00100000d50L;
  public static final long LOGICAL_MEDIA_SKIP_BACKWARD = 0x00100000d51L;
  public static final long LOGICAL_MEDIA_SKIP_FORWARD = 0x00100000d52L;
  public static final long LOGICAL_MEDIA_STEP_BACKWARD = 0x00100000d53L;
  public static final long LOGICAL_MEDIA_STEP_FORWARD = 0x00100000d54L;
  public static final long LOGICAL_MEDIA_TOP_MENU = 0x00100000d55L;
  public static final long LOGICAL_NAVIGATE_IN = 0x00100000d56L;
  public static final long LOGICAL_NAVIGATE_NEXT = 0x00100000d57L;
  public static final long LOGICAL_NAVIGATE_OUT = 0x00100000d58L;
  public static final long LOGICAL_NAVIGATE_PREVIOUS = 0x00100000d59L;
  public static final long LOGICAL_PAIRING = 0x00100000d5aL;
  public static final long LOGICAL_MEDIA_CLOSE = 0x00100000d5bL;
  public static final long LOGICAL_AUDIO_BASS_BOOST_TOGGLE = 0x00100000e02L;
  public static final long LOGICAL_AUDIO_TREBLE_DOWN = 0x00100000e04L;
  public static final long LOGICAL_AUDIO_TREBLE_UP = 0x00100000e05L;
  public static final long LOGICAL_MICROPHONE_TOGGLE = 0x00100000e06L;
  public static final long LOGICAL_MICROPHONE_VOLUME_DOWN = 0x00100000e07L;
  public static final long LOGICAL_MICROPHONE_VOLUME_UP = 0x00100000e08L;
  public static final long LOGICAL_MICROPHONE_VOLUME_MUTE = 0x00100000e09L;
  public static final long LOGICAL_SPEECH_CORRECTION_LIST = 0x00100000f01L;
  public static final long LOGICAL_SPEECH_INPUT_TOGGLE = 0x00100000f02L;
  public static final long LOGICAL_APP_SWITCH = 0x00100001001L;
  public static final long LOGICAL_CALL = 0x00100001002L;
  public static final long LOGICAL_CAMERA_FOCUS = 0x00100001003L;
  public static final long LOGICAL_END_CALL = 0x00100001004L;
  public static final long LOGICAL_GO_BACK = 0x00100001005L;
  public static final long LOGICAL_GO_HOME = 0x00100001006L;
  public static final long LOGICAL_HEADSET_HOOK = 0x00100001007L;
  public static final long LOGICAL_LAST_NUMBER_REDIAL = 0x00100001008L;
  public static final long LOGICAL_NOTIFICATION = 0x00100001009L;
  public static final long LOGICAL_MANNER_MODE = 0x0010000100aL;
  public static final long LOGICAL_VOICE_DIAL = 0x0010000100bL;
  public static final long LOGICAL_TV3_D_MODE = 0x00100001101L;
  public static final long LOGICAL_TV_ANTENNA_CABLE = 0x00100001102L;
  public static final long LOGICAL_TV_AUDIO_DESCRIPTION = 0x00100001103L;
  public static final long LOGICAL_TV_AUDIO_DESCRIPTION_MIX_DOWN = 0x00100001104L;
  public static final long LOGICAL_TV_AUDIO_DESCRIPTION_MIX_UP = 0x00100001105L;
  public static final long LOGICAL_TV_CONTENTS_MENU = 0x00100001106L;
  public static final long LOGICAL_TV_DATA_SERVICE = 0x00100001107L;
  public static final long LOGICAL_TV_INPUT_COMPONENT1 = 0x00100001108L;
  public static final long LOGICAL_TV_INPUT_COMPONENT2 = 0x00100001109L;
  public static final long LOGICAL_TV_INPUT_COMPOSITE1 = 0x0010000110aL;
  public static final long LOGICAL_TV_INPUT_COMPOSITE2 = 0x0010000110bL;
  public static final long LOGICAL_TV_INPUT_HD_M1 = 0x0010000110cL;
  public static final long LOGICAL_TV_INPUT_HD_M2 = 0x0010000110dL;
  public static final long LOGICAL_TV_INPUT_HD_M3 = 0x0010000110eL;
  public static final long LOGICAL_TV_INPUT_HD_M4 = 0x0010000110fL;
  public static final long LOGICAL_TV_INPUT_V_G1 = 0x00100001110L;
  public static final long LOGICAL_TV_MEDIA_CONTEXT = 0x00100001111L;
  public static final long LOGICAL_TV_NETWORK = 0x00100001112L;
  public static final long LOGICAL_TV_NUMBER_ENTRY = 0x00100001113L;
  public static final long LOGICAL_TV_RADIO_SERVICE = 0x00100001114L;
  public static final long LOGICAL_TV_SATELLITE = 0x00100001115L;
  public static final long LOGICAL_TV_SATELLITE_B_S = 0x00100001116L;
  public static final long LOGICAL_TV_SATELLITE_C_S = 0x00100001117L;
  public static final long LOGICAL_TV_SATELLITE_TOGGLE = 0x00100001118L;
  public static final long LOGICAL_TV_TERRESTRIAL_ANALOG = 0x00100001119L;
  public static final long LOGICAL_TV_TERRESTRIAL_DIGITAL = 0x0010000111aL;
  public static final long LOGICAL_TV_TIMER = 0x0010000111bL;
  public static final long LOGICAL_KEY11 = 0x00100001201L;
  public static final long LOGICAL_KEY12 = 0x00100001202L;
  public static final long LOGICAL_SUSPEND = 0x00200000000L;
  public static final long LOGICAL_RESUME = 0x00200000001L;
  public static final long LOGICAL_SLEEP = 0x00200000002L;
  public static final long LOGICAL_ABORT = 0x00200000003L;
  public static final long LOGICAL_LANG1 = 0x00200000010L;
  public static final long LOGICAL_LANG2 = 0x00200000011L;
  public static final long LOGICAL_LANG3 = 0x00200000012L;
  public static final long LOGICAL_LANG4 = 0x00200000013L;
  public static final long LOGICAL_LANG5 = 0x00200000014L;
  public static final long LOGICAL_INTL_BACKSLASH = 0x00200000020L;
  public static final long LOGICAL_INTL_RO = 0x00200000021L;
  public static final long LOGICAL_INTL_YEN = 0x00200000022L;
  public static final long LOGICAL_CONTROL_LEFT = 0x00200000100L;
  public static final long LOGICAL_CONTROL_RIGHT = 0x00200000101L;
  public static final long LOGICAL_SHIFT_LEFT = 0x00200000102L;
  public static final long LOGICAL_SHIFT_RIGHT = 0x00200000103L;
  public static final long LOGICAL_ALT_LEFT = 0x00200000104L;
  public static final long LOGICAL_ALT_RIGHT = 0x00200000105L;
  public static final long LOGICAL_META_LEFT = 0x00200000106L;
  public static final long LOGICAL_META_RIGHT = 0x00200000107L;
  public static final long LOGICAL_CONTROL = 0x002000001f0L;
  public static final long LOGICAL_SHIFT = 0x002000001f2L;
  public static final long LOGICAL_ALT = 0x002000001f4L;
  public static final long LOGICAL_META = 0x002000001f6L;
  public static final long LOGICAL_NUMPAD_ENTER = 0x0020000020dL;
  public static final long LOGICAL_NUMPAD_PAREN_LEFT = 0x00200000228L;
  public static final long LOGICAL_NUMPAD_PAREN_RIGHT = 0x00200000229L;
  public static final long LOGICAL_NUMPAD_MULTIPLY = 0x0020000022aL;
  public static final long LOGICAL_NUMPAD_ADD = 0x0020000022bL;
  public static final long LOGICAL_NUMPAD_COMMA = 0x0020000022cL;
  public static final long LOGICAL_NUMPAD_SUBTRACT = 0x0020000022dL;
  public static final long LOGICAL_NUMPAD_DECIMAL = 0x0020000022eL;
  public static final long LOGICAL_NUMPAD_DIVIDE = 0x0020000022fL;
  public static final long LOGICAL_NUMPAD0 = 0x00200000230L;
  public static final long LOGICAL_NUMPAD1 = 0x00200000231L;
  public static final long LOGICAL_NUMPAD2 = 0x00200000232L;
  public static final long LOGICAL_NUMPAD3 = 0x00200000233L;
  public static final long LOGICAL_NUMPAD4 = 0x00200000234L;
  public static final long LOGICAL_NUMPAD5 = 0x00200000235L;
  public static final long LOGICAL_NUMPAD6 = 0x00200000236L;
  public static final long LOGICAL_NUMPAD7 = 0x00200000237L;
  public static final long LOGICAL_NUMPAD8 = 0x00200000238L;
  public static final long LOGICAL_NUMPAD9 = 0x00200000239L;
  public static final long LOGICAL_NUMPAD_EQUAL = 0x0020000023dL;
  public static final long LOGICAL_GAME_BUTTON1 = 0x00200000301L;
  public static final long LOGICAL_GAME_BUTTON2 = 0x00200000302L;
  public static final long LOGICAL_GAME_BUTTON3 = 0x00200000303L;
  public static final long LOGICAL_GAME_BUTTON4 = 0x00200000304L;
  public static final long LOGICAL_GAME_BUTTON5 = 0x00200000305L;
  public static final long LOGICAL_GAME_BUTTON6 = 0x00200000306L;
  public static final long LOGICAL_GAME_BUTTON7 = 0x00200000307L;
  public static final long LOGICAL_GAME_BUTTON8 = 0x00200000308L;
  public static final long LOGICAL_GAME_BUTTON9 = 0x00200000309L;
  public static final long LOGICAL_GAME_BUTTON10 = 0x0020000030aL;
  public static final long LOGICAL_GAME_BUTTON11 = 0x0020000030bL;
  public static final long LOGICAL_GAME_BUTTON12 = 0x0020000030cL;
  public static final long LOGICAL_GAME_BUTTON13 = 0x0020000030dL;
  public static final long LOGICAL_GAME_BUTTON14 = 0x0020000030eL;
  public static final long LOGICAL_GAME_BUTTON15 = 0x0020000030fL;
  public static final long LOGICAL_GAME_BUTTON16 = 0x00200000310L;
  public static final long LOGICAL_GAME_BUTTON_A = 0x00200000311L;
  public static final long LOGICAL_GAME_BUTTON_B = 0x00200000312L;
  public static final long LOGICAL_GAME_BUTTON_C = 0x00200000313L;
  public static final long LOGICAL_GAME_BUTTON_LEFT1 = 0x00200000314L;
  public static final long LOGICAL_GAME_BUTTON_LEFT2 = 0x00200000315L;
  public static final long LOGICAL_GAME_BUTTON_MODE = 0x00200000316L;
  public static final long LOGICAL_GAME_BUTTON_RIGHT1 = 0x00200000317L;
  public static final long LOGICAL_GAME_BUTTON_RIGHT2 = 0x00200000318L;
  public static final long LOGICAL_GAME_BUTTON_SELECT = 0x00200000319L;
  public static final long LOGICAL_GAME_BUTTON_START = 0x0020000031aL;
  public static final long LOGICAL_GAME_BUTTON_THUMB_LEFT = 0x0020000031bL;
  public static final long LOGICAL_GAME_BUTTON_THUMB_RIGHT = 0x0020000031cL;
  public static final long LOGICAL_GAME_BUTTON_X = 0x0020000031dL;
  public static final long LOGICAL_GAME_BUTTON_Y = 0x0020000031eL;
  public static final long LOGICAL_GAME_BUTTON_Z = 0x0020000031fL;
}
