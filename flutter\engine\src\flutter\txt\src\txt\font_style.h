// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_TXT_SRC_TXT_FONT_STYLE_H_
#define FLUTTER_TXT_SRC_TXT_FONT_STYLE_H_

namespace txt {

enum class FontStyle {
  // NOLINTBEGIN(readability-identifier-naming)
  normal,
  italic,
  // NOLINTEND(readability-identifier-naming)
};

}  // namespace txt

#endif  // FLUTTER_TXT_SRC_TXT_FONT_STYLE_H_
