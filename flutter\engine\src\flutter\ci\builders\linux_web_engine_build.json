{"builds": [{"name": "ci/wasm_release", "drone_dimensions": ["device_type=none", "os=Linux", "cores=32"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "download_emsdk": true}, "gn": ["--web", "--runtime-mode=release", "--no-rbe", "--no-goma"], "ninja": {"config": "wasm_release", "targets": ["flutter/web_sdk:flutter_web_sdk_archive"]}, "archives": [{"name": "wasm_release", "base_path": "out/wasm_release/zip_archives/", "type": "gcs", "include_paths": ["out/wasm_release/zip_archives/flutter-web-sdk.zip"], "realm": "production"}], "cas_archive": false}], "tests": []}