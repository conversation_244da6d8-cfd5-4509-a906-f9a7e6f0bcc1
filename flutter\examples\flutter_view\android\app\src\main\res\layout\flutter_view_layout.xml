<!-- Copyright 2014 The Flutter Authors. All rights reserved.
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file. -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:orientation="vertical"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  >

  <io.flutter.embedding.android.FlutterView
    android:id="@+id/flutter_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="1"
    />

  <androidx.coordinatorlayout.widget.CoordinatorLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="1"
    android:background="@color/grey"
    >

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:orientation="vertical"
      >

      <TextView
        android:id="@+id/button_tap"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:text="@string/button_tap"
        android:layout_weight="1"
        android:gravity="center"
        android:textSize="@dimen/font_size"
        />

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/android"
        android:layout_margin="@dimen/edge_margin"
        android:textSize="@dimen/platform_label_font_size"
        android:background="@color/grey"
        />

    </LinearLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
      android:id="@+id/button"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="bottom|right"
      android:elevation="@dimen/fab_elevation_resting"
      app:pressedTranslationZ="@dimen/fab_elevation_pressed"
      android:layout_margin="@dimen/edge_margin"
      android:src="@drawable/ic_add_black_24dp"
      android:clickable="true"
      app:rippleColor="@color/grey"
      app:fabSize="normal"
      app:backgroundTint="@color/white"
      />

  </androidx.coordinatorlayout.widget.CoordinatorLayout>

</LinearLayout>
