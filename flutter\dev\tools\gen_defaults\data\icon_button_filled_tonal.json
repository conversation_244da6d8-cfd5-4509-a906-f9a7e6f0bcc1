{"version": "6_1_0", "md.comp.filled-tonal-icon-button.container.color": "secondaryContainer", "md.comp.filled-tonal-icon-button.container.height": 40.0, "md.comp.filled-tonal-icon-button.container.shape": "md.sys.shape.corner.full", "md.comp.filled-tonal-icon-button.container.width": 40.0, "md.comp.filled-tonal-icon-button.disabled.container.color": "onSurface", "md.comp.filled-tonal-icon-button.disabled.container.opacity": 0.12, "md.comp.filled-tonal-icon-button.disabled.icon.color": "onSurface", "md.comp.filled-tonal-icon-button.disabled.icon.opacity": 0.38, "md.comp.filled-tonal-icon-button.focus.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.focus.indicator.color": "secondary", "md.comp.filled-tonal-icon-button.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.filled-tonal-icon-button.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.filled-tonal-icon-button.focus.state-layer.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.filled-tonal-icon-button.hover.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.hover.state-layer.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.filled-tonal-icon-button.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.icon.size": 24.0, "md.comp.filled-tonal-icon-button.pressed.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.pressed.state-layer.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.filled-tonal-icon-button.selected.container.color": "secondaryContainer", "md.comp.filled-tonal-icon-button.toggle.selected.focus.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.toggle.selected.focus.state-layer.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.toggle.selected.hover.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.toggle.selected.hover.state-layer.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.toggle.selected.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.toggle.selected.pressed.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.toggle.selected.pressed.state-layer.color": "onSecondaryContainer", "md.comp.filled-tonal-icon-button.toggle.unselected.focus.icon.color": "onSurfaceVariant", "md.comp.filled-tonal-icon-button.toggle.unselected.focus.state-layer.color": "onSurfaceVariant", "md.comp.filled-tonal-icon-button.toggle.unselected.hover.icon.color": "onSurfaceVariant", "md.comp.filled-tonal-icon-button.toggle.unselected.hover.state-layer.color": "onSurfaceVariant", "md.comp.filled-tonal-icon-button.toggle.unselected.icon.color": "onSurfaceVariant", "md.comp.filled-tonal-icon-button.toggle.unselected.pressed.icon.color": "onSurfaceVariant", "md.comp.filled-tonal-icon-button.toggle.unselected.pressed.state-layer.color": "onSurfaceVariant", "md.comp.filled-tonal-icon-button.unselected.container.color": "surfaceContainerHighest"}