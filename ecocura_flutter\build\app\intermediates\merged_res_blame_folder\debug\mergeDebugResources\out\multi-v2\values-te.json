{"logs": [{"outputFile": "com.example.ecocura_flutter.app-mergeDebugResources-47:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,914,1005,1097,1192,1286,1387,1480,1575,1670,1761,1852,1934,2048,2150,2247,2362,2465,2580,2742,2845", "endColumns": "116,111,110,89,104,118,77,76,90,91,94,93,100,92,94,94,90,90,81,113,101,96,114,102,114,161,102,79", "endOffsets": "217,329,440,530,635,754,832,909,1000,1092,1187,1281,1382,1475,1570,1665,1756,1847,1929,2043,2145,2242,2357,2460,2575,2737,2840,2920"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,914,1005,1097,1192,1286,1387,1480,1575,1670,1761,1852,1934,2048,2150,2247,2362,2465,2580,2742,6847", "endColumns": "116,111,110,89,104,118,77,76,90,91,94,93,100,92,94,94,90,90,81,113,101,96,114,102,114,161,102,79", "endOffsets": "217,329,440,530,635,754,832,909,1000,1092,1187,1281,1382,1475,1570,1665,1756,1847,1929,2043,2145,2242,2357,2460,2575,2737,2840,6922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7976d4e64729cb9c47971e21b0850b04\\transformed\\jetified-play-services-base-18.1.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,449,575,686,819,940,1041,1137,1282,1390,1539,1667,1814,1973,2033,2099", "endColumns": "105,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "298,448,574,685,818,939,1040,1136,1281,1389,1538,1666,1813,1972,2032,2098,2178"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3823,3933,4087,4217,4332,4469,4594,4699,4937,5086,5198,5351,5483,5634,5797,5861,5931", "endColumns": "109,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "3928,4082,4212,4327,4464,4589,4694,4794,5081,5193,5346,5478,5629,5792,5856,5926,6010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4799", "endColumns": "137", "endOffsets": "4932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3848899b7e93201e983882e2ba4294f0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,113", "endOffsets": "164,278"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2845,2959", "endColumns": "113,113", "endOffsets": "2954,3068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3073,3175,3283,3385,3486,3592,3699,6927", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3170,3278,3380,3481,3587,3694,3818,7023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6015,6199,6622,6701,7028,7197,7284", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "6083,6293,6696,6842,7192,7279,7363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f660676bf4ab7e115492941cf8444d98\\transformed\\browser-1.4.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,272,383", "endColumns": "110,105,110,106", "endOffsets": "161,267,378,485"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6088,6298,6404,6515", "endColumns": "110,105,110,106", "endOffsets": "6194,6399,6510,6617"}}]}]}