{"datePickerHourSemanticsLabelFew": "$hour", "datePickerHourSemanticsLabelMany": "$hour", "datePickerMinuteSemanticsLabelFew": "$minute minuty", "datePickerMinuteSemanticsLabelMany": "$minute minut", "timerPickerHourLabelFew": "god<PERSON>y", "timerPickerHourLabelMany": "godzin", "timerPickerMinuteLabelFew": "min", "timerPickerMinuteLabelMany": "min", "timerPickerSecondLabelFew": "s", "timerPickerSecondLabelMany": "s", "datePickerHourSemanticsLabelOne": "$hour", "datePickerHourSemanticsLabelOther": "$hour", "datePickerMinuteSemanticsLabelOne": "1 minuta", "datePickerMinuteSemanticsLabelOther": "$minute minuty", "datePickerDateOrder": "dmy", "datePickerDateTimeOrder": "date_time_dayPeriod", "anteMeridiemAbbreviation": "AM", "postMeridiemAbbreviation": "PM", "todayLabel": "D<PERSON>ś", "alertDialogLabel": "<PERSON><PERSON>", "timerPickerHourLabelOne": "<PERSON><PERSON><PERSON>", "timerPickerHourLabelOther": "god<PERSON>y", "timerPickerMinuteLabelOne": "min", "timerPickerMinuteLabelOther": "min", "timerPickerSecondLabelOne": "s", "timerPickerSecondLabelOther": "s", "cutButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "copyButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "pasteButtonLabel": "<PERSON><PERSON><PERSON>", "selectAllButtonLabel": "Wybierz wszystkie", "tabSemanticsLabel": "Karta $tabIndex z $tabCount", "modalBarrierDismissLabel": "Zamknij", "searchTextFieldPlaceholderLabel": "Szukaj", "noSpellCheckReplacementsLabel": "Brak wyników zamieniania", "menuDismissLabel": "Zamknij menu", "lookUpButtonLabel": "Sprawdź", "searchWebButtonLabel": "Szukaj w internecie", "shareButtonLabel": "Udostę<PERSON><PERSON>j…", "clearButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelButtonLabel": "<PERSON><PERSON><PERSON>", "backButtonLabel": "Wstecz"}