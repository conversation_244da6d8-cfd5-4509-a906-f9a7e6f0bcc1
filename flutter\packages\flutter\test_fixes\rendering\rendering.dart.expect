// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/rendering.dart';

void main() {
  // Changes made in https://github.com/flutter/flutter/pull/66305
  RenderStack renderStack = RenderStack();
  renderStack = RenderStack(clipBehavior: Clip.none);
  renderStack = RenderStack(clipBehavior: Clip.hardEdge);
  renderStack = RenderStack(error: '');
  renderStack.clipBehavior;

  // Changes made in https://docs.flutter.dev/release/breaking-changes/clip-behavior
  RenderListWheelViewport renderListWheelViewport = RenderListWheelViewport();
  renderListWheelViewport = RenderListWheelViewport(clipBehavior: Clip.hardEdge);
  renderListWheelViewport = RenderListWheelViewport(clipBehavior: Clip.none);
  renderListWheelViewport = RenderListWheelViewport(error: '');
  renderListWheelViewport.clipBehavior;

  // Change made in https://github.com/flutter/flutter/pull/128522
  RenderParagraph(textScaler: TextScaler.linear(math.min(123, 456)));
  RenderParagraph();
  RenderEditable(textScaler: TextScaler.linear(math.min(123, 456)));
  RenderEditable();
}
