# Flutter Engine

The Flutter Engine is a portable runtime for hosting
[Flutter](https://flutter.dev) applications. It implements Flutter's core
libraries, including animation and graphics, file and network I/O,
accessibility support, plugin architecture, and a Dart runtime and compile
toolchain. Most developers will interact with Flutter via the [Flutter
Framework](https://github.com/flutter/flutter), which provides a modern,
reactive framework, and a rich set of platform, layout and foundation widgets.

More tooling is available to make development experiences easier:

| Target            | Tool                                            |
| ----------------- | ----------------------------------------------- |
| Web               | [`felt`](lib/web_ui/README.md#using-felt)       |
| Mobile or Desktop | [`et`](tools/engine_tool/README.md#engine-tool) |

To learn about running tests, see [testing the engine](docs/testing/Testing-the-engine.md).
