{"version": "6_1_0", "md.comp.outlined-button.container.height": 40.0, "md.comp.outlined-button.container.shape": "md.sys.shape.corner.full", "md.comp.outlined-button.disabled.label-text.color": "onSurface", "md.comp.outlined-button.disabled.label-text.opacity": 0.38, "md.comp.outlined-button.disabled.outline.color": "onSurface", "md.comp.outlined-button.disabled.outline.opacity": 0.12, "md.comp.outlined-button.focus.indicator.color": "secondary", "md.comp.outlined-button.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.outlined-button.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.outlined-button.focus.label-text.color": "primary", "md.comp.outlined-button.focus.outline.color": "primary", "md.comp.outlined-button.focus.state-layer.color": "primary", "md.comp.outlined-button.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.outlined-button.hover.label-text.color": "primary", "md.comp.outlined-button.hover.outline.color": "outline", "md.comp.outlined-button.hover.state-layer.color": "primary", "md.comp.outlined-button.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.outlined-button.label-text.color": "primary", "md.comp.outlined-button.label-text.text-style": "labelLarge", "md.comp.outlined-button.outline.color": "outline", "md.comp.outlined-button.outline.width": 1.0, "md.comp.outlined-button.pressed.label-text.color": "primary", "md.comp.outlined-button.pressed.outline.color": "outline", "md.comp.outlined-button.pressed.state-layer.color": "primary", "md.comp.outlined-button.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.outlined-button.with-icon.disabled.icon.color": "onSurface", "md.comp.outlined-button.with-icon.disabled.icon.opacity": 0.38, "md.comp.outlined-button.with-icon.focus.icon.color": "primary", "md.comp.outlined-button.with-icon.hover.icon.color": "primary", "md.comp.outlined-button.with-icon.icon.color": "primary", "md.comp.outlined-button.with-icon.icon.size": 18.0, "md.comp.outlined-button.with-icon.pressed.icon.color": "primary"}