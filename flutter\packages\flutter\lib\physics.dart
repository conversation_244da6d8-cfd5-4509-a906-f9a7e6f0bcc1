// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// Simple one-dimensional physics simulations, such as springs, friction, and
/// gravity, for use in user interface animations.
///
/// To use, import `package:flutter/physics.dart`.
library physics;

export 'src/physics/clamped_simulation.dart';
export 'src/physics/friction_simulation.dart';
export 'src/physics/gravity_simulation.dart';
export 'src/physics/simulation.dart';
export 'src/physics/spring_simulation.dart';
export 'src/physics/tolerance.dart';
export 'src/physics/utils.dart';
