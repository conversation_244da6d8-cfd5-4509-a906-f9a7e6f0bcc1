# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/impeller/tools/impeller.gni")
import("//flutter/testing/testing.gni")

impeller_shaders("modern_shader_fixtures") {
  name = "modern_fixtures"

  # 2.3 adds support for framebuffer fetch in Metal.
  metal_version = "2.3"

  # Specify that framebuffer fetch is required for all backends. Compilation of
  # shaders will be gated on capability checks.
  require_framebuffer_fetch = true

  # These shaders are not performance critical and malioc analysis of shaders
  # that use framebuffer fetch is unsupported anyway.
  analyze = false

  shaders = [
    "sepia.frag",
    "sepia.vert",
    "swizzle.frag",
  ]
}

impeller_shaders("shader_fixtures") {
  name = "fixtures"

  # 2.3 adds support for framebuffer fetch in Metal.
  metal_version = "2.3"

  # Not analyzing because they are not performance critical, and mipmap uses
  # textureLod, which uses an extension that malioc does not support.
  analyze = false

  shaders = [
    "array.frag",
    "array.vert",
    "baby.frag",
    "baby.vert",
    "box_fade.frag",
    "box_fade.vert",
    "colors.frag",
    "colors.vert",
    "half.frag",
    "impeller.frag",
    "impeller.vert",
    "inactive_uniforms.frag",
    "inactive_uniforms.vert",
    "instanced_draw.frag",
    "instanced_draw.vert",
    "mipmaps.frag",
    "mipmaps.vert",
    "planet.frag",
    "planet.vert",
    "sample.comp",
    "simple.vert",
    "spec_constant.frag",
    "spec_constant.vert",
    "stage1.comp",
    "stage2.comp",
    "test_texture.frag",
    "test_texture.vert",
    "texture.frag",
    "texture.vert",
  ]

  if (impeller_enable_opengles) {
    gles_exclusions = [
      "sample.comp",
      "stage1.comp",
      "stage2.comp",
      "half.frag",
    ]
  }
}

impellerc("runtime_stages") {
  shaders = [
    "ink_sparkle.frag",
    "runtime_stage_example.frag",
    "runtime_stage_filter_example.frag",
    "runtime_stage_simple.frag",
    "runtime_stage_position.frag",
    "gradient.frag",
    "uniforms_and_sampler_1.frag",
    "uniforms_and_sampler_2.frag",
  ]
  sl_file_extension = "iplr"

  shader_target_flags = [
    "--runtime-stage-metal",
    "--runtime-stage-gles",
    "--runtime-stage-vulkan",
  ]

  iplr = true
}

test_fixtures("file_fixtures") {
  fixtures = [
    "//flutter/txt/third_party/fonts/ahem.ttf",
    "//flutter/txt/third_party/fonts/HomemadeApple.ttf",
    "//flutter/txt/third_party/fonts/NotoColorEmoji.ttf",
    "//flutter/txt/third_party/fonts/Roboto-Medium.ttf",
    "//flutter/txt/third_party/fonts/Roboto-Regular.ttf",
    "airplane.jpg",
    "bay_bridge.jpg",
    "blend_mode_dst.png",
    "blend_mode_src.png",
    "blue_noise.png",
    "boston.jpg",
    "embarcadero.jpg",
    "flutter_gpu_texture.frag",
    "flutter_gpu_texture.vert",
    "flutter_gpu_unlit.frag",
    "flutter_gpu_unlit.vert",
    "kalimba.jpg",
    "monkey.png",
    "multiple_stages.hlsl",
    "nine_patch_corners.png",
    "resources_limit.vert",
    "sample.comp",
    "sample.frag",
    "sample.vert",
    "sample_with_binding.vert",
    "simple.vert.hlsl",
    "sa%m#ple.vert",
    "stage1.comp",
    "stage2.comp",
    "struct_def_bug.vert",
    "struct_internal.frag",
    "table_mountain_nx.png",
    "table_mountain_ny.png",
    "table_mountain_nz.png",
    "table_mountain_px.png",
    "table_mountain_py.png",
    "table_mountain_pz.png",
    "test_texture.frag",
    "types.h",
    "wtf.otf",
    "texture_lookup.frag",
  ]
  if (host_os == "mac") {
    fixtures += [ "/System/Library/Fonts/Apple Color Emoji.ttc" ]
  }
  fixtures +=
      filter_include(get_target_outputs(":runtime_stages"), [ "*.iplr" ])
  deps = [ ":runtime_stages" ]
}

impellerc("flutter_gpu_shaders") {
  shaders = [
    # Temporarily build Flutter GPU test shaders as runtime stages.
    "flutter_gpu_unlit.frag",
    "flutter_gpu_unlit.vert",
    "flutter_gpu_texture.frag",
    "flutter_gpu_texture.vert",
  ]

  fixtures = rebase_path("//flutter/impeller/fixtures")
  shader_bundle = "{\"UnlitFragment\": {\"type\": \"fragment\", \"file\": \"${fixtures}/flutter_gpu_unlit.frag\"}, \"UnlitVertex\": {\"type\": \"vertex\", \"file\": \"${fixtures}/flutter_gpu_unlit.vert\"}, \"TextureFragment\": {\"type\": \"fragment\", \"file\": \"${fixtures}/flutter_gpu_texture.frag\"}, \"TextureVertex\": {\"type\": \"vertex\", \"file\": \"${fixtures}/flutter_gpu_texture.vert\"}}"
  shader_bundle_output = "playground.shaderbundle"
}

test_fixtures("flutter_gpu_fixtures") {
  dart_main = "dart_tests.dart"

  fixtures = filter_include(get_target_outputs(":flutter_gpu_shaders"),
                            [
                              "*.iplr",
                              "*.shaderbundle",
                            ])

  deps = [ ":flutter_gpu_shaders" ]
}

group("fixtures") {
  testonly = true

  public_deps = [
    ":file_fixtures",
    ":modern_shader_fixtures",
    ":shader_fixtures",
  ]
}
