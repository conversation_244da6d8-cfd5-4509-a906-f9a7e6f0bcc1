{
    include: [
         "sys/component/realm_builder_absolute.shard.cml",

        // TODO(https://fxbug.dev/404543928): Remove this shard usage when possible
        // and replace with explicit routes.
        "syslog/client.shard.cml",
        "inspect/client.shard.cml",
    ],
    facets: {
        // shell_unittests and embedder_unittests require vulkan to function.
        "fuchsia.test": { type: "vulkan" },
    },
    program: {
        // TODO(fxbug.dev/80338): Switch to gtest_runner after the filters in
        // `gtest_filters.yaml` are supported by `--test-filter` and
        // deprecated-ambient-replace-as-executable is available.
        // Equivalent to the "deprecated-ambient-replace-as-executable" sandbox
        // feature in cfv1.
        runner: "elf_test_ambient_exec_runner",
        binary: "bin/app",

        // Part of //sdk/lib/diagnostics/syslog/elf_stdio.shard.cml.
        forward_stderr_to: "log",
        forward_stdout_to: "log",
    },
    capabilities: [
        { protocol: "fuchsia.test.Suite" },
    ],
    expose: [
        {
            protocol: "fuchsia.test.Suite",
            from: "self",
        },
    ],
    use: [
        {
            protocol: [
                "fuchsia.process.Launcher",
                "fuchsia.tracing.provider.Registry",
                "fuchsia.vulkan.loader.Loader",
            ],
        },
        {
            storage: "tmp",
            path: "/tmp",
        },
    ],
}
