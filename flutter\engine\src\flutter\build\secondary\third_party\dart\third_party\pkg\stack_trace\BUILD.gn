# Copyright 2021 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# TODO(flutter/flutter#85356): This file was originally generated by the
# fuchsia.git script: `package_importer.py`. The generated `BUILD.gn` files were
# copied to the flutter repo to support `dart_library` targets used for
# Flutter-Fuchsia integration tests. This file can be maintained by hand, but it
# would be better to implement a script for Flutter, to either generate these
# BUILD.gn files or dynamically generate the GN targets.

import("//flutter/tools/fuchsia/dart/dart_library.gni")

dart_library("stack_trace") {
  package_name = "stack_trace"

  language_version = "2.12"

  deps = [ "$dart_src/third_party/pkg/path" ]

  sources = [
    "src/chain.dart",
    "src/frame.dart",
    "src/lazy_chain.dart",
    "src/lazy_trace.dart",
    "src/stack_zone_specification.dart",
    "src/trace.dart",
    "src/unparsed_frame.dart",
    "src/utils.dart",
    "src/vm_trace.dart",
    "stack_trace.dart",
  ]
}
