# Copyright 2021 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# TODO(flutter/flutter#85356): This file was originally generated by the
# fuchsia.git script: `package_importer.py`. The generated `BUILD.gn` files were
# copied to the flutter repo to support `dart_library` targets used for
# Flutter-Fuchsia integration tests. This file can be maintained by hand, but it
# would be better to implement a script for Flutter, to either generate these
# BUILD.gn files or dynamically generate the GN targets.

import("//flutter/tools/fuchsia/dart/dart_library.gni")

dart_library("matcher") {
  package_name = "matcher"

  language_version = "2.12"

  deps = [ "$dart_src/third_party/pkg/stack_trace" ]

  sources = [
    "expect.dart",
    "matcher.dart",
    "mirror_matchers.dart",
    "src/core_matchers.dart",
    "src/custom_matcher.dart",
    "src/description.dart",
    "src/equals_matcher.dart",
    "src/error_matchers.dart",
    "src/expect/async_matcher.dart",
    "src/expect/expect.dart",
    "src/expect/expect_async.dart",
    "src/expect/future_matchers.dart",
    "src/expect/never_called.dart",
    "src/expect/prints_matcher.dart",
    "src/expect/stream_matcher.dart",
    "src/expect/stream_matchers.dart",
    "src/expect/throws_matcher.dart",
    "src/expect/throws_matchers.dart",
    "src/expect/util/placeholder.dart",
    "src/expect/util/pretty_print.dart",
    "src/feature_matcher.dart",
    "src/having_matcher.dart",
    "src/interfaces.dart",
    "src/iterable_matchers.dart",
    "src/map_matchers.dart",
    "src/numeric_matchers.dart",
    "src/operator_matchers.dart",
    "src/order_matchers.dart",
    "src/pretty_print.dart",
    "src/string_matchers.dart",
    "src/type_matcher.dart",
    "src/util.dart",
  ]
}
