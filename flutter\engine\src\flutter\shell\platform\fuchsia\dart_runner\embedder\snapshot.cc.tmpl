// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file is linked into the dart executable when it has a snapshot
// linked into it.

#include "embedder/snapshot.h"

namespace dart_runner {

// The string on the next line will be filled in with the contents of the
// generated snapshot binary file for the vm isolate. This string forms the
// content of the dart vm isolate snapshot which is loaded into the vm isolate.
static const uint8_t vm_isolate_snapshot_buffer_[] = { % s};
uint8_t const* const vm_isolate_snapshot_buffer = vm_isolate_snapshot_buffer_;

// The string on the next line will be filled in with the contents of the
// generated snapshot binary file for a regular dart isolate.
// This string forms the content of a regular dart isolate snapshot which
// is loaded into an isolate when it is created.
static const uint8_t isolate_snapshot_buffer_[] = { % s};
uint8_t const* const isolate_snapshot_buffer = isolate_snapshot_buffer_;

}  // namespace dart_runner
