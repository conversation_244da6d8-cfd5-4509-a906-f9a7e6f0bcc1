<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/build" />
      <excludeFolder url="file://$MODULE_DIR$/test/data/dart_dependencies_test/asci_casing/build" />
      <excludeFolder url="file://$MODULE_DIR$/test/data/dart_dependencies_test/bad_import/build" />
      <excludeFolder url="file://$MODULE_DIR$/test/data/dart_dependencies_test/bad_package/build" />
      <excludeFolder url="file://$MODULE_DIR$/test/data/dart_dependencies_test/bad_path/build" />
      <excludeFolder url="file://$MODULE_DIR$/test/data/dart_dependencies_test/changed_sdk_location/build" />
      <excludeFolder url="file://$MODULE_DIR$/test/data/dart_dependencies_test/good/build" />
      <excludeFolder url="file://$MODULE_DIR$/test/data/dart_dependencies_test/syntax_error/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
    <orderEntry type="library" name="Flutter Plugins" level="project" />
  </component>
</module>
