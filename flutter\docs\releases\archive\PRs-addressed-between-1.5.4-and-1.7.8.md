# PRs addressed in `flutter/flutter` between `b593f5167bce84fb3cad5c258477bf3abc1b14eb` and `d51fd86`


## framework - 235 PRs

[#21896](https://github.com/flutter/flutter/pull/21896) Bottom sheet scrolling

[#28834](https://github.com/flutter/flutter/pull/28834) Sliver animated list

[#29188](https://github.com/flutter/flutter/pull/29188) Fix 25807: implement move in sliver multibox widget

[#29677](https://github.com/flutter/flutter/pull/29677) Fix calculation of hero rectTween when Navigator isn't fullscreen

[#29683](https://github.com/flutter/flutter/pull/29683) Show/hide toolbar and handles based on device kind

[#29809](https://github.com/flutter/flutter/pull/29809) Fix text selection toolbar appearing under obstructions

[#29824](https://github.com/flutter/flutter/pull/29824) Cupertino localization step 8: create a gen_cupertino_localizations and generate one for cupertino english and french

[#29954](https://github.com/flutter/flutter/pull/29954) Cupertino localization step 9: add tests

[#30040](https://github.com/flutter/flutter/pull/30040) Implement focus traversal for desktop platforms, shoehorn edition.

[#30076](https://github.com/flutter/flutter/pull/30076) Implements FocusTraversalPolicy and DefaultFocusTraversal features.

[#30129](https://github.com/flutter/flutter/pull/30129) Fix refresh control in the gallery demo, update comments

[#30224](https://github.com/flutter/flutter/pull/30224) Cupertino localization step 10: update the flutter_localizations README

[#30339](https://github.com/flutter/flutter/pull/30339) Add buttons to gestures

[#30388](https://github.com/flutter/flutter/pull/30388) Add hintStyle in SearchDelegate

[#30406](https://github.com/flutter/flutter/pull/30406) Add binaryMessenger constructor argument to platform channels

[#30572](https://github.com/flutter/flutter/pull/30572) [Material] Adaptive Slider constructor

[#30579](https://github.com/flutter/flutter/pull/30579) PointerDownEvent and PointerMoveEvent default `buttons` to 1

[#30612](https://github.com/flutter/flutter/pull/30612) Added required parameters to FlexibleSpaceBarSettings

[#30796](https://github.com/flutter/flutter/pull/30796) Unbounded TextField width error

[#30874](https://github.com/flutter/flutter/pull/30874) Redo "Remove pressure customization from some pointer events"

[#30884](https://github.com/flutter/flutter/pull/30884) [Material] Update TabController to support dynamic Tabs

[#30942](https://github.com/flutter/flutter/pull/30942) rectMoreOrLess equals, prep for 64bit rects

[#30979](https://github.com/flutter/flutter/pull/30979) fix issue 30526: rounding error

[#30983](https://github.com/flutter/flutter/pull/30983) Refactor core uses of FlutterError.

[#30988](https://github.com/flutter/flutter/pull/30988) Tight Paragraph Width

[#31018](https://github.com/flutter/flutter/pull/31018) [Material] selected/unselected label styles + icon themes on BottomNavigationBar

[#31025](https://github.com/flutter/flutter/pull/31025) added `scrimColor` property in Scaffold widget

[#31093](https://github.com/flutter/flutter/pull/31093) Make the matchesGoldenFile docs link to an explanation of how to create golden image files

[#31095](https://github.com/flutter/flutter/pull/31095) Add buttons customization to WidgetController and related testing classes

[#31097](https://github.com/flutter/flutter/pull/31097) Fix text field selection toolbar under Opacity

[#31227](https://github.com/flutter/flutter/pull/31227) Adding CupertinoTabController

[#31228](https://github.com/flutter/flutter/pull/31228) Fix ExpansionPanelList Duplicate Global Keys Exception

[#31275](https://github.com/flutter/flutter/pull/31275) Update SnackBar to allow for support of the new style from Material spec

[#31291](https://github.com/flutter/flutter/pull/31291) Add some docs to StatefulBuilder

[#31294](https://github.com/flutter/flutter/pull/31294) Improve Radio Documentation with Example

[#31295](https://github.com/flutter/flutter/pull/31295) Improve ThemeData.accentColor connection to secondary color

[#31316](https://github.com/flutter/flutter/pull/31316) Add InkWell docs on transitions and ink splash clipping

[#31317](https://github.com/flutter/flutter/pull/31317) Add docs to AppBar

[#31318](https://github.com/flutter/flutter/pull/31318) Add BottomSheetTheme to enable theming color, elevation, shape of BottomSheet

[#31326](https://github.com/flutter/flutter/pull/31326) Add more shuffle cupertino icons

[#31332](https://github.com/flutter/flutter/pull/31332) iOS selection handles are invisible

[#31333](https://github.com/flutter/flutter/pull/31333) Clean up flutter_test/test/controller_test.dart

[#31420](https://github.com/flutter/flutter/pull/31420) Add more breadcrumb docs to Transformation

[#31421](https://github.com/flutter/flutter/pull/31421) Add Widget of the Week video to SizedBox

[#31438](https://github.com/flutter/flutter/pull/31438) Implements focus handling and hover for Material buttons.

[#31464](https://github.com/flutter/flutter/pull/31464) CupertinoPicker fidelity revision

[#31485](https://github.com/flutter/flutter/pull/31485) Prevent exception being thrown on hasScrolledBody

[#31493](https://github.com/flutter/flutter/pull/31493) Keycode generation doc fix

[#31497](https://github.com/flutter/flutter/pull/31497) Revert "Fix 25807: implement move for sliver multibox widget (#29188)"

[#31502](https://github.com/flutter/flutter/pull/31502) Improve Tabs documentation

[#31514](https://github.com/flutter/flutter/pull/31514) Date picker layout exceptions

[#31520](https://github.com/flutter/flutter/pull/31520) Don't add empty OpacityLayer to the engine

[#31538](https://github.com/flutter/flutter/pull/31538) Fix typo in docs

[#31566](https://github.com/flutter/flutter/pull/31566) TimePicker moves to minute mode after hour selection

[#31568](https://github.com/flutter/flutter/pull/31568) fix transform assert

[#31574](https://github.com/flutter/flutter/pull/31574) Improve RadioListTile Callback Behavior Consistency

[#31581](https://github.com/flutter/flutter/pull/31581) Fix Exception on Nested TabBarView disposal

[#31600](https://github.com/flutter/flutter/pull/31600) Re-enable const

[#31619](https://github.com/flutter/flutter/pull/31619) Fix the documentation for UiKitView#creationParams

[#31623](https://github.com/flutter/flutter/pull/31623) fix edge swiping and dropping back at starting point

[#31634](https://github.com/flutter/flutter/pull/31634) Improve canvas example in sample dart ui app

[#31644](https://github.com/flutter/flutter/pull/31644) Cupertino localization step 12: push translation for all supported languages

[#31662](https://github.com/flutter/flutter/pull/31662) added shape property to SliverAppBar

[#31681](https://github.com/flutter/flutter/pull/31681) [Material] Create a themable Range Slider (continuous and discrete)

[#31687](https://github.com/flutter/flutter/pull/31687) Center iOS caret, remove constant offsets that do not scale

[#31693](https://github.com/flutter/flutter/pull/31693) Adds a note to Radio's/RadioListTile's onChange

[#31696](https://github.com/flutter/flutter/pull/31696) Attempt to reduce usage of runtimeType

[#31699](https://github.com/flutter/flutter/pull/31699) Re-land: Add support for Tooltip hover

[#31701](https://github.com/flutter/flutter/pull/31701) Add more asserts to check matrix validity

[#31761](https://github.com/flutter/flutter/pull/31761) Support clipBehavior changes in hot reload

[#31763](https://github.com/flutter/flutter/pull/31763) Fix ScrollbarPainter thumbExtent calculation and add padding

[#31798](https://github.com/flutter/flutter/pull/31798) Fix tab indentation

[#31802](https://github.com/flutter/flutter/pull/31802) Reland "Fix text field selection toolbar under Opacity (#31097)"

[#31804](https://github.com/flutter/flutter/pull/31804) only build asset when there is asset declared in pubspec

[#31819](https://github.com/flutter/flutter/pull/31819) Redo: Add buttons to gestures

[#31822](https://github.com/flutter/flutter/pull/31822) remove unnecessary artificial delay in catalog example

[#31824](https://github.com/flutter/flutter/pull/31824) fix FlutterDriver timeout

[#31832](https://github.com/flutter/flutter/pull/31832) Allow DSS to be dragged when its children do not fill extent

[#31851](https://github.com/flutter/flutter/pull/31851) Add documentation to Navigator

[#31852](https://github.com/flutter/flutter/pull/31852) Text selection handles are sometimes not interactive

[#31861](https://github.com/flutter/flutter/pull/31861) Add Horizontal Padding to Constrained Chip Label Calculations

[#31890](https://github.com/flutter/flutter/pull/31890) apply fp hack to Flex

[#31894](https://github.com/flutter/flutter/pull/31894) Introduce separate HitTestResults for Box and Sliver

[#31902](https://github.com/flutter/flutter/pull/31902) Updated primaryColor docs to refer to colorScheme properties

[#31903](https://github.com/flutter/flutter/pull/31903) Extract TODO comment from Image.asset dardoc

[#31909](https://github.com/flutter/flutter/pull/31909) Change unfocus to unfocus the entire chain, not just the primary focus

[#31929](https://github.com/flutter/flutter/pull/31929) Sample Code & Animation for Flow Widget

[#31935](https://github.com/flutter/flutter/pull/31935) Redo#2: Add buttons to gestures

[#31938](https://github.com/flutter/flutter/pull/31938) Update scrimDrawerColor with proper const format

[#31987](https://github.com/flutter/flutter/pull/31987) Text wrap width

[#32013](https://github.com/flutter/flutter/pull/32013) Cupertino Turkish Translation

[#32025](https://github.com/flutter/flutter/pull/32025) Make Hover Listener respect transforms

[#32041](https://github.com/flutter/flutter/pull/32041) Remove deprecated decodedCacheRatioCap

[#32053](https://github.com/flutter/flutter/pull/32053) Increase TimePicker touch targets

[#32059](https://github.com/flutter/flutter/pull/32059) fix issue 14014 read only text field

[#32070](https://github.com/flutter/flutter/pull/32070) rename foreground and background to light and dark

[#32086](https://github.com/flutter/flutter/pull/32086) Fix CupertinoSliverRefreshControl onRefresh callback

[#32142](https://github.com/flutter/flutter/pull/32142) Fix RenderPointerListener so that callbacks aren't called at the wrong time.

[#32147](https://github.com/flutter/flutter/pull/32147) Added state management docs/sample to SwitchListTile

[#32177](https://github.com/flutter/flutter/pull/32177) Tab Animation Sample Video

[#32192](https://github.com/flutter/flutter/pull/32192) Transform PointerEvents to the local coordinate system of the event receiver

[#32328](https://github.com/flutter/flutter/pull/32328) Add breadcrumbs to TextOverflow

[#32340](https://github.com/flutter/flutter/pull/32340) make immutables const

[#32360](https://github.com/flutter/flutter/pull/32360) Allow flutter web to be compiled with flutter

[#32380](https://github.com/flutter/flutter/pull/32380) const everything in Driver

[#32408](https://github.com/flutter/flutter/pull/32408) More const conversions

[#32410](https://github.com/flutter/flutter/pull/32410) Add ancestor and descendant finders to Driver

[#32434](https://github.com/flutter/flutter/pull/32434) Support for replacing the TabController, after disposing the old one

[#32437](https://github.com/flutter/flutter/pull/32437) Add assert that the root widget has been attached.

[#32444](https://github.com/flutter/flutter/pull/32444) Updated some links

[#32469](https://github.com/flutter/flutter/pull/32469)  Let CupertinoNavigationBarBackButton take a custom onPressed

[#32487](https://github.com/flutter/flutter/pull/32487) Add a more meaningful message to the assertion on children

[#32527](https://github.com/flutter/flutter/pull/32527) Added 'enabled' property to the PopupMenuButton

[#32528](https://github.com/flutter/flutter/pull/32528) Tapping a modal bottom sheet should not dismiss it by default

[#32530](https://github.com/flutter/flutter/pull/32530) Add Actions to AppBar Sample Doc

[#32620](https://github.com/flutter/flutter/pull/32620) Added ScrollController to TextField

[#32638](https://github.com/flutter/flutter/pull/32638) Fix apidocs in _WidgetsAppState.basicLocaleListResolution

[#32641](https://github.com/flutter/flutter/pull/32641) Updating dart.dev related links

[#32654](https://github.com/flutter/flutter/pull/32654) Tabs code/doc cleanup

[#32686](https://github.com/flutter/flutter/pull/32686) enable lint prefer_null_aware_operators

[#32703](https://github.com/flutter/flutter/pull/32703) Add Doc Samples For CheckboxListTile, RadioListTile and SwitchListTile

[#32711](https://github.com/flutter/flutter/pull/32711) use null aware operators

[#32726](https://github.com/flutter/flutter/pull/32726) Material should not prevent ScrollNotifications from bubbling upwards

[#32730](https://github.com/flutter/flutter/pull/32730) Add reverseDuration to AnimationController

[#32776](https://github.com/flutter/flutter/pull/32776) Text field focus and hover support.

[#32823](https://github.com/flutter/flutter/pull/32823) Add enableInteractiveSelection to CupertinoTextField

[#32838](https://github.com/flutter/flutter/pull/32838) Handles hidden by keyboard

[#32842](https://github.com/flutter/flutter/pull/32842) Allow "from" hero state to survive hero animation in a push transition

[#32843](https://github.com/flutter/flutter/pull/32843) Added a missing dispose of an AnimationController that was leaking a ticker.

[#32853](https://github.com/flutter/flutter/pull/32853) Add onBytesReceived callback to consolidateHttpClientResponseBytes()

[#32857](https://github.com/flutter/flutter/pull/32857) Add debugNetworkImageHttpClientProvider

[#32904](https://github.com/flutter/flutter/pull/32904) Use reverseDuration on Tooltip and InkWell

[#32909](https://github.com/flutter/flutter/pull/32909) Documentation fix for debugProfileBuildsEnabled

[#32911](https://github.com/flutter/flutter/pull/32911) Material Long Press Text Handle Flash

[#32914](https://github.com/flutter/flutter/pull/32914) Make hover and focus not respond when buttons and fields are disabled.

[#32936](https://github.com/flutter/flutter/pull/32936) Add some sanity to the ImageStream listener API

[#32974](https://github.com/flutter/flutter/pull/32974) Fix disabled CupertinoTextField style

[#33058](https://github.com/flutter/flutter/pull/33058) Add more missing returns

[#33073](https://github.com/flutter/flutter/pull/33073) SliverAppBar shape property

[#33080](https://github.com/flutter/flutter/pull/33080) Fixed several issues with confirmDismiss handling on the LeaveBehindItem demo.

[#33090](https://github.com/flutter/flutter/pull/33090) [Material] Add support for hovered, pressed, and focused text color on Buttons.

[#33148](https://github.com/flutter/flutter/pull/33148) ExpandIcon Custom Colors

[#33152](https://github.com/flutter/flutter/pull/33152) ModalRoute resumes previous focus on didPopNext

[#33164](https://github.com/flutter/flutter/pull/33164) remove Layer.replaceWith due to no usage and no tests

[#33195](https://github.com/flutter/flutter/pull/33195) Slight clarification in the ImageCache docs

[#33226](https://github.com/flutter/flutter/pull/33226) Explain hairline rendering in BorderSide.width docs

[#33230](https://github.com/flutter/flutter/pull/33230) Framework support for font features in text styles

[#33260](https://github.com/flutter/flutter/pull/33260) Pass an async callback to testWidgets.

[#33279](https://github.com/flutter/flutter/pull/33279) Fix a problem in first focus determination.

[#33298](https://github.com/flutter/flutter/pull/33298) Add actions and keyboard shortcut map support

[#33361](https://github.com/flutter/flutter/pull/33361) (trivial) Rename test file

[#33369](https://github.com/flutter/flutter/pull/33369) Add loading support to Image

[#33370](https://github.com/flutter/flutter/pull/33370) Update FadeInImage to use new Image APIs

[#33406](https://github.com/flutter/flutter/pull/33406) Add web safe indirection to Platform.isPlatform getters

[#33431](https://github.com/flutter/flutter/pull/33431) Expose service client and app isolate in driver

[#33442](https://github.com/flutter/flutter/pull/33442) fix GridView documentation

[#33461](https://github.com/flutter/flutter/pull/33461) Various code cleanup improvements

[#33462](https://github.com/flutter/flutter/pull/33462) Fix text scaling of strut style

[#33467](https://github.com/flutter/flutter/pull/33467) fixed 33347 fill the gap during performLayout in SliverGrid and Slive…

[#33473](https://github.com/flutter/flutter/pull/33473) fix 23723 rounding error

[#33474](https://github.com/flutter/flutter/pull/33474) Fixed for DropdownButton crashing when a style was used that didn't include a fontSize

[#33475](https://github.com/flutter/flutter/pull/33475) Move declaration of semantic handlers from detectors to recognizers

[#33477](https://github.com/flutter/flutter/pull/33477) Fix onExit calling when the mouse is removed.

[#33488](https://github.com/flutter/flutter/pull/33488) use toFixedAsString and DoubleProperty in diagnosticProperties

[#33489](https://github.com/flutter/flutter/pull/33489) Remove empty file

[#33531](https://github.com/flutter/flutter/pull/33531) Fixed broken link in debugProfileBuildsEnabled documentation

[#33535](https://github.com/flutter/flutter/pull/33535) Custom height parameters for DataTable header and data rows

[#33620](https://github.com/flutter/flutter/pull/33620) Document that offsets are returned in logical pixels

[#33627](https://github.com/flutter/flutter/pull/33627) SliverFillRemaining flag for different use cases

[#33628](https://github.com/flutter/flutter/pull/33628) DataTable Custom Horizontal Padding

[#33632](https://github.com/flutter/flutter/pull/33632) Update the keycodes from source

[#33634](https://github.com/flutter/flutter/pull/33634) Let there be scroll bars

[#33653](https://github.com/flutter/flutter/pull/33653) Include advice about dispose in TextEditingController api

[#33662](https://github.com/flutter/flutter/pull/33662) Prep for engine roll

[#33663](https://github.com/flutter/flutter/pull/33663) Use conditional imports for flutter foundation libraries

[#33665](https://github.com/flutter/flutter/pull/33665) [Trivial] Move dropdownValue into State in DropdownButton sample docs

[#33674](https://github.com/flutter/flutter/pull/33674) Add documentation to ImplicitlyAnimatedWidgetState

[#33695](https://github.com/flutter/flutter/pull/33695) Add pseudo-key synonyms for keys like shift, meta, alt, and control.

[#33729](https://github.com/flutter/flutter/pull/33729) Update consolidateHttpClientResponseBytes() to use compressionState

[#33739](https://github.com/flutter/flutter/pull/33739) fixed cupertinoTextField placeholder textAlign

[#33794](https://github.com/flutter/flutter/pull/33794) Text inline widgets, TextSpan rework

[#33802](https://github.com/flutter/flutter/pull/33802) Double double tap toggles instead of error

[#33805](https://github.com/flutter/flutter/pull/33805) Fixing duplicate golden test names

[#33808](https://github.com/flutter/flutter/pull/33808) fix ExpansionPanelList merge the header semantics when it is not nece…

[#33814](https://github.com/flutter/flutter/pull/33814) Added a benchmark for ImageCache

[#33842](https://github.com/flutter/flutter/pull/33842) Don't print warning message when running benchmarks test.

[#33865](https://github.com/flutter/flutter/pull/33865) Correct version name for BottomNavigationBar golden test

[#33868](https://github.com/flutter/flutter/pull/33868) Game controller button support

[#33876](https://github.com/flutter/flutter/pull/33876) Reland "Framework support for font features in text styles"

[#33880](https://github.com/flutter/flutter/pull/33880) Splitting golden file versioning out as an argument of matchesGoldenFile

[#33886](https://github.com/flutter/flutter/pull/33886) Add currentSystemFrameTimeStamp to SchedulerBinding

[#33901](https://github.com/flutter/flutter/pull/33901) Respond to AndroidView focus events.

[#33917](https://github.com/flutter/flutter/pull/33917) 'the the' doc fix

[#33946](https://github.com/flutter/flutter/pull/33946) Reland "Text inline widgets, TextSpan rework"

[#33955](https://github.com/flutter/flutter/pull/33955) Add localFocalPoint to ScaleDetector

[#33996](https://github.com/flutter/flutter/pull/33996) Remove unused/dead code from WidgetInspector

[#33999](https://github.com/flutter/flutter/pull/33999) Updating MediaQuery with viewPadding

[#34012](https://github.com/flutter/flutter/pull/34012) Extract DiagnosticsNode serializer from WidgetInspector

[#34057](https://github.com/flutter/flutter/pull/34057) Add endIndent property to Divider and VerticalDivider

[#34068](https://github.com/flutter/flutter/pull/34068) fix empty selection arrow when double clicked on empty read only text…

[#34073](https://github.com/flutter/flutter/pull/34073) Dartdoc Generation README Improvements

[#34112](https://github.com/flutter/flutter/pull/34112) Separate web and io implementations of network image

[#34137](https://github.com/flutter/flutter/pull/34137) Added tool sample for PageController

[#34175](https://github.com/flutter/flutter/pull/34175) Don't show scrollbar if there isn't enough content

[#34243](https://github.com/flutter/flutter/pull/34243) update the Flutter.Frame event to use new engine APIs

[#34285](https://github.com/flutter/flutter/pull/34285) fix Applying decoration for a table row widget will cause render exce…

[#34298](https://github.com/flutter/flutter/pull/34298) Preserving SafeArea : Part 2

[#34355](https://github.com/flutter/flutter/pull/34355) Text field vertical align

[#34365](https://github.com/flutter/flutter/pull/34365) redux of a change to use new engine APIs for Flutter.Frame events

[#34368](https://github.com/flutter/flutter/pull/34368) Fix semantics_tester

[#34388](https://github.com/flutter/flutter/pull/34388) Change API doc link to api.dart.dev

[#34417](https://github.com/flutter/flutter/pull/34417) Include raw value in Diagnostics json for basic types

[#34424](https://github.com/flutter/flutter/pull/34424) SizedBox documentation minor update

[#34434](https://github.com/flutter/flutter/pull/34434) Semantics fixes

[#34440](https://github.com/flutter/flutter/pull/34440) Add Driver command to get diagnostics tree

[#34474](https://github.com/flutter/flutter/pull/34474) Release diagnostics

[#34501](https://github.com/flutter/flutter/pull/34501) [Material] Fix TextDirection and selected thumb for RangeSliderThumbShape and RangeSliderValueIndicatorShape

[#34508](https://github.com/flutter/flutter/pull/34508) add route information to Flutter.Navigation events

[#34512](https://github.com/flutter/flutter/pull/34512) Make sure fab semantics end up on top

[#34515](https://github.com/flutter/flutter/pull/34515) OutlineInputBorder adjusts for borderRadius that is too large

[#34519](https://github.com/flutter/flutter/pull/34519) fix page scroll position rounding error

[#34587](https://github.com/flutter/flutter/pull/34587) Do not copy paths, rects, and rrects when layer offset is zero

[#34597](https://github.com/flutter/flutter/pull/34597) [Material] Update slider gallery demo, including range slider

[#34664](https://github.com/flutter/flutter/pull/34664) Adjust defaults in docs to match new defaults in code.

[#34679](https://github.com/flutter/flutter/pull/34679) Fix-up code sample for TweenSequence

[#34683](https://github.com/flutter/flutter/pull/34683) add read only semantics flag

[#34684](https://github.com/flutter/flutter/pull/34684) Add more structure to errors.

[#34758](https://github.com/flutter/flutter/pull/34758) Added some Widgets of the Week Videos to documentation

[#34859](https://github.com/flutter/flutter/pull/34859) Fix Vertical Alignment Regression

[#34863](https://github.com/flutter/flutter/pull/34863) Prepare for HttpClientResponse Uint8List SDK change

[#34869](https://github.com/flutter/flutter/pull/34869) [Material] Properly call onChangeStart and onChangeEnd in Range Slider

[#34870](https://github.com/flutter/flutter/pull/34870) Add test case for Flutter Issue #27677 as a benchmark.

[#34919](https://github.com/flutter/flutter/pull/34919) Remove duplicate error parts

[#34932](https://github.com/flutter/flutter/pull/34932) Added onChanged property to TextFormField

[#35046](https://github.com/flutter/flutter/pull/35046) Add generated Icon diagram to api docs

## tool - 140 PRs

[#28808](https://github.com/flutter/flutter/pull/28808)  updated tearDownAll function

[#31028](https://github.com/flutter/flutter/pull/31028) Adds support for generating projects that use AndroidX support libraries

[#31039](https://github.com/flutter/flutter/pull/31039) Fix bundle id on iOS launch using flutter run

[#31282](https://github.com/flutter/flutter/pull/31282) Stop precaching the artifacts for dynamic mode.

[#31329](https://github.com/flutter/flutter/pull/31329) Add Xcode build script for macOS target

[#31342](https://github.com/flutter/flutter/pull/31342) check if project exists before regenerating platform specific tooling

[#31359](https://github.com/flutter/flutter/pull/31359) Remove support for building dynamic patches on Android

[#31399](https://github.com/flutter/flutter/pull/31399) add ignorable track-widget-creation flag to build aot

[#31400](https://github.com/flutter/flutter/pull/31400) add printError messages and tool exit to android device

[#31404](https://github.com/flutter/flutter/pull/31404) throw toolExit instead of rethrowing on filesystem exceptions

[#31406](https://github.com/flutter/flutter/pull/31406) if there is no .ios or ios sub-project, don't attempt building for iOS

[#31419](https://github.com/flutter/flutter/pull/31419) Add a note about events coming from the server

[#31446](https://github.com/flutter/flutter/pull/31446) Allow filtering devices to only those supported by current project

[#31491](https://github.com/flutter/flutter/pull/31491) Allow adb stdout to contain the port number without failing

[#31515](https://github.com/flutter/flutter/pull/31515) Support local engine and asset sync for macOS

[#31526](https://github.com/flutter/flutter/pull/31526) replace no-op log reader with real implementation

[#31591](https://github.com/flutter/flutter/pull/31591) make sure we exit early if the Runner.xcodeproj file is missing

[#31631](https://github.com/flutter/flutter/pull/31631) Teach Linux to use local engine

[#31736](https://github.com/flutter/flutter/pull/31736) update packages and unpin build

[#31757](https://github.com/flutter/flutter/pull/31757) Make FlutterProject factories synchronous

[#31759](https://github.com/flutter/flutter/pull/31759) Remove deprecated commands

[#31765](https://github.com/flutter/flutter/pull/31765) Initial sketch of tools testbed

[#31798](https://github.com/flutter/flutter/pull/31798) Fix tab indentation

[#31804](https://github.com/flutter/flutter/pull/31804) only build asset when there is asset declared in pubspec

[#31807](https://github.com/flutter/flutter/pull/31807) Make const available for classes that override AssetBundle

[#31812](https://github.com/flutter/flutter/pull/31812) Fix #31764: Show appropriate error message when fonts pubspec.yaml isn't iterable

[#31825](https://github.com/flutter/flutter/pull/31825) Fix missing return statements on function literals

[#31835](https://github.com/flutter/flutter/pull/31835) Cherry-pick ADB CrOS fix to beta

[#31850](https://github.com/flutter/flutter/pull/31850) Make Gradle error message more specific

[#31868](https://github.com/flutter/flutter/pull/31868) Handle notification errors

[#31873](https://github.com/flutter/flutter/pull/31873) Add basic desktop linux checks

[#31889](https://github.com/flutter/flutter/pull/31889) Start abstracting platform logic builds behind a shared interface

[#31895](https://github.com/flutter/flutter/pull/31895) Report CompileTime metric in flutter build aot --report-timings.

[#32060](https://github.com/flutter/flutter/pull/32060) make hotfix use a plus instead of minus

[#32071](https://github.com/flutter/flutter/pull/32071) [flutter_tool] In 'attach' use platform dill etc from the Fuchsia SDK

[#32072](https://github.com/flutter/flutter/pull/32072) don't NPE with empty pubspec

[#32126](https://github.com/flutter/flutter/pull/32126) Bump multicast_dns version

[#32335](https://github.com/flutter/flutter/pull/32335) Teach flutter msbuild for Windows

[#32360](https://github.com/flutter/flutter/pull/32360) Allow flutter web to be compiled with flutter

[#32404](https://github.com/flutter/flutter/pull/32404) Comment out .vscode/ in gitignore for templates

[#32444](https://github.com/flutter/flutter/pull/32444) Updated some links

[#32503](https://github.com/flutter/flutter/pull/32503) Add more missing returns

[#32787](https://github.com/flutter/flutter/pull/32787) Support 32 and 64 bit

[#32849](https://github.com/flutter/flutter/pull/32849) [flutter_tool] Adds support for 'run' for Fuchsia devices

[#33041](https://github.com/flutter/flutter/pull/33041) Rename `flutter packages` to `flutter pub`

[#33078](https://github.com/flutter/flutter/pull/33078) don't send crash reports if on a user branch

[#33146](https://github.com/flutter/flutter/pull/33146) [flutter_tool] Don't look for Fuchsia artifacts on Windows

[#33191](https://github.com/flutter/flutter/pull/33191) Remove colon from Gradle task name since it's deprecated

[#33197](https://github.com/flutter/flutter/pull/33197) Wire up hot restart and incremental rebuilds for web

[#33225](https://github.com/flutter/flutter/pull/33225) Reland "Clean up some flutter_tools tests and roll dependencies"

[#33228](https://github.com/flutter/flutter/pull/33228) Make Paths absolute in settings.gradle

[#33263](https://github.com/flutter/flutter/pull/33263) [flutter_tool] Improve Fuchsia 'run' tests

[#33264](https://github.com/flutter/flutter/pull/33264) Add local overrides to testbed and provide more defaults

[#33271](https://github.com/flutter/flutter/pull/33271) No longer necessary with ddc fix

[#33272](https://github.com/flutter/flutter/pull/33272) Add mustRunAfter on mergeAssets task to force task ordering

[#33277](https://github.com/flutter/flutter/pull/33277) Implement macOS support in `flutter doctor`

[#33282](https://github.com/flutter/flutter/pull/33282) [flutter_tool] Use product runner in Fuchsia release build

[#33283](https://github.com/flutter/flutter/pull/33283) Fix relative paths and snapshot logic in tool

[#33284](https://github.com/flutter/flutter/pull/33284) make sure we build test targets too

[#33297](https://github.com/flutter/flutter/pull/33297) Instrument add to app flows

[#33374](https://github.com/flutter/flutter/pull/33374) Devfs cleanup and testing

[#33443](https://github.com/flutter/flutter/pull/33443) Wrap Windows build invocation in a batch script

[#33448](https://github.com/flutter/flutter/pull/33448) Use vswhere to find Visual Studio

[#33450](https://github.com/flutter/flutter/pull/33450) Do not return null from IosProject.isSwift

[#33454](https://github.com/flutter/flutter/pull/33454) ensure unpack declares required artifacts

[#33458](https://github.com/flutter/flutter/pull/33458) Add to app measurement

[#33466](https://github.com/flutter/flutter/pull/33466) [flutter_tool] Misc. fixes for Fuchsia

[#33472](https://github.com/flutter/flutter/pull/33472) add daemon command to enumerate supported platforms

[#33525](https://github.com/flutter/flutter/pull/33525) Add capability to flutter test --platform=chrome

[#33526](https://github.com/flutter/flutter/pull/33526) Update Fuchsia SDK

[#33533](https://github.com/flutter/flutter/pull/33533) Reland - Wire up hot restart and incremental rebuilds for web

[#33540](https://github.com/flutter/flutter/pull/33540) Pass local engine variables to Windows build

[#33608](https://github.com/flutter/flutter/pull/33608) Restructure macOS project files

[#33611](https://github.com/flutter/flutter/pull/33611) Use Dart's new direct ELF generator to package AOT blobs as shared libraries in Android APKs

[#33629](https://github.com/flutter/flutter/pull/33629) Add real-er restart for web using webkit inspection protocol

[#33636](https://github.com/flutter/flutter/pull/33636) Implement plugin tooling support for macOS

[#33676](https://github.com/flutter/flutter/pull/33676) Removing old golden checkout for integration test

[#33684](https://github.com/flutter/flutter/pull/33684) Disable CocoaPods input and output paths in Xcode build phase and adopt new Xcode build system

[#33696](https://github.com/flutter/flutter/pull/33696) Generate ELF shared libraries and allow multi-abi libs in APKs and App bundles

[#33846](https://github.com/flutter/flutter/pull/33846) [flutter_tool] Fix 'q' for Fuchsia profile/debug mode

[#33852](https://github.com/flutter/flutter/pull/33852) Disable CocoaPods input and output paths in Xcode build phase and adopt new Xcode build system

[#33859](https://github.com/flutter/flutter/pull/33859) Reland support flutter test on platform chrome

[#33867](https://github.com/flutter/flutter/pull/33867) Remove environment variable guards for command line desktop and web

[#33872](https://github.com/flutter/flutter/pull/33872) Add 'doctor' support for Windows

[#33874](https://github.com/flutter/flutter/pull/33874) Prevent windows web doctor from launching chrome

[#33892](https://github.com/flutter/flutter/pull/33892) add benchmarks to track web size

[#33923](https://github.com/flutter/flutter/pull/33923) [flutter_tool] Track APK sha calculation time

[#33924](https://github.com/flutter/flutter/pull/33924) Added --dart-flags option to flutter run

[#33951](https://github.com/flutter/flutter/pull/33951) Whitelist adb.exe heap corruption exit code.

[#33956](https://github.com/flutter/flutter/pull/33956) Codegen an entrypoint for flutter web applications

[#33980](https://github.com/flutter/flutter/pull/33980) Increase daemon protocol version for getSupportedPlatforms

[#33990](https://github.com/flutter/flutter/pull/33990) Add device category for daemon

[#34018](https://github.com/flutter/flutter/pull/34018) Add flutter create for the web

[#34050](https://github.com/flutter/flutter/pull/34050) limit open files on macOS when copying assets

[#34066](https://github.com/flutter/flutter/pull/34066) Adds the androidX flag to a modules pubspec.yaml template so it is se…

[#34074](https://github.com/flutter/flutter/pull/34074) add analytics fields for attached device os version & run mode

[#34081](https://github.com/flutter/flutter/pull/34081) Report async callback errors that currently go unreported.

[#34084](https://github.com/flutter/flutter/pull/34084) make running on web spooky

[#34090](https://github.com/flutter/flutter/pull/34090) More verification on flutter build web, add tests and cleanup

[#34112](https://github.com/flutter/flutter/pull/34112) Separate web and io implementations of network image

[#34123](https://github.com/flutter/flutter/pull/34123) Generate ELF shared libraries and allow multi-abi libs in APKs and App bundles

[#34159](https://github.com/flutter/flutter/pull/34159) Use product define for flutter web and remove extra asset server

[#34162](https://github.com/flutter/flutter/pull/34162) Update the Fuchsia SDK

[#34181](https://github.com/flutter/flutter/pull/34181) Reland "Added --dart-flags option to flutter run (#33924)"

[#34189](https://github.com/flutter/flutter/pull/34189) Instrument usage of include_flutter.groovy and xcode_backend.sh

[#34255](https://github.com/flutter/flutter/pull/34255) [flutter_tool] Don't truncate verbose logs from _flutter.listViews

[#34276](https://github.com/flutter/flutter/pull/34276) [flutter_tool,fuchsia] Prefetch tiles when starting an app

[#34282](https://github.com/flutter/flutter/pull/34282) Split gradle_plugin_test.dart

[#34288](https://github.com/flutter/flutter/pull/34288) Report commands that resulted in success or failure

[#34291](https://github.com/flutter/flutter/pull/34291) Check whether FLUTTER_ROOT and FLUTTER_ROOT/bin are writable.

[#34293](https://github.com/flutter/flutter/pull/34293) Change Xcode developmentRegion to 'en' and CFBundleDevelopmentRegion to DEVELOPMENT_LANGUAGE

[#34295](https://github.com/flutter/flutter/pull/34295) Prepare for Uint8List SDK breaking changes

[#34353](https://github.com/flutter/flutter/pull/34353) Refactor Gradle plugin

[#34369](https://github.com/flutter/flutter/pull/34369) Remove unused flag `--target-platform` from `flutter run`

[#34376](https://github.com/flutter/flutter/pull/34376) Add missing pieces for 'driver' support on macOS

[#34447](https://github.com/flutter/flutter/pull/34447) [flutter_tool,fuchsia] Update the install flow for packaging migration.

[#34460](https://github.com/flutter/flutter/pull/34460) Add back ability to override the local engine in Gradle

[#34517](https://github.com/flutter/flutter/pull/34517) pass .packages path to snapshot invocation

[#34526](https://github.com/flutter/flutter/pull/34526) retry on HttpException during cache download

[#34527](https://github.com/flutter/flutter/pull/34527) Don't crash on invalid .packages file

[#34529](https://github.com/flutter/flutter/pull/34529) Remove compilation trace and dynamic support code

[#34573](https://github.com/flutter/flutter/pull/34573) Ensures flutter jar is added to all build types on plugin projects

[#34584](https://github.com/flutter/flutter/pull/34584) fix a typo

[#34589](https://github.com/flutter/flutter/pull/34589) Remove most of the target logic for build web, cleanup rules

[#34606](https://github.com/flutter/flutter/pull/34606) Remove portions of the Gradle script related to dynamic patching

[#34616](https://github.com/flutter/flutter/pull/34616) Kill compiler process when test does not exit cleanly

[#34624](https://github.com/flutter/flutter/pull/34624) Break down flutter doctor validations and results

[#34685](https://github.com/flutter/flutter/pull/34685) Close platform when tests are complete (dispose compiler and delete font files)

[#34686](https://github.com/flutter/flutter/pull/34686) unpin build daemon and roll dependencies

[#34725](https://github.com/flutter/flutter/pull/34725) Fix NPE in flutter tools

[#34736](https://github.com/flutter/flutter/pull/34736) Remove flags related to dynamic patching

[#34755](https://github.com/flutter/flutter/pull/34755) Add linux doctor implementation

[#34785](https://github.com/flutter/flutter/pull/34785) Tweak the display name of emulators

[#34794](https://github.com/flutter/flutter/pull/34794) Add `emulatorID` field to devices in daemon

[#34802](https://github.com/flutter/flutter/pull/34802) Prefer ephemeral devices from command line run

[#34856](https://github.com/flutter/flutter/pull/34856) set device name to Chrome

[#34885](https://github.com/flutter/flutter/pull/34885) Reland: rename web device

[#35074](https://github.com/flutter/flutter/pull/35074) Attempt to enable tool coverage redux

[#35084](https://github.com/flutter/flutter/pull/35084) Move findTargetDevices to DeviceManager

[#35092](https://github.com/flutter/flutter/pull/35092) Add FlutterProjectFactory so that it can be overridden internally.

## f: material design - 76 PRs

[#21896](https://github.com/flutter/flutter/pull/21896) Bottom sheet scrolling

[#29188](https://github.com/flutter/flutter/pull/29188) Fix 25807: implement move in sliver multibox widget

[#29809](https://github.com/flutter/flutter/pull/29809) Fix text selection toolbar appearing under obstructions

[#30388](https://github.com/flutter/flutter/pull/30388) Add hintStyle in SearchDelegate

[#30572](https://github.com/flutter/flutter/pull/30572) [Material] Adaptive Slider constructor

[#30612](https://github.com/flutter/flutter/pull/30612) Added required parameters to FlexibleSpaceBarSettings

[#30796](https://github.com/flutter/flutter/pull/30796) Unbounded TextField width error

[#30884](https://github.com/flutter/flutter/pull/30884) [Material] Update TabController to support dynamic Tabs

[#30942](https://github.com/flutter/flutter/pull/30942) rectMoreOrLess equals, prep for 64bit rects

[#31018](https://github.com/flutter/flutter/pull/31018) [Material] selected/unselected label styles + icon themes on BottomNavigationBar

[#31025](https://github.com/flutter/flutter/pull/31025) added `scrimColor` property in Scaffold widget

[#31228](https://github.com/flutter/flutter/pull/31228) Fix ExpansionPanelList Duplicate Global Keys Exception

[#31275](https://github.com/flutter/flutter/pull/31275) Update SnackBar to allow for support of the new style from Material spec

[#31294](https://github.com/flutter/flutter/pull/31294) Improve Radio Documentation with Example

[#31295](https://github.com/flutter/flutter/pull/31295) Improve ThemeData.accentColor connection to secondary color

[#31316](https://github.com/flutter/flutter/pull/31316) Add InkWell docs on transitions and ink splash clipping

[#31317](https://github.com/flutter/flutter/pull/31317) Add docs to AppBar

[#31318](https://github.com/flutter/flutter/pull/31318) Add BottomSheetTheme to enable theming color, elevation, shape of BottomSheet

[#31438](https://github.com/flutter/flutter/pull/31438) Implements focus handling and hover for Material buttons.

[#31502](https://github.com/flutter/flutter/pull/31502) Improve Tabs documentation

[#31514](https://github.com/flutter/flutter/pull/31514) Date picker layout exceptions

[#31538](https://github.com/flutter/flutter/pull/31538) Fix typo in docs

[#31566](https://github.com/flutter/flutter/pull/31566) TimePicker moves to minute mode after hour selection

[#31574](https://github.com/flutter/flutter/pull/31574) Improve RadioListTile Callback Behavior Consistency

[#31581](https://github.com/flutter/flutter/pull/31581) Fix Exception on Nested TabBarView disposal

[#31644](https://github.com/flutter/flutter/pull/31644) Cupertino localization step 12: push translation for all supported languages

[#31662](https://github.com/flutter/flutter/pull/31662) added shape property to SliverAppBar

[#31681](https://github.com/flutter/flutter/pull/31681) [Material] Create a themable Range Slider (continuous and discrete)

[#31693](https://github.com/flutter/flutter/pull/31693) Adds a note to Radio's/RadioListTile's onChange

[#31699](https://github.com/flutter/flutter/pull/31699) Re-land: Add support for Tooltip hover

[#31763](https://github.com/flutter/flutter/pull/31763) Fix ScrollbarPainter thumbExtent calculation and add padding

[#31852](https://github.com/flutter/flutter/pull/31852) Text selection handles are sometimes not interactive

[#31861](https://github.com/flutter/flutter/pull/31861) Add Horizontal Padding to Constrained Chip Label Calculations

[#31902](https://github.com/flutter/flutter/pull/31902) Updated primaryColor docs to refer to colorScheme properties

[#31938](https://github.com/flutter/flutter/pull/31938) Update scrimDrawerColor with proper const format

[#32053](https://github.com/flutter/flutter/pull/32053) Increase TimePicker touch targets

[#32147](https://github.com/flutter/flutter/pull/32147) Added state management docs/sample to SwitchListTile

[#32177](https://github.com/flutter/flutter/pull/32177) Tab Animation Sample Video

[#32434](https://github.com/flutter/flutter/pull/32434) Support for replacing the TabController, after disposing the old one

[#32527](https://github.com/flutter/flutter/pull/32527) Added 'enabled' property to the PopupMenuButton

[#32528](https://github.com/flutter/flutter/pull/32528) Tapping a modal bottom sheet should not dismiss it by default

[#32530](https://github.com/flutter/flutter/pull/32530) Add Actions to AppBar Sample Doc

[#32620](https://github.com/flutter/flutter/pull/32620) Added ScrollController to TextField

[#32654](https://github.com/flutter/flutter/pull/32654) Tabs code/doc cleanup

[#32703](https://github.com/flutter/flutter/pull/32703) Add Doc Samples For CheckboxListTile, RadioListTile and SwitchListTile

[#32726](https://github.com/flutter/flutter/pull/32726) Material should not prevent ScrollNotifications from bubbling upwards

[#32776](https://github.com/flutter/flutter/pull/32776) Text field focus and hover support.

[#32838](https://github.com/flutter/flutter/pull/32838) Handles hidden by keyboard

[#32843](https://github.com/flutter/flutter/pull/32843) Added a missing dispose of an AnimationController that was leaking a ticker.

[#32904](https://github.com/flutter/flutter/pull/32904) Use reverseDuration on Tooltip and InkWell

[#32911](https://github.com/flutter/flutter/pull/32911) Material Long Press Text Handle Flash

[#32914](https://github.com/flutter/flutter/pull/32914) Make hover and focus not respond when buttons and fields are disabled.

[#32950](https://github.com/flutter/flutter/pull/32950) Material allows "select all" when not collapsed

[#33073](https://github.com/flutter/flutter/pull/33073) SliverAppBar shape property

[#33080](https://github.com/flutter/flutter/pull/33080) Fixed several issues with confirmDismiss handling on the LeaveBehindItem demo.

[#33090](https://github.com/flutter/flutter/pull/33090) [Material] Add support for hovered, pressed, and focused text color on Buttons.

[#33148](https://github.com/flutter/flutter/pull/33148) ExpandIcon Custom Colors

[#33361](https://github.com/flutter/flutter/pull/33361) (trivial) Rename test file

[#33474](https://github.com/flutter/flutter/pull/33474) Fixed for DropdownButton crashing when a style was used that didn't include a fontSize

[#33535](https://github.com/flutter/flutter/pull/33535) Custom height parameters for DataTable header and data rows

[#33628](https://github.com/flutter/flutter/pull/33628) DataTable Custom Horizontal Padding

[#33634](https://github.com/flutter/flutter/pull/33634) Let there be scroll bars

[#33653](https://github.com/flutter/flutter/pull/33653) Include advice about dispose in TextEditingController api

[#33665](https://github.com/flutter/flutter/pull/33665) [Trivial] Move dropdownValue into State in DropdownButton sample docs

[#33802](https://github.com/flutter/flutter/pull/33802) Double double tap toggles instead of error

[#33808](https://github.com/flutter/flutter/pull/33808) fix ExpansionPanelList merge the header semantics when it is not nece…

[#33865](https://github.com/flutter/flutter/pull/33865) Correct version name for BottomNavigationBar golden test

[#34055](https://github.com/flutter/flutter/pull/34055) Toggle toolbar exception fix

[#34057](https://github.com/flutter/flutter/pull/34057) Add endIndent property to Divider and VerticalDivider

[#34355](https://github.com/flutter/flutter/pull/34355) Text field vertical align

[#34501](https://github.com/flutter/flutter/pull/34501) [Material] Fix TextDirection and selected thumb for RangeSliderThumbShape and RangeSliderValueIndicatorShape

[#34515](https://github.com/flutter/flutter/pull/34515) OutlineInputBorder adjusts for borderRadius that is too large

[#34597](https://github.com/flutter/flutter/pull/34597) [Material] Update slider gallery demo, including range slider

[#34859](https://github.com/flutter/flutter/pull/34859) Fix Vertical Alignment Regression

[#34869](https://github.com/flutter/flutter/pull/34869) [Material] Properly call onChangeStart and onChangeEnd in Range Slider

[#34932](https://github.com/flutter/flutter/pull/34932) Added onChanged property to TextFormField

## a: tests - 38 PRs

[#30942](https://github.com/flutter/flutter/pull/30942) rectMoreOrLess equals, prep for 64bit rects

[#31333](https://github.com/flutter/flutter/pull/31333) Clean up flutter_test/test/controller_test.dart

[#31452](https://github.com/flutter/flutter/pull/31452) Remove engine tests

[#32070](https://github.com/flutter/flutter/pull/32070) rename foreground and background to light and dark

[#32437](https://github.com/flutter/flutter/pull/32437) Add assert that the root widget has been attached.

[#33349](https://github.com/flutter/flutter/pull/33349) Compatibility pass on flutter/foundation tests for JavaScript compilation. (1)

[#33350](https://github.com/flutter/flutter/pull/33350) Compatibility pass on flutter/scheduler tests for JavaScript compilation. (2)

[#33352](https://github.com/flutter/flutter/pull/33352) Compatibility pass on flutter/painting tests for JavaScript compilation. (3)

[#33354](https://github.com/flutter/flutter/pull/33354) Compatibility pass on flutter/services tests for JavaScript compilation. (4)

[#33355](https://github.com/flutter/flutter/pull/33355) Compatibility pass on flutter/rendering tests for JavaScript compilation. (5)

[#33359](https://github.com/flutter/flutter/pull/33359) Compatibility pass on flutter/physics tests for JavaScript compilation. (6)

[#33360](https://github.com/flutter/flutter/pull/33360) Compatibility pass on flutter/semantics tests for JavaScript compilation. (7)

[#33361](https://github.com/flutter/flutter/pull/33361) (trivial) Rename test file

[#33377](https://github.com/flutter/flutter/pull/33377) Compatibility pass on flutter/widgets tests for JavaScript compilation. (8)

[#33378](https://github.com/flutter/flutter/pull/33378) Compatibility pass on flutter/material tests for JavaScript compilation. (9)

[#33406](https://github.com/flutter/flutter/pull/33406) Add web safe indirection to Platform.isPlatform getters

[#33459](https://github.com/flutter/flutter/pull/33459) make sure version check includes hotfixes

[#33676](https://github.com/flutter/flutter/pull/33676) Removing old golden checkout for integration test

[#33786](https://github.com/flutter/flutter/pull/33786) Add a real-er web restart, doctor, workflow

[#33805](https://github.com/flutter/flutter/pull/33805) Fixing duplicate golden test names

[#33842](https://github.com/flutter/flutter/pull/33842) Don't print warning message when running benchmarks test.

[#33865](https://github.com/flutter/flutter/pull/33865) Correct version name for BottomNavigationBar golden test

[#33880](https://github.com/flutter/flutter/pull/33880) Splitting golden file versioning out as an argument of matchesGoldenFile

[#33932](https://github.com/flutter/flutter/pull/33932) More removing of timeouts.

[#34012](https://github.com/flutter/flutter/pull/34012) Extract DiagnosticsNode serializer from WidgetInspector

[#34017](https://github.com/flutter/flutter/pull/34017) Skip web test on crazy import

[#34032](https://github.com/flutter/flutter/pull/34032) Enable web foundation tests

[#34054](https://github.com/flutter/flutter/pull/34054) Make it easier to pass local engine flags when running devicelab tests

[#34199](https://github.com/flutter/flutter/pull/34199) make sure this test doesn't run for real

[#34368](https://github.com/flutter/flutter/pull/34368) Fix semantics_tester

[#34417](https://github.com/flutter/flutter/pull/34417) Include raw value in Diagnostics json for basic types

[#34440](https://github.com/flutter/flutter/pull/34440) Add Driver command to get diagnostics tree

[#34616](https://github.com/flutter/flutter/pull/34616) Kill compiler process when test does not exit cleanly

[#34685](https://github.com/flutter/flutter/pull/34685) Close platform when tests are complete (dispose compiler and delete font files)

[#34863](https://github.com/flutter/flutter/pull/34863) Prepare for HttpClientResponse Uint8List SDK change

[#34877](https://github.com/flutter/flutter/pull/34877) More shards

[#35074](https://github.com/flutter/flutter/pull/35074) Attempt to enable tool coverage redux

[#35130](https://github.com/flutter/flutter/pull/35130) pass new users for release_smoke_tests

## waiting for tree to go green - 35 PRs

[#28808](https://github.com/flutter/flutter/pull/28808)  updated tearDownAll function

[#28834](https://github.com/flutter/flutter/pull/28834) Sliver animated list

[#29677](https://github.com/flutter/flutter/pull/29677) Fix calculation of hero rectTween when Navigator isn't fullscreen

[#30979](https://github.com/flutter/flutter/pull/30979) fix issue 30526: rounding error

[#31028](https://github.com/flutter/flutter/pull/31028) Adds support for generating projects that use AndroidX support libraries

[#31452](https://github.com/flutter/flutter/pull/31452) Remove engine tests

[#31600](https://github.com/flutter/flutter/pull/31600) Re-enable const

[#31798](https://github.com/flutter/flutter/pull/31798) Fix tab indentation

[#31832](https://github.com/flutter/flutter/pull/31832) Allow DSS to be dragged when its children do not fill extent

[#31868](https://github.com/flutter/flutter/pull/31868) Handle notification errors

[#32025](https://github.com/flutter/flutter/pull/32025) Make Hover Listener respect transforms

[#32266](https://github.com/flutter/flutter/pull/32266) Add reference to Runner-Bridging-Header.h to iOS profile config

[#32328](https://github.com/flutter/flutter/pull/32328) Add breadcrumbs to TextOverflow

[#32380](https://github.com/flutter/flutter/pull/32380) const everything in Driver

[#32410](https://github.com/flutter/flutter/pull/32410) Add ancestor and descendant finders to Driver

[#33152](https://github.com/flutter/flutter/pull/33152) ModalRoute resumes previous focus on didPopNext

[#33272](https://github.com/flutter/flutter/pull/33272) Add mustRunAfter on mergeAssets task to force task ordering

[#33442](https://github.com/flutter/flutter/pull/33442) fix GridView documentation

[#33461](https://github.com/flutter/flutter/pull/33461) Various code cleanup improvements

[#33653](https://github.com/flutter/flutter/pull/33653) Include advice about dispose in TextEditingController api

[#33865](https://github.com/flutter/flutter/pull/33865) Correct version name for BottomNavigationBar golden test

[#33880](https://github.com/flutter/flutter/pull/33880) Splitting golden file versioning out as an argument of matchesGoldenFile

[#33932](https://github.com/flutter/flutter/pull/33932) More removing of timeouts.

[#33955](https://github.com/flutter/flutter/pull/33955) Add localFocalPoint to ScaleDetector

[#34298](https://github.com/flutter/flutter/pull/34298) Preserving SafeArea : Part 2

[#34440](https://github.com/flutter/flutter/pull/34440) Add Driver command to get diagnostics tree

[#34474](https://github.com/flutter/flutter/pull/34474) Release diagnostics

[#34573](https://github.com/flutter/flutter/pull/34573) Ensures flutter jar is added to all build types on plugin projects

[#34592](https://github.com/flutter/flutter/pull/34592) Config lib dependencies for flavors

[#34606](https://github.com/flutter/flutter/pull/34606) Remove portions of the Gradle script related to dynamic patching

[#34655](https://github.com/flutter/flutter/pull/34655) Revert "Config lib dependencies for flavors"

[#34685](https://github.com/flutter/flutter/pull/34685) Close platform when tests are complete (dispose compiler and delete font files)

[#34738](https://github.com/flutter/flutter/pull/34738) Update Xcode projects to recommended Xcode 10 project settings

[#34877](https://github.com/flutter/flutter/pull/34877) More shards

[#34919](https://github.com/flutter/flutter/pull/34919) Remove duplicate error parts

## d: api docs - 34 PRs

[#31294](https://github.com/flutter/flutter/pull/31294) Improve Radio Documentation with Example

[#31295](https://github.com/flutter/flutter/pull/31295) Improve ThemeData.accentColor connection to secondary color

[#31316](https://github.com/flutter/flutter/pull/31316) Add InkWell docs on transitions and ink splash clipping

[#31317](https://github.com/flutter/flutter/pull/31317) Add docs to AppBar

[#31493](https://github.com/flutter/flutter/pull/31493) Keycode generation doc fix

[#31502](https://github.com/flutter/flutter/pull/31502) Improve Tabs documentation

[#31634](https://github.com/flutter/flutter/pull/31634) Improve canvas example in sample dart ui app

[#31693](https://github.com/flutter/flutter/pull/31693) Adds a note to Radio's/RadioListTile's onChange

[#31763](https://github.com/flutter/flutter/pull/31763) Fix ScrollbarPainter thumbExtent calculation and add padding

[#31902](https://github.com/flutter/flutter/pull/31902) Updated primaryColor docs to refer to colorScheme properties

[#31903](https://github.com/flutter/flutter/pull/31903) Extract TODO comment from Image.asset dardoc

[#31929](https://github.com/flutter/flutter/pull/31929) Sample Code & Animation for Flow Widget

[#32147](https://github.com/flutter/flutter/pull/32147) Added state management docs/sample to SwitchListTile

[#32177](https://github.com/flutter/flutter/pull/32177) Tab Animation Sample Video

[#32530](https://github.com/flutter/flutter/pull/32530) Add Actions to AppBar Sample Doc

[#32638](https://github.com/flutter/flutter/pull/32638) Fix apidocs in _WidgetsAppState.basicLocaleListResolution

[#32641](https://github.com/flutter/flutter/pull/32641) Updating dart.dev related links

[#32703](https://github.com/flutter/flutter/pull/32703) Add Doc Samples For CheckboxListTile, RadioListTile and SwitchListTile

[#32909](https://github.com/flutter/flutter/pull/32909) Documentation fix for debugProfileBuildsEnabled

[#33226](https://github.com/flutter/flutter/pull/33226) Explain hairline rendering in BorderSide.width docs

[#33442](https://github.com/flutter/flutter/pull/33442) fix GridView documentation

[#33620](https://github.com/flutter/flutter/pull/33620) Document that offsets are returned in logical pixels

[#33653](https://github.com/flutter/flutter/pull/33653) Include advice about dispose in TextEditingController api

[#33665](https://github.com/flutter/flutter/pull/33665) [Trivial] Move dropdownValue into State in DropdownButton sample docs

[#33917](https://github.com/flutter/flutter/pull/33917) 'the the' doc fix

[#34073](https://github.com/flutter/flutter/pull/34073) Dartdoc Generation README Improvements

[#34137](https://github.com/flutter/flutter/pull/34137) Added tool sample for PageController

[#34163](https://github.com/flutter/flutter/pull/34163) update CupertinoDialogAction docs

[#34356](https://github.com/flutter/flutter/pull/34356) Add widget of the week videos

[#34388](https://github.com/flutter/flutter/pull/34388) Change API doc link to api.dart.dev

[#34424](https://github.com/flutter/flutter/pull/34424) SizedBox documentation minor update

[#34679](https://github.com/flutter/flutter/pull/34679) Fix-up code sample for TweenSequence

[#34758](https://github.com/flutter/flutter/pull/34758) Added some Widgets of the Week Videos to documentation

[#35046](https://github.com/flutter/flutter/pull/35046) Add generated Icon diagram to api docs

## team - 28 PRs

[#28808](https://github.com/flutter/flutter/pull/28808)  updated tearDownAll function

[#30327](https://github.com/flutter/flutter/pull/30327) Add "feature request" issue template

[#31452](https://github.com/flutter/flutter/pull/31452) Remove engine tests

[#31825](https://github.com/flutter/flutter/pull/31825) Fix missing return statements on function literals

[#31885](https://github.com/flutter/flutter/pull/31885) Fix commit message UTF issue for deploy_gallery shard too

[#31944](https://github.com/flutter/flutter/pull/31944) Performance issue template

[#32345](https://github.com/flutter/flutter/pull/32345) Add master channel to performance issue template

[#32503](https://github.com/flutter/flutter/pull/32503) Add more missing returns

[#33267](https://github.com/flutter/flutter/pull/33267) Add unpublish_package script.

[#33459](https://github.com/flutter/flutter/pull/33459) make sure version check includes hotfixes

[#33539](https://github.com/flutter/flutter/pull/33539) Fix/update several HTML links

[#33554](https://github.com/flutter/flutter/pull/33554) Remove obsolete TODOs

[#33677](https://github.com/flutter/flutter/pull/33677) Roll pub dependencies

[#33786](https://github.com/flutter/flutter/pull/33786) Add a real-er web restart, doctor, workflow

[#33787](https://github.com/flutter/flutter/pull/33787) Add chrome stable to dockerfile and web shard

[#33861](https://github.com/flutter/flutter/pull/33861) Unmark flutter_gallery__back_button_memory as flaky

[#33932](https://github.com/flutter/flutter/pull/33932) More removing of timeouts.

[#34032](https://github.com/flutter/flutter/pull/34032) Enable web foundation tests

[#34054](https://github.com/flutter/flutter/pull/34054) Make it easier to pass local engine flags when running devicelab tests

[#34112](https://github.com/flutter/flutter/pull/34112) Separate web and io implementations of network image

[#34199](https://github.com/flutter/flutter/pull/34199) make sure this test doesn't run for real

[#34738](https://github.com/flutter/flutter/pull/34738) Update Xcode projects to recommended Xcode 10 project settings

[#34739](https://github.com/flutter/flutter/pull/34739) Disable widgets and material web tests

[#34812](https://github.com/flutter/flutter/pull/34812) Shard framework tests

[#34818](https://github.com/flutter/flutter/pull/34818) Make docs do less work/be less flaky

[#34857](https://github.com/flutter/flutter/pull/34857) More shards

[#34877](https://github.com/flutter/flutter/pull/34877) More shards

[#35130](https://github.com/flutter/flutter/pull/35130) pass new users for release_smoke_tests

## f: cupertino - 27 PRs

[#29809](https://github.com/flutter/flutter/pull/29809) Fix text selection toolbar appearing under obstructions

[#29824](https://github.com/flutter/flutter/pull/29824) Cupertino localization step 8: create a gen_cupertino_localizations and generate one for cupertino english and french

[#29954](https://github.com/flutter/flutter/pull/29954) Cupertino localization step 9: add tests

[#30129](https://github.com/flutter/flutter/pull/30129) Fix refresh control in the gallery demo, update comments

[#30224](https://github.com/flutter/flutter/pull/30224) Cupertino localization step 10: update the flutter_localizations README

[#31227](https://github.com/flutter/flutter/pull/31227) Adding CupertinoTabController

[#31308](https://github.com/flutter/flutter/pull/31308) Added font bold when isDefaultAction is true in CupertinoDialogAction

[#31326](https://github.com/flutter/flutter/pull/31326) Add more shuffle cupertino icons

[#31464](https://github.com/flutter/flutter/pull/31464) CupertinoPicker fidelity revision

[#31623](https://github.com/flutter/flutter/pull/31623) fix edge swiping and dropping back at starting point

[#31644](https://github.com/flutter/flutter/pull/31644) Cupertino localization step 12: push translation for all supported languages

[#31763](https://github.com/flutter/flutter/pull/31763) Fix ScrollbarPainter thumbExtent calculation and add padding

[#31852](https://github.com/flutter/flutter/pull/31852) Text selection handles are sometimes not interactive

[#32013](https://github.com/flutter/flutter/pull/32013) Cupertino Turkish Translation

[#32086](https://github.com/flutter/flutter/pull/32086) Fix CupertinoSliverRefreshControl onRefresh callback

[#32469](https://github.com/flutter/flutter/pull/32469)  Let CupertinoNavigationBarBackButton take a custom onPressed

[#32513](https://github.com/flutter/flutter/pull/32513) Cupertino localization step 12 try 2: push translation for all supported languages

[#32620](https://github.com/flutter/flutter/pull/32620) Added ScrollController to TextField

[#32823](https://github.com/flutter/flutter/pull/32823) Add enableInteractiveSelection to CupertinoTextField

[#32974](https://github.com/flutter/flutter/pull/32974) Fix disabled CupertinoTextField style

[#33624](https://github.com/flutter/flutter/pull/33624) CupertinoTabScaffold crash fix

[#33634](https://github.com/flutter/flutter/pull/33634) Let there be scroll bars

[#33653](https://github.com/flutter/flutter/pull/33653) Include advice about dispose in TextEditingController api

[#33739](https://github.com/flutter/flutter/pull/33739) fixed cupertinoTextField placeholder textAlign

[#34095](https://github.com/flutter/flutter/pull/34095) Cupertino text edit tooltip, reworked

[#34163](https://github.com/flutter/flutter/pull/34163) update CupertinoDialogAction docs

[#34964](https://github.com/flutter/flutter/pull/34964) CupertinoTextField.onTap

## severe: API break - 17 PRs

[#29188](https://github.com/flutter/flutter/pull/29188) Fix 25807: implement move in sliver multibox widget

[#29683](https://github.com/flutter/flutter/pull/29683) Show/hide toolbar and handles based on device kind

[#30040](https://github.com/flutter/flutter/pull/30040) Implement focus traversal for desktop platforms, shoehorn edition.

[#30579](https://github.com/flutter/flutter/pull/30579) PointerDownEvent and PointerMoveEvent default `buttons` to 1

[#30874](https://github.com/flutter/flutter/pull/30874) Redo "Remove pressure customization from some pointer events"

[#31227](https://github.com/flutter/flutter/pull/31227) Adding CupertinoTabController

[#31574](https://github.com/flutter/flutter/pull/31574) Improve RadioListTile Callback Behavior Consistency

[#32059](https://github.com/flutter/flutter/pull/32059) fix issue 14014 read only text field

[#32842](https://github.com/flutter/flutter/pull/32842) Allow "from" hero state to survive hero animation in a push transition

[#33148](https://github.com/flutter/flutter/pull/33148) ExpandIcon Custom Colors

[#33164](https://github.com/flutter/flutter/pull/33164) remove Layer.replaceWith due to no usage and no tests

[#33370](https://github.com/flutter/flutter/pull/33370) Update FadeInImage to use new Image APIs

[#33794](https://github.com/flutter/flutter/pull/33794) Text inline widgets, TextSpan rework

[#33946](https://github.com/flutter/flutter/pull/33946) Reland "Text inline widgets, TextSpan rework"

[#34051](https://github.com/flutter/flutter/pull/34051) Reland "Text inline widgets, TextSpan rework (#30069)" with improved backwards compatibility

[#34095](https://github.com/flutter/flutter/pull/34095) Cupertino text edit tooltip, reworked

[#34501](https://github.com/flutter/flutter/pull/34501) [Material] Fix TextDirection and selected thumb for RangeSliderThumbShape and RangeSliderValueIndicatorShape

## a: text input - 15 PRs

[#29683](https://github.com/flutter/flutter/pull/29683) Show/hide toolbar and handles based on device kind

[#31687](https://github.com/flutter/flutter/pull/31687) Center iOS caret, remove constant offsets that do not scale

[#32059](https://github.com/flutter/flutter/pull/32059) fix issue 14014 read only text field

[#32823](https://github.com/flutter/flutter/pull/32823) Add enableInteractiveSelection to CupertinoTextField

[#32838](https://github.com/flutter/flutter/pull/32838) Handles hidden by keyboard

[#32974](https://github.com/flutter/flutter/pull/32974) Fix disabled CupertinoTextField style

[#33794](https://github.com/flutter/flutter/pull/33794) Text inline widgets, TextSpan rework

[#33802](https://github.com/flutter/flutter/pull/33802) Double double tap toggles instead of error

[#33946](https://github.com/flutter/flutter/pull/33946) Reland "Text inline widgets, TextSpan rework"

[#34051](https://github.com/flutter/flutter/pull/34051) Reland "Text inline widgets, TextSpan rework (#30069)" with improved backwards compatibility

[#34055](https://github.com/flutter/flutter/pull/34055) Toggle toolbar exception fix

[#34068](https://github.com/flutter/flutter/pull/34068) fix empty selection arrow when double clicked on empty read only text…

[#34095](https://github.com/flutter/flutter/pull/34095) Cupertino text edit tooltip, reworked

[#34515](https://github.com/flutter/flutter/pull/34515) OutlineInputBorder adjusts for borderRadius that is too large

[#34859](https://github.com/flutter/flutter/pull/34859) Fix Vertical Alignment Regression

## ☸ platform-web - 15 PRs

[#33197](https://github.com/flutter/flutter/pull/33197) Wire up hot restart and incremental rebuilds for web

[#33349](https://github.com/flutter/flutter/pull/33349) Compatibility pass on flutter/foundation tests for JavaScript compilation. (1)

[#33350](https://github.com/flutter/flutter/pull/33350) Compatibility pass on flutter/scheduler tests for JavaScript compilation. (2)

[#33352](https://github.com/flutter/flutter/pull/33352) Compatibility pass on flutter/painting tests for JavaScript compilation. (3)

[#33354](https://github.com/flutter/flutter/pull/33354) Compatibility pass on flutter/services tests for JavaScript compilation. (4)

[#33355](https://github.com/flutter/flutter/pull/33355) Compatibility pass on flutter/rendering tests for JavaScript compilation. (5)

[#33359](https://github.com/flutter/flutter/pull/33359) Compatibility pass on flutter/physics tests for JavaScript compilation. (6)

[#33360](https://github.com/flutter/flutter/pull/33360) Compatibility pass on flutter/semantics tests for JavaScript compilation. (7)

[#33377](https://github.com/flutter/flutter/pull/33377) Compatibility pass on flutter/widgets tests for JavaScript compilation. (8)

[#33378](https://github.com/flutter/flutter/pull/33378) Compatibility pass on flutter/material tests for JavaScript compilation. (9)

[#33629](https://github.com/flutter/flutter/pull/33629) Add real-er restart for web using webkit inspection protocol

[#33956](https://github.com/flutter/flutter/pull/33956) Codegen an entrypoint for flutter web applications

[#34018](https://github.com/flutter/flutter/pull/34018) Add flutter create for the web

[#34112](https://github.com/flutter/flutter/pull/34112) Separate web and io implementations of network image

[#34159](https://github.com/flutter/flutter/pull/34159) Use product define for flutter web and remove extra asset server

## a: desktop - 14 PRs

[#29683](https://github.com/flutter/flutter/pull/29683) Show/hide toolbar and handles based on device kind

[#30040](https://github.com/flutter/flutter/pull/30040) Implement focus traversal for desktop platforms, shoehorn edition.

[#30076](https://github.com/flutter/flutter/pull/30076) Implements FocusTraversalPolicy and DefaultFocusTraversal features.

[#30339](https://github.com/flutter/flutter/pull/30339) Add buttons to gestures

[#30579](https://github.com/flutter/flutter/pull/30579) PointerDownEvent and PointerMoveEvent default `buttons` to 1

[#31493](https://github.com/flutter/flutter/pull/31493) Keycode generation doc fix

[#31819](https://github.com/flutter/flutter/pull/31819) Redo: Add buttons to gestures

[#31935](https://github.com/flutter/flutter/pull/31935) Redo#2: Add buttons to gestures

[#33298](https://github.com/flutter/flutter/pull/33298) Add actions and keyboard shortcut map support

[#33443](https://github.com/flutter/flutter/pull/33443) Wrap Windows build invocation in a batch script

[#33454](https://github.com/flutter/flutter/pull/33454) ensure unpack declares required artifacts

[#33636](https://github.com/flutter/flutter/pull/33636) Implement plugin tooling support for macOS

[#33695](https://github.com/flutter/flutter/pull/33695) Add pseudo-key synonyms for keys like shift, meta, alt, and control.

[#33868](https://github.com/flutter/flutter/pull/33868) Game controller button support

## t: gradle - 11 PRs

[#32787](https://github.com/flutter/flutter/pull/32787) Support 32 and 64 bit

[#33191](https://github.com/flutter/flutter/pull/33191) Remove colon from Gradle task name since it's deprecated

[#33228](https://github.com/flutter/flutter/pull/33228) Make Paths absolute in settings.gradle

[#33272](https://github.com/flutter/flutter/pull/33272) Add mustRunAfter on mergeAssets task to force task ordering

[#33696](https://github.com/flutter/flutter/pull/33696) Generate ELF shared libraries and allow multi-abi libs in APKs and App bundles

[#34123](https://github.com/flutter/flutter/pull/34123) Generate ELF shared libraries and allow multi-abi libs in APKs and App bundles

[#34353](https://github.com/flutter/flutter/pull/34353) Refactor Gradle plugin

[#34460](https://github.com/flutter/flutter/pull/34460) Add back ability to override the local engine in Gradle

[#34573](https://github.com/flutter/flutter/pull/34573) Ensures flutter jar is added to all build types on plugin projects

[#34592](https://github.com/flutter/flutter/pull/34592) Config lib dependencies for flavors

[#34668](https://github.com/flutter/flutter/pull/34668) Re-land config lib dependencies for flavors

## f: scrolling - 9 PRs

[#31485](https://github.com/flutter/flutter/pull/31485) Prevent exception being thrown on hasScrolledBody

[#31763](https://github.com/flutter/flutter/pull/31763) Fix ScrollbarPainter thumbExtent calculation and add padding

[#31832](https://github.com/flutter/flutter/pull/31832) Allow DSS to be dragged when its children do not fill extent

[#32620](https://github.com/flutter/flutter/pull/32620) Added ScrollController to TextField

[#32726](https://github.com/flutter/flutter/pull/32726) Material should not prevent ScrollNotifications from bubbling upwards

[#32842](https://github.com/flutter/flutter/pull/32842) Allow "from" hero state to survive hero animation in a push transition

[#33627](https://github.com/flutter/flutter/pull/33627) SliverFillRemaining flag for different use cases

[#33634](https://github.com/flutter/flutter/pull/33634) Let there be scroll bars

[#34175](https://github.com/flutter/flutter/pull/34175) Don't show scrollbar if there isn't enough content

## engine - 6 PRs

[#31520](https://github.com/flutter/flutter/pull/31520) Don't add empty OpacityLayer to the engine

[#33272](https://github.com/flutter/flutter/pull/33272) Add mustRunAfter on mergeAssets task to force task ordering

[#33662](https://github.com/flutter/flutter/pull/33662) Prep for engine roll

[#34276](https://github.com/flutter/flutter/pull/34276) [flutter_tool,fuchsia] Prefetch tiles when starting an app

[#34460](https://github.com/flutter/flutter/pull/34460) Add back ability to override the local engine in Gradle

[#34870](https://github.com/flutter/flutter/pull/34870) Add test case for Flutter Issue #27677 as a benchmark.

## severe: new feature - 6 PRs

[#33148](https://github.com/flutter/flutter/pull/33148) ExpandIcon Custom Colors

[#33535](https://github.com/flutter/flutter/pull/33535) Custom height parameters for DataTable header and data rows

[#33628](https://github.com/flutter/flutter/pull/33628) DataTable Custom Horizontal Padding

[#33794](https://github.com/flutter/flutter/pull/33794) Text inline widgets, TextSpan rework

[#33946](https://github.com/flutter/flutter/pull/33946) Reland "Text inline widgets, TextSpan rework"

[#34051](https://github.com/flutter/flutter/pull/34051) Reland "Text inline widgets, TextSpan rework (#30069)" with improved backwards compatibility

## t: flutter driver - 5 PRs

[#28808](https://github.com/flutter/flutter/pull/28808)  updated tearDownAll function

[#31824](https://github.com/flutter/flutter/pull/31824) fix FlutterDriver timeout

[#32380](https://github.com/flutter/flutter/pull/32380) const everything in Driver

[#33431](https://github.com/flutter/flutter/pull/33431) Expose service client and app isolate in driver

[#33786](https://github.com/flutter/flutter/pull/33786) Add a real-er web restart, doctor, workflow

## a: images - 5 PRs

[#32853](https://github.com/flutter/flutter/pull/32853) Add onBytesReceived callback to consolidateHttpClientResponseBytes()

[#32857](https://github.com/flutter/flutter/pull/32857) Add debugNetworkImageHttpClientProvider

[#32936](https://github.com/flutter/flutter/pull/32936) Add some sanity to the ImageStream listener API

[#33729](https://github.com/flutter/flutter/pull/33729) Update consolidateHttpClientResponseBytes() to use compressionState

[#33814](https://github.com/flutter/flutter/pull/33814) Added a benchmark for ImageCache

## a: animation - 4 PRs

[#21896](https://github.com/flutter/flutter/pull/21896) Bottom sheet scrolling

[#29677](https://github.com/flutter/flutter/pull/29677) Fix calculation of hero rectTween when Navigator isn't fullscreen

[#32730](https://github.com/flutter/flutter/pull/32730) Add reverseDuration to AnimationController

[#32842](https://github.com/flutter/flutter/pull/32842) Allow "from" hero state to survive hero animation in a push transition

## ▣ platform-android - 4 PRs

[#31491](https://github.com/flutter/flutter/pull/31491) Allow adb stdout to contain the port number without failing

[#32950](https://github.com/flutter/flutter/pull/32950) Material allows "select all" when not collapsed

[#33191](https://github.com/flutter/flutter/pull/33191) Remove colon from Gradle task name since it's deprecated

[#33696](https://github.com/flutter/flutter/pull/33696) Generate ELF shared libraries and allow multi-abi libs in APKs and App bundles

## d: examples - 4 PRs

[#31634](https://github.com/flutter/flutter/pull/31634) Improve canvas example in sample dart ui app

[#31822](https://github.com/flutter/flutter/pull/31822) remove unnecessary artificial delay in catalog example

[#31929](https://github.com/flutter/flutter/pull/31929) Sample Code & Animation for Flow Widget

[#35046](https://github.com/flutter/flutter/pull/35046) Add generated Icon diagram to api docs

## a: typography - 4 PRs

[#31987](https://github.com/flutter/flutter/pull/31987) Text wrap width

[#33794](https://github.com/flutter/flutter/pull/33794) Text inline widgets, TextSpan rework

[#33946](https://github.com/flutter/flutter/pull/33946) Reland "Text inline widgets, TextSpan rework"

[#34051](https://github.com/flutter/flutter/pull/34051) Reland "Text inline widgets, TextSpan rework (#30069)" with improved backwards compatibility

## t: xcode - 4 PRs

[#32266](https://github.com/flutter/flutter/pull/32266) Add reference to Runner-Bridging-Header.h to iOS profile config

[#33684](https://github.com/flutter/flutter/pull/33684) Disable CocoaPods input and output paths in Xcode build phase and adopt new Xcode build system

[#34293](https://github.com/flutter/flutter/pull/34293) Change Xcode developmentRegion to 'en' and CFBundleDevelopmentRegion to DEVELOPMENT_LANGUAGE

[#34738](https://github.com/flutter/flutter/pull/34738) Update Xcode projects to recommended Xcode 10 project settings

## severe: crash - 3 PRs

[#31228](https://github.com/flutter/flutter/pull/31228) Fix ExpansionPanelList Duplicate Global Keys Exception

[#31581](https://github.com/flutter/flutter/pull/31581) Fix Exception on Nested TabBarView disposal

[#34460](https://github.com/flutter/flutter/pull/34460) Add back ability to override the local engine in Gradle

## ⌺‬ platform-ios - 3 PRs

[#31332](https://github.com/flutter/flutter/pull/31332) iOS selection handles are invisible

[#31687](https://github.com/flutter/flutter/pull/31687) Center iOS caret, remove constant offsets that do not scale

[#33684](https://github.com/flutter/flutter/pull/33684) Disable CocoaPods input and output paths in Xcode build phase and adopt new Xcode build system

## a: internationalization - 3 PRs

[#31644](https://github.com/flutter/flutter/pull/31644) Cupertino localization step 12: push translation for all supported languages

[#32013](https://github.com/flutter/flutter/pull/32013) Cupertino Turkish Translation

[#32513](https://github.com/flutter/flutter/pull/32513) Cupertino localization step 12 try 2: push translation for all supported languages

## ○ platform-fuchsia - 3 PRs

[#32849](https://github.com/flutter/flutter/pull/32849) [flutter_tool] Adds support for 'run' for Fuchsia devices

[#34162](https://github.com/flutter/flutter/pull/34162) Update the Fuchsia SDK

[#34276](https://github.com/flutter/flutter/pull/34276) [flutter_tool,fuchsia] Prefetch tiles when starting an app

## a: existing-apps - 3 PRs

[#33297](https://github.com/flutter/flutter/pull/33297) Instrument add to app flows

[#33450](https://github.com/flutter/flutter/pull/33450) Do not return null from IosProject.isSwift

[#34189](https://github.com/flutter/flutter/pull/34189) Instrument usage of include_flutter.groovy and xcode_backend.sh

## a: accessibility - 3 PRs

[#33808](https://github.com/flutter/flutter/pull/33808) fix ExpansionPanelList merge the header semantics when it is not nece…

[#34368](https://github.com/flutter/flutter/pull/34368) Fix semantics_tester

[#34434](https://github.com/flutter/flutter/pull/34434) Semantics fixes

## customer: solaris - 2 PRs

[#29677](https://github.com/flutter/flutter/pull/29677) Fix calculation of hero rectTween when Navigator isn't fullscreen

[#34298](https://github.com/flutter/flutter/pull/34298) Preserving SafeArea : Part 2

## e: desktop - 2 PRs

[#31329](https://github.com/flutter/flutter/pull/31329) Add Xcode build script for macOS target

[#31567](https://github.com/flutter/flutter/pull/31567) Remove need for build/name scripts on Linux desktop

## ⌘‬ platform-mac - 2 PRs

[#31329](https://github.com/flutter/flutter/pull/31329) Add Xcode build script for macOS target

[#33636](https://github.com/flutter/flutter/pull/33636) Implement plugin tooling support for macOS

## a: fidelity - 2 PRs

[#31464](https://github.com/flutter/flutter/pull/31464) CupertinoPicker fidelity revision

[#33634](https://github.com/flutter/flutter/pull/33634) Let there be scroll bars

## f: date/time picker - 2 PRs

[#31464](https://github.com/flutter/flutter/pull/31464) CupertinoPicker fidelity revision

[#32843](https://github.com/flutter/flutter/pull/32843) Added a missing dispose of an AnimationController that was leaking a ticker.

## team: gallery - 2 PRs

[#31486](https://github.com/flutter/flutter/pull/31486) fix precedence issue

[#33634](https://github.com/flutter/flutter/pull/33634) Let there be scroll bars

## dependency: dart - 2 PRs

[#32787](https://github.com/flutter/flutter/pull/32787) Support 32 and 64 bit

[#34295](https://github.com/flutter/flutter/pull/34295) Prepare for Uint8List SDK breaking changes

## team: flakes - 2 PRs

[#33932](https://github.com/flutter/flutter/pull/33932) More removing of timeouts.

[#34199](https://github.com/flutter/flutter/pull/34199) make sure this test doesn't run for real

## customer: espresso - 2 PRs

[#34012](https://github.com/flutter/flutter/pull/34012) Extract DiagnosticsNode serializer from WidgetInspector

[#34440](https://github.com/flutter/flutter/pull/34440) Add Driver command to get diagnostics tree

## severe: customer critical - 2 PRs

[#34298](https://github.com/flutter/flutter/pull/34298) Preserving SafeArea : Part 2

[#34519](https://github.com/flutter/flutter/pull/34519) fix page scroll position rounding error

## t: flutter doctor - 2 PRs

[#34624](https://github.com/flutter/flutter/pull/34624) Break down flutter doctor validations and results

[#34755](https://github.com/flutter/flutter/pull/34755) Add linux doctor implementation

## f: routes - 1 PRs

[#21896](https://github.com/flutter/flutter/pull/21896) Bottom sheet scrolling

## p: framework - 1 PRs

[#30406](https://github.com/flutter/flutter/pull/30406) Add binaryMessenger constructor argument to platform channels

## ❖ platform-windows - 1 PRs

[#33443](https://github.com/flutter/flutter/pull/33443) Wrap Windows build invocation in a batch script

## a: size - 1 PRs

[#34474](https://github.com/flutter/flutter/pull/34474) Release diagnostics

## customer: google - 1 PRs

[#34474](https://github.com/flutter/flutter/pull/34474) Release diagnostics

## severe: performance - 1 PRs

[#34870](https://github.com/flutter/flutter/pull/34870) Add test case for Flutter Issue #27677 as a benchmark.


This release included 841 PRs.
Not all may be shown; some PRs such as those generated by autorollers are omitted.


# PRs addressed in `flutter/engine` between `b593f5167bce84fb3cad5c258477bf3abc1b14eb` and `d004bcd4d619fc3574761d63d7cf7b7291332c79`


## affects: text input - 2 PRs

[#9322](https://github.com/flutter/engine/pull/9322) Check for invalid indexes when performing InputAdpator backspace.

[#9406](https://github.com/flutter/engine/pull/9406) Update harfbuzz to 2.5.2

## platform-android - 1 PRs

[#9172](https://github.com/flutter/engine/pull/9172) Use shared library when libapp.so is found

## affects: dev experience - 1 PRs

[#9304](https://github.com/flutter/engine/pull/9304) Decorate UIApplicationDelegate wrappers with matching UIKit deprecation

## platform-ios - 1 PRs

[#9304](https://github.com/flutter/engine/pull/9304) Decorate UIApplicationDelegate wrappers with matching UIKit deprecation

## accessibility - 1 PRs

[#9321](https://github.com/flutter/engine/pull/9321) Fix a11y in embedded Android views post O

## crash - 1 PRs

[#9322](https://github.com/flutter/engine/pull/9322) Check for invalid indexes when performing InputAdpator backspace.

## affects: framework - 1 PRs

[#9452](https://github.com/flutter/engine/pull/9452) Convert RRect.scaleRadii to public method

## affects: tests - 1 PRs

[#9458](https://github.com/flutter/engine/pull/9458) Test cleanup geometry_test.dart


This release included 605 PRs.
Not all may be shown; some PRs such as those generated by autorollers are omitted.
