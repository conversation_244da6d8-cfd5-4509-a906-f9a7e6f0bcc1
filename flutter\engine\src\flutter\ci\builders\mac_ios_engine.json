{"_comment": ["The builds defined in this file should not contain tests, ", "and the file should not contain builds that are essentially tests. ", "The only builds in this file should be the builds necessary to produce ", "release artifacts. ", "Tests to run on mac hosts should go in one of the other mac_ build ", "definition files."], "luci_flags": {"delay_collect_builds": true, "parallel_download_builds": true}, "builds": [{"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_debug_sim", "--ios", "--runtime-mode", "debug", "--simulator", "--no-lto", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_debug_sim", "description": "Produces debug mode artifacts to target the x64 iOS simulator.", "ninja": {"config": "ci/ios_debug_sim"}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_debug_sim", "--ios", "--runtime-mode", "debug", "--simulator", "--no-lto", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}, {"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_debug_sim_arm64", "--ios", "--runtime-mode", "debug", "--simulator", "--simulator-cpu=arm64", "--no-lto", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_debug_sim_arm64", "description": "Produces debug mode artifacts to target the arm64 iOS simulator.", "ninja": {"config": "ci/ios_debug_sim_arm64"}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_debug_sim_arm64", "--ios", "--runtime-mode", "debug", "--simulator", "--simulator-cpu=arm64", "--no-lto", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}, {"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_debug", "--ios", "--runtime-mode", "debug", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_debug", "description": "Produces debug mode artifacts to target iOS.", "ninja": {"config": "ci/ios_debug", "targets": ["flutter/lib/snapshot:generate_snapshot_bins", "flutter/shell/platform/darwin/ios:flutter_framework"]}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_debug", "--ios", "--runtime-mode", "debug", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}, {"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_profile", "--ios", "--runtime-mode", "profile", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_profile", "description": "Produces profile mode artifacts to target iOS.", "ninja": {"config": "ci/ios_profile", "targets": ["flutter/lib/snapshot:generate_snapshot_bins", "flutter/shell/platform/darwin/ios:flutter_framework"]}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_profile", "--ios", "--runtime-mode", "profile", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}, {"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_release", "--ios", "--runtime-mode", "release", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_release", "description": "Produces release mode artifacts to target iOS.", "ninja": {"config": "ci/ios_release", "targets": ["flutter/lib/snapshot:generate_snapshot_bins", "flutter/shell/platform/darwin/ios:flutter_framework"]}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_release", "--ios", "--runtime-mode", "release", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}, {"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_debug_sim_extension_safe", "--ios", "--runtime-mode", "debug", "--simulator", "--no-lto", "--darwin-extension-safe", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_debug_sim_extension_safe", "description": "Produces extension safe debug mode artifacts to target the x64 iOS simulator.", "ninja": {"config": "ci/ios_debug_sim_extension_safe"}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_debug_sim_extension_safe", "--ios", "--runtime-mode", "debug", "--simulator", "--no-lto", "--darwin-extension-safe", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}, {"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_debug_sim_arm64_extension_safe", "--ios", "--runtime-mode", "debug", "--simulator", "--simulator-cpu=arm64", "--no-lto", "--darwin-extension-safe", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_debug_sim_arm64_extension_safe", "description": "Produces extension safe debug mode artifacts to target the arm64 iOS simulator.", "ninja": {"config": "ci/ios_debug_sim_arm64_extension_safe"}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_debug_sim_arm64_extension_safe", "--ios", "--runtime-mode", "debug", "--simulator", "--simulator-cpu=arm64", "--no-lto", "--darwin-extension-safe", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}, {"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_debug_extension_safe", "--ios", "--runtime-mode", "debug", "--darwin-extension-safe", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_debug_extension_safe", "description": "Produces extension safe debug mode artifacts to target iOS.", "ninja": {"config": "ci/ios_debug_extension_safe", "targets": ["flutter/lib/snapshot:generate_snapshot_bins", "flutter/shell/platform/darwin/ios:flutter_framework"]}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_debug_extension_safe", "--ios", "--runtime-mode", "debug", "--darwin-extension-safe", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}, {"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_profile_extension_safe", "--ios", "--runtime-mode", "profile", "--darwin-extension-safe", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_profile_extension_safe", "description": "Produces extension safe profile mode artifacts to target iOS.", "ninja": {"config": "ci/ios_profile_extension_safe", "targets": ["flutter/lib/snapshot:generate_snapshot_bins", "flutter/shell/platform/darwin/ios:flutter_framework"]}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_profile_extension_safe", "--ios", "--runtime-mode", "profile", "--darwin-extension-safe", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}, {"drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/ios_release_extension_safe", "--ios", "--runtime-mode", "release", "--darwin-extension-safe", "--rbe", "--no-goma", "--xcode-symlinks"], "name": "ci/ios_release_extension_safe", "description": "Produces extension safe release mode artifacts to target iOS.", "ninja": {"config": "ci/ios_release_extension_safe", "targets": ["flutter/lib/snapshot:generate_snapshot_bins", "flutter/shell/platform/darwin/ios:flutter_framework"]}, "postsubmit_overrides": {"gn": ["--target-dir", "ci/ios_release_extension_safe", "--ios", "--runtime-mode", "release", "--darwin-extension-safe", "--no-rbe", "--no-goma"]}, "properties": {"$flutter/osx_sdk": {"sdk_version": "16c5032a"}}}], "generators": {"tasks": [{"name": "Debug-ios-Flutter.xcframework", "parameters": ["--dst", "out/debug", "--arm64-out-dir", "out/ci/ios_debug", "--simulator-x64-out-dir", "out/ci/ios_debug_sim", "--simulator-arm64-out-dir", "out/ci/ios_debug_sim_arm64"], "script": "flutter/sky/tools/create_ios_framework.py", "language": "python3"}, {"name": "Profile-ios-Flutter.xcframework", "parameters": ["--dst", "out/profile", "--arm64-out-dir", "out/ci/ios_profile", "--simulator-x64-out-dir", "out/ci/ios_debug_sim", "--simulator-arm64-out-dir", "out/ci/ios_debug_sim_arm64"], "script": "flutter/sky/tools/create_ios_framework.py", "language": "python3"}, {"name": "Release-ios-Flutter.xcframework", "parameters": ["--dst", "out/release", "--arm64-out-dir", "out/ci/ios_release", "--simulator-x64-out-dir", "out/ci/ios_debug_sim", "--simulator-arm64-out-dir", "out/ci/ios_debug_sim_arm64", "--d<PERSON>m", "--strip"], "script": "flutter/sky/tools/create_ios_framework.py", "language": "python3"}, {"name": "Verify-export-symbols-release-binaries", "parameters": ["src/out/ci", "src/flutter/buildtools"], "script": "flutter/testing/symbols/verify_exported.dart", "language": "dart"}]}, "archives": [{"source": "out/debug/artifacts.zip", "destination": "ios/artifacts.zip", "realm": "production"}, {"source": "out/profile/artifacts.zip", "destination": "ios-profile/artifacts.zip", "realm": "production"}, {"source": "out/release/artifacts.zip", "destination": "ios-release/artifacts.zip", "realm": "production"}]}