// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "flutter/shell/platform/windows/testing/mock_text_input_manager.h"

namespace flutter {
namespace testing {

MockTextInputManager::MockTextInputManager() : TextInputManager(){};

MockTextInputManager::~MockTextInputManager() = default;

}  // namespace testing
}  // namespace flutter
