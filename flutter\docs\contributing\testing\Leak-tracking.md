# Leak tracking in Flutter Framework

## TL;DR;

To enable leak tracking locally pass `--dart-define LEAK_TRACKING=true` to `flutter test`.

See leak_tracker documentation for
[general leak troubleshooting](https://github.com/dart-lang/leak_tracker/blob/main/doc/leak_tracking/TROUBLESHOOT.md).

This page contains Flutter Framework specific information.

## Overview

Flutter Framework widget tests use [leak_tracker](https://github.com/dart-lang/leak_tracker/blob/main/doc/leak_tracking/
OVERVIEW.md) to detect leaks from objects that have not been disposed.

Test failures cause by leaks look like this:

```
Expected: leak free
    Actual: <Instance of 'Leaks'>
     Which: contains leaks:
            # The text is generated by leak_tracker.
            # For leak troubleshooting tips open:
            # https://github.com/flutter/flutter/blob/main/docs/contributing/testing/Leak-tracking.md
            notDisposed:
              total: 1
              objects:
                FocusNode:
                  test: Align smoke test
                  identityHashCode: 82308154
```

## When is it ok to opt-out from leak tracking?

In general, leak tracking should be enabled, to verify that all
disposables are disposed.
If a tests is opted out, the reasons should be clearly explained
in the comments.

It is ok to opt out a test when the test
throws an exception and thus the code did not finalize properly.

While some exceptions should be finalized properly
and should not result in leaking objects,
the project to make sure disposables are disposed
in exceptional code paths was not prioritized yet.
See [issue #157470](https://github.com/flutter/flutter/issues/157470).

## Where leak tracking is configured?

Leak tracker is configured in [flutter_test_config.dart](https://github.com/flutter/flutter/blob/9441f8f6c806fb0a3b7d058a40b5e59c373e6055/packages/flutter/test/flutter_test_config.dart#L45).

See [leak_tracker documentation](https://github.com/dart-lang/leak_tracker/blob/main/doc/leak_tracking/TROUBLESHOOT.md)
on how to enable/disable leak tracking for individual tests and files.

## What are defaults?

Leak tracking is enabled on two test shards in Flutter's testing infrastructure:

- [Windows framework_tests_libraries_leak_tracking](https://github.com/flutter/flutter/blob/9441f8f6c806fb0a3b7d058a40b5e59c373e6055/.ci.yaml#L5553)
- [Windows framework_tests_widgets_leak_tracking](https://github.com/flutter/flutter/blob/9441f8f6c806fb0a3b7d058a40b5e59c373e6055/.ci.yaml#L5640C11-L5640C56)

For local testing, or for test shards configuration, to enable leak tracking for flutter tests, pass
`--dart-define LEAK_TRACKING=true` to `flutter test`.

You can see the bot's status on the [Flutter build dashboard](https://flutter-dashboard.appspot.com/#/build).
