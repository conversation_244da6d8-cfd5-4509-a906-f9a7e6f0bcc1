# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/impeller/tools/impeller.gni")
import("//flutter/shell/version/version.gni")

impeller_component("utilities") {
  # Current versions of libcxx have deprecated some of the UTF-16 string
  # conversion APIs.
  defines = [ "_LIBCPP_DISABLE_DEPRECATION_WARNINGS" ]

  sources = [
    "utilities.cc",
    "utilities.h",
  ]

  public_deps = [
    "../base",
    "../geometry",
    "../runtime_stage",
    "//flutter/fml",
  ]
}

impeller_component("compiler_lib") {
  include_dirs = [ "//flutter/third_party/vulkan-deps/spirv-cross/src/" ]

  # Current versions of libcxx have deprecated some of the UTF-16 string
  # conversion APIs.
  defines = [ "_LIBCPP_DISABLE_DEPRECATION_WARNINGS" ]

  sources = [
    "code_gen_template.h",
    "compiler.cc",
    "compiler.h",
    "compiler_backend.cc",
    "compiler_backend.h",
    "constants.cc",
    "constants.h",
    "include_dir.h",
    "includer.cc",
    "includer.h",
    "logger.h",
    "reflector.cc",
    "reflector.h",
    "runtime_stage_data.cc",
    "runtime_stage_data.h",
    "shader_bundle.cc",
    "shader_bundle.h",
    "shader_bundle_data.cc",
    "shader_bundle_data.h",
    "source_options.cc",
    "source_options.h",
    "spirv_compiler.cc",
    "spirv_compiler.h",
    "spirv_sksl.cc",
    "spirv_sksl.h",
    "switches.cc",
    "switches.h",
    "types.cc",
    "types.h",
    "uniform_sorter.cc",
    "uniform_sorter.h",
  ]

  public_deps = [
    ":utilities",
    "../base",
    "../geometry",
    "../runtime_stage",
    "//flutter/fml",
    "//flutter/impeller/shader_bundle:shader_bundle_flatbuffers",

    # All third_party deps must be included by the global license script.
    "//third_party/inja",
    "//third_party/shaderc_flutter",
    "//third_party/spirv_cross_flutter",
  ]
}

impeller_component("impellerc") {
  target_type = "executable"

  sources = [ "impellerc_main.cc" ]

  deps = [
    ":compiler_lib",
    "shader_lib",
  ]

  metadata = {
    entitlement_file_path = [ "impellerc" ]
  }
}

group("compiler") {
  deps = [ ":impellerc" ]
}

impeller_component("compiler_unittests") {
  testonly = true

  output_name = "impellerc_unittests"

  sources = [
    "compiler_test.cc",
    "compiler_test.h",
    "compiler_unittests.cc",
    "shader_bundle_unittests.cc",
    "switches_unittests.cc",
  ]

  deps = [
    ":compiler_lib",
    "//flutter/testing:testing_lib",
  ]
}
