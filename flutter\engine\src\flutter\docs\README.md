This is an index of team-facing documentation for the [flutter/engine repository](https://github.com/flutter/engine/).

- [Accessibility on Windows](https://github.com/flutter/flutter/blob/master/docs/platforms/desktop/windows/Accessibility-on-Windows.md)
- [Code signing metadata](./release/Code-signing-metadata.md) for engine binaries
- [Compiling the engine](./contributing/Compiling-the-engine.md)
- [Comparing AOT Snapshot Sizes](./benchmarks/Comparing-AOT-Snapshot-Sizes.md)
- [Crashes](./Crashes.md)
- [Custom Flutter engine embedders](./Custom-Flutter-Engine-Embedders.md)
- [Custom Flutter Engine Embedding in AOT Mode](./Custom-Flutter-Engine-Embedding-in-AOT-Mode.md)
- [Debugging the engine](./Debugging-the-engine.md)
- [Flutter engine operation in AOT Mode](./Flutter-engine-operation-in-AOT-Mode.md)
- [Flutter Test Fonts](https://github.com/flutter/flutter/blob/master/docs/contributing/testing/Flutter-Test-Fonts.md)
- [Flutter's modes](./Flutter's-modes.md)
- [Engine Clang Tidy Linter](./ci/Engine-Clang-Tidy-Linter.md)
- [Engine disk footprint](./Engine-disk-footprint.md)
- [Engine-specific Service Protocol extensions](./Engine-specific-Service-Protocol-extensions.md)
- [Engine pre‐submits and post‐submits](./ci/Engine-pre-submits-and-post-submits.md)
- [Image Codecs in the Flutter Engine](Image-Codecs-in-the-Flutter-Engine.md)
- [Impeller](./impeller/README.md) documentation index
- [Life of a Flutter Frame](Life-of-a-Flutter-Frame.md)
- [Reduce Flutter engine size with MLGO](Reduce-Flutter-engine-size-with-MLGO.md)
- [Resolving common build failures](https://github.com/flutter/flutter/blob/master/docs/platforms/android/Resolving-common-build-failures.md)
- [Setting up the Engine development environment](./contributing/Setting-up-the-Engine-development-environment.md)
- [Supporting legacy platforms](Supporting-legacy-platforms.md)
- [Testing Android Changes in the Devicelab on an Emulator](https://github.com/flutter/flutter/blob/master/docs/platforms/android/Testing-Android-Changes-in-the-Devicelab-on-an-Emulator.md)
- [Testing the engine](./testing/Testing-the-engine.md)
- [The Engine architecture](https://github.com/flutter/flutter/blob/master/docs/about/The-Engine-architecture.md)
- [Upgrading Engine's Android API version](https://github.com/flutter/flutter/blob/master/docs/platforms/android/Upgrading-Engine's-Android-API-version.md)
- [Using the Dart Development Service (DDS) and Flutter DevTools with a custom Flutter Engine Embedding](./Using-the-Dart-Development-Service-(DDS)-and-Flutter-DevTools-with-a-custom-Flutter-Engine-Embedding.md)
- [Using Sanitizers with the Flutter Engine](./Using-Sanitizers-with-the-Flutter-Engine.md)
