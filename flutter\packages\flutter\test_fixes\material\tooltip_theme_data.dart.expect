// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';

void main() {
  // Changes made in https://github.com/flutter/flutter/pull/163314
  TooltipThemeData tooltipThemeData = TooltipThemeData();
  tooltipThemeData = TooltipThemeData();
  tooltipThemeData = TooltipThemeData(constraints: BoxConstraints(minHeight: 15.0));
  tooltipThemeData = TooltipThemeData(constraints: BoxConstraints(minHeight: 15.0));
  tooltipThemeData = TooltipThemeData(
    constraints: BoxConstraints(maxWidth: 20.0),
  );
  tooltipThemeData.constraints?.minHeight;
  tooltipThemeData.copyWith();
  tooltipThemeData.copyWith(constraints: BoxConstraints(minHeight: 15.0));
  tooltipThemeData.copyWith(constraints: BoxConstraints(minHeight: 15.0));
  tooltipThemeData.copyWith(
    constraints: BoxConstraints(maxWidth: 20.0),
  );
}
