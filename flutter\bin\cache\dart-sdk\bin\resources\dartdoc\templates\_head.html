<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="{{ metaDescription }}">
  <title>{{ title }}</title>
  {{ #relCanonicalPrefix }}
  <link rel="canonical" href="{{{relCanonicalPrefix}}}/{{{bareHref}}}">
  {{ /relCanonicalPrefix}}

  {{#useBaseHref}}{{! TODO(jdkoren): remove when the useBaseHref option is removed.}}
  {{#htmlBase}}
  <!-- required because all the links are pseudo-absolute -->
  <base href="{{{htmlBase}}}">
  {{/htmlBase}}
  {{/useBaseHref}}

  {{! TODO(jdkoren): unwrap ^useBaseHref sections when the option is removed.}}
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,300;0,400;0,500;0,700;1,400&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0" rel="stylesheet">
  {{! **Update versions for static assets when changed to force browsers to refresh them.** }}
  <link rel="stylesheet" href="{{^useBaseHref}}%%__HTMLBASE_dartdoc_internal__%%{{/useBaseHref}}static-assets/github.css?v1">
  <link rel="stylesheet" href="{{^useBaseHref}}%%__HTMLBASE_dartdoc_internal__%%{{/useBaseHref}}static-assets/styles.css?v1">
  <link rel="icon" href="{{^useBaseHref}}%%__HTMLBASE_dartdoc_internal__%%{{/useBaseHref}}static-assets/favicon.png?v1">

  {{{ customHeader }}}
</head>

{{! We don't use <base href>, but we do lookup the htmlBase from javascript. }}
<body data-base-href="{{{htmlBase}}}" data-using-base-href="{{{useBaseHref}}}" class="light-theme">

<div id="overlay-under-drawer"></div>

<header id="title">
  <span id="sidenav-left-toggle" class="material-symbols-outlined" role="button" tabindex="0">menu</span>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    {{ #navLinks }}
    <li><a href="{{{ href }}}">{{ breadcrumbName }}</a></li>
    {{ /navLinks }}
    {{ #navLinksWithGenerics }}
    <li><a href="{{{ href }}}">{{ breadcrumbName }}{{ #hasGenericParameters }}<span class="signature">{{{ genericParameters }}}</span>{{ /hasGenericParameters }}</a></li>
    {{ /navLinksWithGenerics }}
    {{^hasHomepage}}
    <li class="self-crumb">{{{ layoutTitle }}}</li>
    {{/hasHomepage}}
    {{#hasHomepage}}
    <li><a href="{{{homepage}}}">{{{ layoutTitle }}}</a></li>
    {{/hasHomepage}}
  </ol>
  <div class="self-name">{{self.name}}</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
  <button class="toggle" id="theme-button" title="Toggle between light and dark mode" aria-label="Light and dark mode toggle">
    <span id="dark-theme-button" class="material-symbols-outlined" aria-hidden="true">
      dark_mode
    </span>
    <span id="light-theme-button" class="material-symbols-outlined" aria-hidden="true">
      light_mode
    </span>
  </button>
</header>
<main>