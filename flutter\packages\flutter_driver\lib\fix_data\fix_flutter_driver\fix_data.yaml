# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the
# flutter/packages/flutter_driver/test_fixes/README.md file for instructions
# on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

version: 1
transforms:
# Changes made in https://github.com/flutter/flutter/pull/82939
- title: 'Migrate to setSemantics'
  date: 2024-01-10
  element:
    uris: [ 'flutter_driver.dart' ]
    method: 'enableAccessibility'
    inClass: 'FlutterDriver'
  changes:
  - kind: 'rename'
    newName: 'setSemantics'
  - kind: 'addParameter'
    index: 0
    name: 'enabled'
    style: 'required_positional'
    argumentValue:
      expression: 'true'

# Changes made in https://github.com/flutter/flutter/pull/79310
- title: 'Migrate to writeTimelineToFile'
  date: 2024-01-10
  element:
    uris: [ 'flutter_driver.dart' ]
    method: 'writeSummaryToFile'
    inClass: 'TimelineSummary'
  changes:
  - kind: 'rename'
    newName: 'writeTimelineToFile'
