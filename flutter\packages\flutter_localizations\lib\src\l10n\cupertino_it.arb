{"datePickerHourSemanticsLabelOne": "$hour in punto", "datePickerHourSemanticsLabelOther": "$hour in punto", "datePickerMinuteSemanticsLabelOne": "1 minuto", "datePickerMinuteSemanticsLabelOther": "$minute minuti", "datePickerDateOrder": "dmy", "datePickerDateTimeOrder": "date_time_dayPeriod", "anteMeridiemAbbreviation": "AM", "postMeridiemAbbreviation": "PM", "todayLabel": "<PERSON><PERSON><PERSON>", "alertDialogLabel": "Avviso", "timerPickerHourLabelOne": "ora", "timerPickerHourLabelOther": "ore", "timerPickerMinuteLabelOne": "min.", "timerPickerMinuteLabelOther": "min.", "timerPickerSecondLabelOne": "sec.", "timerPickerSecondLabelOther": "sec.", "cutButtonLabel": "Taglia", "copyButtonLabel": "Copia", "pasteButtonLabel": "<PERSON><PERSON><PERSON>", "selectAllButtonLabel": "Se<PERSON><PERSON>na tutto", "tabSemanticsLabel": "Scheda $tabIndex di $tabCount", "modalBarrierDismissLabel": "Ignora", "searchTextFieldPlaceholderLabel": "Cerca", "noSpellCheckReplacementsLabel": "Nessuna sostituzione trovata", "menuDismissLabel": "Ignora menu", "lookUpButtonLabel": "Cerca", "searchWebButtonLabel": "Cerca sul web", "shareButtonLabel": "<PERSON><PERSON><PERSON><PERSON>…", "clearButtonLabel": "Can<PERSON><PERSON>", "cancelButtonLabel": "<PERSON><PERSON><PERSON>", "backButtonLabel": "Indietro"}