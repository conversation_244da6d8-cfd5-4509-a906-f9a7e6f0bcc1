// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by dev/tools/gen_keycodes/bin/gen_keycodes.dart and
// should not be edited directly.
//
// Edit the template dev/tools/gen_keycodes/data/web_key_map_dart.tmpl instead.
// See dev/tools/gen_keycodes/README.md for more information.

/// Maps Web KeyboardEvent keys to the matching LogicalKeyboardKey id.
const Map<String, int> kWebToLogicalKey = <String, int>{
@@@WEB_LOGICAL_KEY_CODE_MAP@@@
};

/// Maps Web KeyboardEvent codes to the matching PhysicalKeyboardKey USB HID code.
const Map<String, int> kWebToPhysicalKey = <String, int>{
@@@WEB_PHYSICAL_KEY_CODE_MAP@@@
};

/// Maps Web KeyboardEvent keys to Flutter logical IDs that depend on locations.
///
/// `KeyboardEvent.location` is defined as:
///
///  * 0: Standard
///  * 1: Left
///  * 2: Right
///  * 3: Numpad
const Map<String, List<int?>> kWebLogicalLocationMap = <String, List<int?>>{
@@@WEB_LOGICAL_LOCATION_MAP@@@
};
