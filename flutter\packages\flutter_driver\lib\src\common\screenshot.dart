// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'dart:convert';
import 'dart:typed_data';

import 'message.dart';

/// Format of data returned by screenshot driver command.
enum ScreenshotFormat {
  /// Raw RGBA format.
  ///
  /// Unencoded bytes, in RGBA row-primary form with premultiplied alpha, 8 bits per channel.
  rawRgba,

  /// Raw straight RGBA format.
  ///
  /// Unencoded bytes, in RGBA row-primary form with straight alpha, 8 bits per channel.
  rawStraightRgba,

  /// Raw unmodified format.
  rawUnmodified,

  /// Raw extended range RGBA format.
  ///
  /// Unencoded bytes, in RGBA row-primary form with straight alpha, 32 bit
  /// float (IEEE 754 binary32) per channel.
  rawExtendedRgba128,

  /// PNG format.
  png,
}

/// A Flutter Driver command that takes a screenshot.
class ScreenshotCommand extends Command {
  /// Constructs this command to take a screenshot.
  ScreenshotCommand({super.timeout, this.format = ScreenshotFormat.png});

  /// Deserializes this command from the value generated by [serialize].
  ScreenshotCommand.deserialize(super.json)
    : format = ScreenshotFormat.values[int.tryParse(json['format']!) ?? 4],
      super.deserialize();

  /// Whether the resulting data is PNG compressed.
  final ScreenshotFormat format;

  /// Serializes this command to parameter name/value pairs.
  @override
  Map<String, String> serialize() {
    return super.serialize()..addAll(<String, String>{'format': format.index.toString()});
  }

  @override
  String get kind => 'screenshot';
}

/// base64 encode a PNG
class ScreenshotResult extends Result {
  /// Constructs a screenshot result with PNG or raw RGBA byte data.
  ScreenshotResult(this._data);

  final Uint8List _data;

  @override
  Map<String, Object?> toJson() {
    return <String, Object?>{'data': base64.encode(_data)};
  }
}
