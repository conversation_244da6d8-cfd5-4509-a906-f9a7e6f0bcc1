# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/tools/fuchsia/dart/dart_component.gni")
import("//flutter/tools/fuchsia/fuchsia_archive.gni")
import("//flutter/tools/fuchsia/gn-sdk/src/gn_configs.gni")

group("tests") {
  testonly = true
  deps = [ ":dart-jit-runner-integration-test" ]
}

executable("dart-jit-runner-integration-test-bin") {
  testonly = true

  output_name = "dart-jit-runner-integration-test"

  sources = [ "dart-jit-runner-integration-test.cc" ]

  # This is needed for //flutter/third_party/googletest for linking zircon
  # symbols.
  libs = [ "${fuchsia_arch_root}/sysroot/lib/libzircon.so" ]

  deps = [
    "${fuchsia_sdk}/fidl/fuchsia.logger",
    "${fuchsia_sdk}/fidl/fuchsia.tracing.provider",
    "${fuchsia_sdk}/pkg/async",
    "${fuchsia_sdk}/pkg/async-loop-testing",
    "${fuchsia_sdk}/pkg/fidl_cpp",
    "${fuchsia_sdk}/pkg/sys_component_cpp_testing",
    "${fuchsia_sdk}/pkg/zx",
    "../dart_echo_server:jit_echo_package",
    "//flutter/fml",
    "//flutter/shell/platform/fuchsia/dart_runner/fidl:dart_test",
    "//flutter/third_party/googletest:gtest",
    "//flutter/third_party/googletest:gtest_main",
  ]
}

fuchsia_test_archive("dart-jit-runner-integration-test") {
  deps = [ ":dart-jit-runner-integration-test-bin" ]
}
