# Copyright 2021 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# TODO(flutter/flutter#85356): This file was originally generated by the
# fuchsia.git script: `package_importer.py`. The generated `BUILD.gn` files were
# copied to the flutter repo to support `dart_library` targets used for
# Flutter-Fuchsia integration tests. This file can be maintained by hand, but it
# would be better to implement a script for Flutter, to either generate these
# BUILD.gn files or dynamically generate the GN targets.

import("//flutter/tools/fuchsia/dart/dart_library.gni")

dart_library("path") {
  package_name = "path"

  language_version = "2.12"

  deps = []

  sources = [
    "path.dart",
    "src/characters.dart",
    "src/context.dart",
    "src/internal_style.dart",
    "src/parsed_path.dart",
    "src/path_exception.dart",
    "src/path_map.dart",
    "src/path_set.dart",
    "src/style.dart",
    "src/style/posix.dart",
    "src/style/url.dart",
    "src/style/windows.dart",
    "src/utils.dart",
  ]
}
