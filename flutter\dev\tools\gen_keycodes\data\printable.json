{"Backquote": "`", "Backslash": "\\", "BracketLeft": "[", "BracketRight": "]", "Comma": ",", "Digit0": "0", "Digit1": "1", "Digit2": "2", "Digit3": "3", "Digit4": "4", "Digit5": "5", "Digit6": "6", "Digit7": "7", "Digit8": "8", "Digit9": "9", "Equal": "=", "KeyA": "a", "KeyB": "b", "KeyC": "c", "KeyD": "d", "KeyE": "e", "KeyF": "f", "KeyG": "g", "KeyH": "h", "KeyI": "i", "KeyJ": "j", "KeyK": "k", "KeyL": "l", "KeyM": "m", "KeyN": "n", "KeyO": "o", "KeyP": "p", "KeyQ": "q", "KeyR": "r", "KeyS": "s", "KeyT": "t", "KeyU": "u", "KeyV": "v", "KeyW": "w", "KeyX": "x", "KeyY": "y", "KeyZ": "z", "Minus": "-", "Numpad0": "0", "Numpad1": "1", "Numpad2": "2", "Numpad3": "3", "Numpad4": "4", "Numpad5": "5", "Numpad6": "6", "Numpad7": "7", "Numpad8": "8", "Numpad9": "9", "NumpadAdd": "+", "NumpadComma": ",", "NumpadDecimal": ".", "NumpadDivide": "/", "NumpadEqual": "=", "NumpadMultiply": "*", "NumpadParenLeft": "(", "NumpadParenRight": ")", "NumpadSubtract": "-", "Period": ".", "Quote": "'", "Semicolon": ";", "Slash": "/", "Space": " "}