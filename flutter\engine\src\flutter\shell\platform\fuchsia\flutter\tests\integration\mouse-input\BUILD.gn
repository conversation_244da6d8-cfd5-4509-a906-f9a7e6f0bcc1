# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

assert(is_fuchsia)

import("//flutter/tools/fuchsia/fuchsia_archive.gni")
import("//flutter/tools/fuchsia/gn-sdk/src/gn_configs.gni")
import("//flutter/tools/fuchsia/gn-sdk/src/package.gni")

group("tests") {
  testonly = true
  deps = [ ":mouse-input-test" ]
}

executable("mouse-input-test-bin") {
  testonly = true
  output_name = "mouse-input-test"
  sources = [ "mouse-input-test.cc" ]

  # This is needed for //flutter/third_party/googletest for linking zircon
  # symbols.
  libs = [ "${fuchsia_arch_root}/sysroot/lib/libzircon.so" ]

  deps = [
    "${fuchsia_sdk}/fidl/fuchsia.accessibility.semantics",
    "${fuchsia_sdk}/fidl/fuchsia.buildinfo",
    "${fuchsia_sdk}/fidl/fuchsia.component",
    "${fuchsia_sdk}/fidl/fuchsia.fonts",
    "${fuchsia_sdk}/fidl/fuchsia.intl",
    "${fuchsia_sdk}/fidl/fuchsia.kernel",
    "${fuchsia_sdk}/fidl/fuchsia.memorypressure",
    "${fuchsia_sdk}/fidl/fuchsia.metrics",
    "${fuchsia_sdk}/fidl/fuchsia.net.interfaces",
    "${fuchsia_sdk}/fidl/fuchsia.tracing.provider",
    "${fuchsia_sdk}/fidl/fuchsia.ui.app",
    "${fuchsia_sdk}/fidl/fuchsia.ui.display.singleton",
    "${fuchsia_sdk}/fidl/fuchsia.ui.input",
    "${fuchsia_sdk}/fidl/fuchsia.ui.pointerinjector",
    "${fuchsia_sdk}/fidl/fuchsia.ui.test.input",
    "${fuchsia_sdk}/fidl/fuchsia.ui.test.scene",
    "${fuchsia_sdk}/fidl/fuchsia.web",
    "${fuchsia_sdk}/pkg/async",
    "${fuchsia_sdk}/pkg/async-loop-testing",
    "${fuchsia_sdk}/pkg/fidl_cpp",
    "${fuchsia_sdk}/pkg/sys_component_cpp_testing",
    "${fuchsia_sdk}/pkg/zx",
    "mouse-input-view:package",
    "//flutter/fml",
    "//flutter/shell/platform/fuchsia/flutter/tests/integration/utils:portable_ui_test",
    "//flutter/third_party/googletest:gtest",
    "//flutter/third_party/googletest:gtest_main",
  ]
}

fuchsia_test_archive("mouse-input-test") {
  deps = [
    ":mouse-input-test-bin",

    # "OOT" copies of the runners used by tests, to avoid conflicting with the
    # runners in the base fuchsia image.
    # TODO(fxbug.dev/106575): Fix this with subpackages.
    "//flutter/shell/platform/fuchsia/flutter:oot_flutter_jit_runner",
  ]
}
