{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 932, "left": 778, "maximized": true, "right": 1616, "top": 40, "work_area_bottom": 912, "work_area_left": 0, "work_area_right": 1707, "work_area_top": 0}}, "cached_fonts": {"search_results_page": {"fonts": ["<PERSON><PERSON>", "Google Sans"]}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 18766, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "download_bubble": {"partial_view_impressions": 1}, "enterprise_profile_guid": "********-3b4e-460a-a2a2-949b768b7d07", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "137.0.7151.120"}, "gaia_cookie": {"changed_time": **********.596288, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "28680d95-f271-4594-bf91-f25265eb5c84"}}, "history_clusters": {"all_cache": {"all_keywords": {}, "all_timestamp": "*****************"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"en": 2}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "YOFpYTy6R+g+ykHcwH4mPZbJQSW+L4+Yz+SN+8qnHi4jW4AR2Qj6MrzybaAo8pig7eyEZEjK0wpu6/ivYkIrQA=="}, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13395077627880436", "last_fetch_success": "13395077628022358"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://www.google.com:443,*": {"last_modified": "13395077678866437", "setting": {"client_hints": [4, 5, 9, 10, 11, 13, 14, 15, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:52407,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.google.com:443,*": {"expiration": "13402855298434458", "last_modified": "13395079298434512", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13395077675384490", "setting": {"lastEngagementTime": 1.3395077675384484e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:52407,*": {"last_modified": "13395077698512895", "setting": {"lastEngagementTime": 1.3395077698512878e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.999999999999999, "rawScore": 5.999999999999999}}, "https://www.google.com:443,*": {"last_modified": "13395077677104777", "setting": {"lastEngagementTime": 1.3395077677104772e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "137.0.7151.120", "creation_time": "13395077617830106", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13395077698512878", "last_time_obsolete_http_credentials_removed": 1750604077.895153, "last_time_password_store_metrics_reported": 1750604047.888452, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {"0": {"12": ["13395077690"]}}, "hash_real_time_ohttp_expiration_time": "13395336818437801", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13395077617", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "savefile": {"default_directory": "C:\\Users\\<USER>\\Downloads"}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ19nm24jY5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEJra5tuI2OUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13394937599000000", "uma_in_sql_start_time": "13395077617887826"}, "selectfile": {"last_directory": "C:\\Users\\<USER>\\Downloads"}, "sessions": {"event_log": [{"crashed": false, "time": "13395077617883945", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13395079298366750", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"brent crude oil prices\",\"pcc vs brn scorecard\",\"mahindra bolero 2025\",\"bff video\",\"oneplus nord ce5 price\",\"karnataka pgcet mca question paper 2025\",\"sanju samson ipl 2026\",\"tommy genesis\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChoIkk4SFQoRVHJlbmRpbmcgc2VhcmNoZXMoCg\\u003d\\u003d\",\"google:suggestdetail\":[{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"Cg0vZy8xMWc2eGg1cHIwEhlDYW5hZGlhbiByYXBwZXIgYW5kIG1vZGVsMqsJZGF0YTppbWFnZS9qcGVnO2Jhc2U2NCwvOWovNEFBUVNrWkpSZ0FCQVFBQUFRQUJBQUQvMndDRUFBa0dCd2dIQmdrSUJ3Z0tDZ2tMRFJZUERRd01EUnNVRlJBV0lCMGlJaUFkSHg4a0tEUXNKQ1l4Sng4ZkxUMHRNVFUzT2pvNkl5cy9SRDg0UXpRNU9qY0JDZ29LRFF3TkdnOFBHamNsSHlVM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOLy9BQUJFSUFFQUFRQU1CRVFBQ0VRRURFUUgveEFBYUFBQUNBd0VCQUFBQUFBQUFBQUFBQUFBQkFnTUVCUVlBLzhRQUtCQUFBZ0VEQWdVRkFBTUFBQUFBQUFBQUFRSUFBd1FSSVRFU0V5SkJVUVZoY1lHUkl6SkMvOFFBR2dFQUF3QURBUUFBQUFBQUFBQUFBQUFBQUFFQ0F3UUZCdi9FQUI0UkFRRUFBZ01CQVFFQkFBQUFBQUFBQUFBQkFoRURJVEVFUVNJUy85b0FEQU1CQUFJUkF4RUFQd0RsSEd2eE9wSG1NcVZ4MVpIZldFVGZScEVwVUJHKzM3SFp1REhMVlI0ajBRWWdZRVJBcEVEZ1JLWEdHc0luTDBoR2dsSXZqeWpxWDVoUlBTa1JnQ0lIc3NEQWlJRk1TbDgvRVFxTmhwS2lNalc2R3BjVXFhN3M0QS9ZWmRRc0p2S1JFWXlMQUFSQXltQmxNU21nUnJKT28yR2tjOVJmQ2p5TzBwRUxnbllaaGJKNmNsdDFBeUNJQXBnWUdCd2hpTm90SlhTT01JRDVNYzlSbDRqUDljZmNwai9FM3FqMnRHeFJhREVPdU9ZL2ttY2pQbHZKbmR2UjhIejQ4WEhKUFdIVHVWUzlWVUpDVk54MjlzVGE0TTdOUm8vWHdUVnNhSm02NVpUQTRVd1UwRndkNWpaSHE1WHBBVURoWEcrY251WThVNTZWOXpMWVZmMUttb1IxcUlyY1RGMVBGT0QvQUsxbmRQVzRUZUUyeUdyY0o2UjBaSDFObml2YlgrakgrSzI1MVhteW1CZ1lqYUZNSnkzWjJPUmdCUU4vT3NpNzMweTQyYXRxS295c2VnbkIyenBLblRIbFpmRlc2ckMzb1BVTlZhWjJWbUdkZllURDlHZXNOVDliSHc4WCt1VGRuVWNxOTVjdTVMczFVSFhmT2swTGppNzB0QzRLS3ljc25xSTNPMGVDYzV2cHRVdlVPVFZGRzQ2aG9CVThlTXpkNCtiOHJqOHZ5N204ZlduZzRteTBBNFlIdGNyY0NxZ1IrSWxNdGpzZkg1aVJGNWFrNlVibTVwMjY4VlE5czRHNWhsbk1mUng4T2ZKZjVjejZyNmlMMm9QNGdxTG9EL3FhT2VWeXkyN25CeFRqd21NUUpRZW1FdWFUcFVwY1lRa0hEQWtiRmQvdmIzbU96Y2JFeU5UVG4zalVxdmc0UGd3blVUbmRKYm1wenJxbVVZa0hBeDRPZHBlREZkVEcxMWcxRTZUem9nYXhVNUgvMlE9PToNVG9tbXkgR2VuZXNpc0oHIzAzNjc3YlI9Z3Nfc3NwPWVKemo0dFZQMXpjMFREZXJ5REF0S0RJd1lQVGlMY25QemExVVNFX05TeTNPTEFZQWpFd0p6d3ACcAY\\u003d\",\"zl\":10002}],\"google:suggesteventid\":\"-1949045318098414264\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\"]}]", "cachedresults_with_url": {"https://www.google.com/search?sca_esv=880447038e84d9f5&q=cardboard&udm=2&fbs=AIIjpHxU7SXXniUZfeShr2fp4giZ1Y6MJ25_tmWITc7uy4KIeioyp3OhN11EY0n5qfq-zEMZldv_eRjZ2XLYc5GnVnME7glWodDcaQwvGYJtospyF4hao4VocMoniUVvlzzwRcCLYvfsjPeiuln4CZ9mxRNzMJl71rp-WEM_b89yTboF0wE9FSbwRYrSVuB6YHexvLk_d7r_74zDurdYUrhSbdL5TbQGiA&sa=X&ved=2ahUKEwiBx960pIWOAxWjs1YBHTpwNZoQtKgLKAF6BAgOEAE&biw=1707&bih=825&dpr=1.5": ")]}'\n[\"\",[\"cardboard sheet\",\"cardboard box\",\"cardboard paper\",\"cardboard price\",\"cardboard craft\",\"cardboard for project\",\"cardboard for exam\",\"paper board\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChcIwLgCEhEKD1JlY2VudCBzZWFyY2hlcwohCJBOEhwKGlJlbGF0ZWQgdG8gcmVjZW50IHNlYXJjaGVz\",\"google:suggestdetail\":[{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000}],\"google:suggesteventid\":\"1587437649830766601\",\"google:suggestrelevance\":[601,600,555,554,553,552,551,550],\"google:suggestsubtypes\":[[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"],\"google:verbatimrelevance\":851}]"}}}