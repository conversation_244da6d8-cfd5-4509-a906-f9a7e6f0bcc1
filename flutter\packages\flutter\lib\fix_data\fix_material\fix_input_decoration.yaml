# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the flutter/packages/flutter/test_fixes/README.md
# file for instructions on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for InputDecoration and InputDecorationTheme from the Material library. *
version: 1
transforms:
  # Changes made in https://github.com/flutter/flutter/pull/161235.
  - title: "Migrate to 'maintainHintSize'"
    date: 2025-01-24
    element:
      uris: [ 'material.dart' ]
      constructor: ''
      inClass: 'InputDecoration'
    changes:
      - kind: 'renameParameter'
        oldName: 'maintainHintHeight'
        newName: 'maintainHintSize'

  # Changes made in https://github.com/flutter/flutter/pull/161235.
  - title: "Migrate to 'maintainHintSize'"
    date: 2025-01-24
    element:
      uris: [ 'material.dart' ]
      constructor: 'collapsed'
      inClass: 'InputDecoration'
    changes:
      - kind: 'renameParameter'
        oldName: 'maintainHintHeight'
        newName: 'maintainHintSize'

  # Changes made in https://github.com/flutter/flutter/pull/161235.
  - title: "Migrate to 'maintainHintSize'"
    date: 2025-01-24
    element:
      uris: [ 'material.dart' ]
      field: 'maintainHintHeight'
      inClass: 'InputDecoration'
    changes:
      - kind: 'rename'
        newName: 'maintainHintSize'

  # Changes made in https://github.com/flutter/flutter/pull/152486.
  - title: "Remove invalid parameter"
    date: 2024-07-27
    element:
      uris: [ 'material.dart' ]
      constructor: 'collapsed'
      inClass: 'InputDecoration'
    changes:
      - kind: 'removeParameter'
        name: 'floatingLabelBehavior'

  # Changes made in https://github.com/flutter/flutter/pull/152486.
  - title: "Remove invalid parameter"
    date: 2024-07-27
    element:
      uris: [ 'material.dart' ]
      constructor: 'collapsed'
      inClass: 'InputDecoration'
    changes:
      - kind: 'removeParameter'
        name: 'floatingLabelAlignment'

 # Changes made in https://github.com/flutter/flutter/pull/46115
  - title: "Migrate to 'floatingLabelBehavior'"
    date: 2020-01-15
    element:
      uris: [ 'material.dart' ]
      field: 'hasFloatingPlaceholder'
      inClass: 'InputDecorationTheme'
    changes:
      - kind: 'rename'
        newName: 'floatingLabelBehavior'

  # Changes made in https://github.com/flutter/flutter/pull/46115
  - title: "Migrate to 'floatingLabelBehavior'"
    date: 2020-01-15
    element:
      uris: [ 'material.dart' ]
      constructor: ''
      inClass: 'InputDecorationTheme'
    oneOf:
      - if: "hasFloatingPlaceholder == 'true'"
        changes:
          - kind: 'addParameter'
            index: 14
            name: 'floatingLabelBehavior'
            style: optional_named
            argumentValue:
              expression: '{% FloatingLabelBehavior %}.auto'
              requiredIf: "hasFloatingPlaceholder == 'true'"
              variables:
                FloatingLabelBehavior:
                  kind: 'import'
                  uris: [ 'material.dart' ]
                  name: 'FloatingLabelBehavior'
          - kind: 'removeParameter'
            name: 'hasFloatingPlaceholder'
      - if: "hasFloatingPlaceholder == 'false'"
        changes:
          - kind: 'addParameter'
            index: 14
            name: 'floatingLabelBehavior'
            style: optional_named
            argumentValue:
              expression: '{% FloatingLabelBehavior %}.never'
              requiredIf: "hasFloatingPlaceholder == 'false'"
              variables:
                FloatingLabelBehavior:
                  kind: 'import'
                  uris: [ 'material.dart' ]
                  name: 'FloatingLabelBehavior'
          - kind: 'removeParameter'
            name: 'hasFloatingPlaceholder'
    variables:
      hasFloatingPlaceholder:
        kind: 'fragment'
        value: 'arguments[hasFloatingPlaceholder]'

  # Changes made in https://github.com/flutter/flutter/pull/46115
  - title: "Migrate to 'floatingLabelBehavior'"
    date: 2020-01-15
    element:
      uris: [ 'material.dart' ]
      method: 'copyWith'
      inClass: 'InputDecorationTheme'
    oneOf:
      - if: "hasFloatingPlaceholder == 'true'"
        changes:
          - kind: 'addParameter'
            index: 14
            name: 'floatingLabelBehavior'
            style: optional_named
            argumentValue:
              expression: '{% FloatingLabelBehavior %}.auto'
              requiredIf: "hasFloatingPlaceholder == 'true'"
              variables:
                FloatingLabelBehavior:
                  kind: 'import'
                  uris: [ 'material.dart' ]
                  name: 'FloatingLabelBehavior'
          - kind: 'removeParameter'
            name: 'hasFloatingPlaceholder'
      - if: "hasFloatingPlaceholder == 'false'"
        changes:
          - kind: 'addParameter'
            index: 14
            name: 'floatingLabelBehavior'
            style: optional_named
            argumentValue:
              expression: '{% FloatingLabelBehavior %}.never'
              requiredIf: "hasFloatingPlaceholder == 'false'"
              variables:
                FloatingLabelBehavior:
                  kind: 'import'
                  uris: [ 'material.dart' ]
                  name: 'FloatingLabelBehavior'
          - kind: 'removeParameter'
            name: 'hasFloatingPlaceholder'
    variables:
      hasFloatingPlaceholder:
        kind: 'fragment'
        value: 'arguments[hasFloatingPlaceholder]'

  # Changes made in https://github.com/flutter/flutter/pull/46115
  - title: "Migrate to 'floatingLabelBehavior'"
    date: 2020-01-15
    element:
      uris: [ 'material.dart' ]
      field: 'hasFloatingPlaceholder'
      inClass: 'InputDecoration'
    changes:
      - kind: 'rename'
        newName: 'floatingLabelBehavior'

  # Changes made in https://github.com/flutter/flutter/pull/46115
  - title: "Rename to 'floatingLabelBehavior'"
    date: 2020-01-15
    element:
      uris: [ 'material.dart' ]
      constructor: 'collapsed'
      inClass: 'InputDecoration'
    oneOf:
      - if: "hasFloatingPlaceholder == 'true'"
        changes:
          - kind: 'addParameter'
            index: 14
            name: 'floatingLabelBehavior'
            style: optional_named
            argumentValue:
              expression: '{% FloatingLabelBehavior %}.auto'
              requiredIf: "hasFloatingPlaceholder == 'true'"
              variables:
                FloatingLabelBehavior:
                  kind: 'import'
                  uris: [ 'material.dart' ]
                  name: 'FloatingLabelBehavior'
          - kind: 'removeParameter'
            name: 'hasFloatingPlaceholder'
      - if: "hasFloatingPlaceholder == 'false'"
        changes:
          - kind: 'addParameter'
            index: 14
            name: 'floatingLabelBehavior'
            style: optional_named
            argumentValue:
              expression: '{% FloatingLabelBehavior %}.never'
              requiredIf: "hasFloatingPlaceholder == 'false'"
              variables:
                FloatingLabelBehavior:
                  kind: 'import'
                  uris: [ 'material.dart' ]
                  name: 'FloatingLabelBehavior'
          - kind: 'removeParameter'
            name: 'hasFloatingPlaceholder'
    variables:
      hasFloatingPlaceholder:
        kind: 'fragment'
        value: 'arguments[hasFloatingPlaceholder]'

  # Changes made in https://github.com/flutter/flutter/pull/46115
  - title: "Rename to 'floatingLabelBehavior'"
    date: 2020-01-15
    element:
      uris: [ 'material.dart' ]
      constructor: ''
      inClass: 'InputDecoration'
    oneOf:
      - if: "hasFloatingPlaceholder == 'true'"
        changes:
          - kind: 'addParameter'
            index: 14
            name: 'floatingLabelBehavior'
            style: optional_named
            argumentValue:
              expression: '{% FloatingLabelBehavior %}.auto'
              requiredIf: "hasFloatingPlaceholder == 'true'"
              variables:
                FloatingLabelBehavior:
                  kind: 'import'
                  uris: [ 'material.dart' ]
                  name: 'FloatingLabelBehavior'
          - kind: 'removeParameter'
            name: 'hasFloatingPlaceholder'
      - if: "hasFloatingPlaceholder == 'false'"
        changes:
          - kind: 'addParameter'
            index: 14
            name: 'floatingLabelBehavior'
            style: optional_named
            argumentValue:
              expression: '{% FloatingLabelBehavior %}.never'
              requiredIf: "hasFloatingPlaceholder == 'false'"
              variables:
                FloatingLabelBehavior:
                  kind: 'import'
                  uris: [ 'material.dart' ]
                  name: 'FloatingLabelBehavior'
          - kind: 'removeParameter'
            name: 'hasFloatingPlaceholder'
    variables:
      hasFloatingPlaceholder:
        kind: 'fragment'
        value: 'arguments[hasFloatingPlaceholder]'

# Before adding a new fix: read instructions at the top of this file.
