// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef LIB_TONIC_TYPED_DATA_UINT16_LIST_H_
#define LIB_TONIC_TYPED_DATA_UINT16_LIST_H_

#warning uint16_list.h is deprecated; use typed_list.h instead.

#include "tonic/typed_data/typed_list.h"

#endif  // LIB_TONIC_TYPED_DATA_UINT16_LIST_H_
