// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

layout(local_size_x_id = 0) in;
layout(std430) buffer;

#include <impeller/prefix_sum.glsl>

#define BLOCK_SIZE 1024

layout(binding = 0) readonly buffer InputData {
  uint count;
  uint data[];
}
input_data;

layout(binding = 1) writeonly buffer OutputData {
  uint data[];
}
output_data;

// Needs to be number of threads per threadgroup.
shared uint memory[BLOCK_SIZE];

void main() {
  uint ident = gl_GlobalInvocationID.x;

  uint value = 0;
  if (ident < input_data.count) {
    value = input_data.data[ident];
  }

  if (ident < BLOCK_SIZE) {
    memory[ident] = value;
  }

  barrier();

  ExclusivePrefixSum(ident, memory, BLOCK_SIZE);

  if (ident < input_data.count) {
    // Convert exclusive to inclusive sum.
    output_data.data[ident] = memory[ident] + value;
  }
}
