# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/flutter_export_environment.sh
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Flutter specific
flutter_project_template/build/
flutter_project_template/.flutter-plugins
flutter_project_template/.flutter-plugins-dependencies
flutter_project_template/.dart_tool/
flutter_project_template/.packages
flutter_project_template/pubspec.lock

# iOS specific
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
**/xcuserdata
.DS_Store
**/.DS_Store
UpCyclization.xcodeproj/xcuserdata/
UpCyclization.xcodeproj/project.xcworkspace/xcuserdata/

# Xcode
*.xcworkspace
!default.xcworkspace
xcuserdata/
build/
DerivedData/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3

# Xcode Patch
*.xcworkspace/
!default.xcworkspace/

# CocoaPods
Pods/
*.xcworkspace

# Carthage
Carthage/Build/

# Swift Package Manager
.swiftpm/
.build/

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# Firebase
GoogleService-Info.plist
google-services.json
firebase_options.dart

# Environment files
.env
.env.local
.env.production
.env.staging

# API Keys and secrets
**/secrets/
**/config/secrets.dart
**/lib/config/api_keys.dart

# Machine Learning Models (if large)
*.tflite
*.mlmodel
**/*.h5
**/*.pb

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Temporary files
*.tmp
*.temp
temp/
tmp/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Windows specific
*.cab
*.msi
*.msix
*.msm
*.msp

# Node.js (if using for any tooling)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Archives
*.zip
*.tar.gz
*.rar
*.7z

# IDE files
*.swp
*.swo
*~

# Generated files
*.generated.dart
*.g.dart
*.freezed.dart
*.gr.dart

# Test related
test/coverage/
coverage/
*.coverage

# Documentation
doc/api/

# Local storage
*.sqlite
*.sqlite3
*.db
