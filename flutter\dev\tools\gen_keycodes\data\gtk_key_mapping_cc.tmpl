// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "key_mapping.h"

#include <glib.h>
#include <map>

#include "flutter/shell/platform/linux/fl_key_embedder_responder_private.h"

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by
// flutter/flutter@dev/tools/gen_keycodes/bin/gen_keycodes.dart and should not
// be edited directly.
//
// Edit the template dev/tools/gen_keycodes/data/gtk_key_mapping_cc.tmpl
// instead. See dev/tools/gen_keycodes/README.md for more information.

std::map<uint64_t, uint64_t> xkb_to_physical_key_map = {
@@@XKB_SCAN_CODE_MAP@@@
};

std::map<uint64_t, uint64_t> gtk_keyval_to_logical_key_map = {
@@@GTK_KEYVAL_CODE_MAP@@@
};

void initialize_modifier_bit_to_checked_keys(GHashTable* table) {
  FlKeyEmbedderCheckedKey* data;
@@@GTK_MODIFIER_BIT_MAP@@@
}

void initialize_lock_bit_to_checked_keys(GHashTable* table) {
  FlKeyEmbedderCheckedKey* data;
@@@GTK_MODE_BIT_MAP@@@
}

const std::vector<LayoutGoal> layout_goals = {
@@@LAYOUT_GOALS@@@
};

@@@MASK_CONSTANTS@@@
