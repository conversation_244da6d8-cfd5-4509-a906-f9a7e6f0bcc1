## Use reliable links

If you would like to link to a website,
do your best to link to a reliable link destination,
sometimes known as a permalink.

Even if you update the link in the repo if it changes or breaks,
outdated versions might remain in older versions of tools, docs, etc.
Sometimes the link destination itself can add an appropriate redirect,
but sometimes it can't and other times it's destination is not the best choice.

- If you're not sure if you need a more reliable link,
  check out [Situations to consider](#situations-to-consider).
- If your destination does not already have a stable link,
  and you think it would benefit from one,
  follow the instructions in [Create a reliable link](#create-a-reliable-link).

### Situations to consider

There are many linking situations that could benefit from a more
stable/reliable redirect with a memorable URL.
For reference, the following are a few situations where
they can have a high impact:

- Output from CLI tools like `flutter` and `dart`
- Comments or prose in files generated by tools like `flutter create`
- API docs in the framework or other bundled packages
- Error messages and other diagnostics from the engine, framework, or
  other Flutter-team maintained packages
- Troubleshooting links from DevTools and editors

> [!TIP]
> Links that would lead to fragments on the Dart and Flutter sites
> should often be replaced with a custom reliable link.
> Browsers don't send URI fragments to the server,
> so redirecting them needs extra client-side handling.


### Create a reliable link

If you need a reliable link, consider
adding a tooling redirect to the Flutter documentation site,
and using that in place of the intended destination.
Tooling redirects are added in a similar fashion to design-doc go links,
and the website team will do their best to keep it functional.
Once created, a tooling redirect is accessed through a link like
starting with `/to/`, such as `flutter.dev/to/gesture-disambiguation`.

Before creating a new tooling link,
verify that an appropriate one doesn't exist already.
To see what redirects exist already, check the `/to/` entries in
[`flutter/website/firebase.json`][flutter-redirects] and
[`dart-lang/site-www/firebase.json`][dart-redirects].

If an appropriate tooling redirect doesn't exist already,
create one following these steps:

 1. Open the [`firebase.json` file][flutter-redirects] in the
    `flutter/website` repository and edit it using the GitHub UI or locally.
 2. Determine a short, relevant, and memorable name for the redirect.
    No special characters are allowed and under 5 words is preferred.
    Remember users might need to type it manually, or it might be output
    in a crowded terminal or UI.
 3. Convert the name to a `lowercase-with-dashes` format and fill out
    a redirect entry in the following format with it and the destination URL:

    ```json
    { "source": "/to/<redirect-name>", "destination": "<url>", "type": 301 }
    ```

    Note that if you are linking to the Flutter docs site itself,
    don't include the origin of the destination URL, just the path.
 4. Place this entry with the other tooling (`/to/`) redirects in the file
    according to its alphanumerical sorting.
    Certain editors can sort a selected portion of the redirects as well.
 5. Commit your changes, push them if needed, and open a new pull request
    against the `flutter/website` repository's `main` branch.
 6. Set a descriptive pull request title, similar to the following:

    ```markdown
    Add a `/to/<redirect-name>` tooling redirect
    ```
 7. In the pull request description, include a link to the PR, CL, or
    wherever else that will make use of the redirect, as well as
    what project team can be reached out to if it needs to be updated.
 8. Unless a website maintainer has feedback about the
    redirect name, usefulness, destination, or formatting,
    they will approve and land the change for you.
 9. Once your tooling redirect has landed,
    switch to using the tooling redirect wherever needed.
10. The website maintainers might reach out to you
    if the redirect needs to be updated, but do consider
    checking your redirects occasionally to verify they still make sense.

> [!IMPORTANT]
> There is a maintenance cost of a tooling redirect,
> both for your team and the website maintainers.
>
> So if the link is being surfaced in a way or location
> that users will quickly see an updated version,
> a tooling redirect might not be necessary.
> The same goes for links that are only needed for a short time.

[flutter-redirects]: https://github.com/flutter/website/blob/main/firebase.json
[dart-redirects]: https://github.com/dart-lang/site-www/blob/main/firebase.json
