{"_comment": "THIS IS A GENERATED FILE. Do not edit this file directly.", "_comment2": "See `generate_builder_json.dart` for the generator code", "builds": [{"name": "web_tests/test_bundles/dart2js-canvaskit-engine", "drone_dimensions": ["device_type=none", "os=Linux"], "generators": {"tasks": [{"name": "compile bundle dart2js-canvaskit-engine", "parameters": ["test", "--compile", "--bundle=dart2js-canvaskit-engine"], "scripts": ["flutter/lib/web_ui/dev/felt"]}]}}, {"name": "web_tests/test_bundles/dart2js-canvaskit-canvaskit", "drone_dimensions": ["device_type=none", "os=Linux"], "generators": {"tasks": [{"name": "compile bundle dart2js-canvaskit-canvaskit", "parameters": ["test", "--compile", "--bundle=dart2js-canvaskit-canvaskit"], "scripts": ["flutter/lib/web_ui/dev/felt"]}]}}, {"name": "web_tests/test_bundles/dart2js-canvaskit-ui", "drone_dimensions": ["device_type=none", "os=Linux"], "generators": {"tasks": [{"name": "compile bundle dart2js-canvaskit-ui", "parameters": ["test", "--compile", "--bundle=dart2js-canvaskit-ui"], "scripts": ["flutter/lib/web_ui/dev/felt"]}]}}, {"name": "web_tests/test_bundles/dart2wasm-canvaskit-engine", "drone_dimensions": ["device_type=none", "os=Linux"], "generators": {"tasks": [{"name": "compile bundle dart2wasm-canvaskit-engine", "parameters": ["test", "--compile", "--bundle=dart2wasm-canvaskit-engine"], "scripts": ["flutter/lib/web_ui/dev/felt"]}]}}, {"name": "web_tests/test_bundles/dart2wasm-skwasm-ui", "drone_dimensions": ["device_type=none", "os=Linux"], "generators": {"tasks": [{"name": "compile bundle dart2wasm-skwasm-ui", "parameters": ["test", "--compile", "--bundle=dart2wasm-skwasm-ui"], "scripts": ["flutter/lib/web_ui/dev/felt"]}]}}, {"name": "web_tests/test_bundles/fallbacks", "drone_dimensions": ["device_type=none", "os=Linux"], "generators": {"tasks": [{"name": "compile bundle fallbacks", "parameters": ["test", "--compile", "--bundle=fallbacks"], "scripts": ["flutter/lib/web_ui/dev/felt"]}]}}], "tests": [{"name": "web engine analysis and license checks", "recipe": "engine_v2/tester_engine", "drone_dimensions": ["device_type=none", "os=Linux"], "tasks": [{"name": "check licenses", "parameters": ["check-licenses"], "script": "flutter/lib/web_ui/dev/felt"}]}, {"name": "Linux run chrome suites", "recipe": "engine_v2/tester_engine", "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"download_android_deps": false, "download_jdk": false}, "dependencies": ["web_tests/test_bundles/dart2js-canvaskit-engine", "web_tests/test_bundles/dart2js-canvaskit-canvaskit", "web_tests/test_bundles/dart2js-canvaskit-ui", "web_tests/test_bundles/dart2wasm-canvaskit-engine", "web_tests/test_bundles/dart2wasm-skwasm-ui", "web_tests/test_bundles/fallbacks"], "test_dependencies": [{"dependency": "goldctl", "version": "git_revision:720a542f6fe4f92922c3b8f0fdcc4d2ac6bb83cd"}, {"dependency": "chrome_and_driver", "version": "125.0.6422.141"}], "tasks": [{"name": "copy artifacts", "parameters": ["test", "--copy-artifacts", "--suite=chrome-dart2js-canvaskit-engine", "--suite=chrome-dart2js-canvaskit-canvaskit", "--suite=chrome-dart2js-canvaskit-ui", "--suite=chrome-full-dart2js-canvaskit-canvaskit", "--suite=chrome-full-dart2js-canvaskit-ui", "--suite=edge-dart2js-canvaskit-engine", "--suite=edge-dart2js-canvaskit-canvaskit", "--suite=edge-dart2js-canvaskit-ui", "--suite=edge-full-dart2js-canvaskit-canvaskit", "--suite=edge-full-dart2js-canvaskit-ui", "--suite=firefox-dart2js-canvaskit-engine", "--suite=firefox-dart2js-canvaskit-canvaskit", "--suite=firefox-dart2js-canvaskit-ui", "--suite=safari-dart2js-canvaskit-engine", "--suite=safari-dart2js-canvaskit-canvaskit", "--suite=safari-dart2js-canvaskit-ui", "--suite=chrome-dart2wasm-canvaskit-engine", "--suite=chrome-coi-dart2wasm-skwasm-ui", "--suite=chrome-force-st-dart2wasm-skwasm-ui", "--suite=chrome-fallbacks", "--suite=chrome-coi-fallbacks", "--suite=chrome-force-st-fallbacks", "--suite=firefox-fallbacks", "--suite=safari-fallbacks"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-dart2js-canvaskit-engine", "parameters": ["test", "--run", "--suite=chrome-dart2js-canvaskit-engine"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-dart2js-canvaskit-canvaskit", "parameters": ["test", "--run", "--suite=chrome-dart2js-canvaskit-canvaskit"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-dart2js-canvaskit-ui", "parameters": ["test", "--run", "--suite=chrome-dart2js-canvaskit-ui"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-full-dart2js-canvaskit-canvaskit", "parameters": ["test", "--run", "--suite=chrome-full-dart2js-canvaskit-canvaskit"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-full-dart2js-canvaskit-ui", "parameters": ["test", "--run", "--suite=chrome-full-dart2js-canvaskit-ui"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-dart2wasm-canvaskit-engine", "parameters": ["test", "--run", "--suite=chrome-dart2wasm-canvaskit-engine"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-coi-dart2wasm-skwasm-ui", "parameters": ["test", "--run", "--suite=chrome-coi-dart2wasm-skwasm-ui"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-force-st-dart2wasm-skwasm-ui", "parameters": ["test", "--run", "--suite=chrome-force-st-dart2wasm-skwasm-ui"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-fallbacks", "parameters": ["test", "--run", "--suite=chrome-fallbacks"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-coi-fallbacks", "parameters": ["test", "--run", "--suite=chrome-coi-fallbacks"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite chrome-force-st-fallbacks", "parameters": ["test", "--run", "--suite=chrome-force-st-fallbacks"], "script": "flutter/lib/web_ui/dev/felt"}]}, {"name": "Linux run firefox suites", "recipe": "engine_v2/tester_engine", "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"download_android_deps": false, "download_jdk": false}, "dependencies": ["web_tests/test_bundles/dart2js-canvaskit-engine", "web_tests/test_bundles/dart2js-canvaskit-canvaskit", "web_tests/test_bundles/dart2js-canvaskit-ui", "web_tests/test_bundles/fallbacks"], "test_dependencies": [{"dependency": "goldctl", "version": "git_revision:720a542f6fe4f92922c3b8f0fdcc4d2ac6bb83cd"}, {"dependency": "firefox", "version": "version:132.0"}], "tasks": [{"name": "copy artifacts", "parameters": ["test", "--copy-artifacts", "--suite=chrome-dart2js-canvaskit-engine", "--suite=chrome-dart2js-canvaskit-canvaskit", "--suite=chrome-dart2js-canvaskit-ui", "--suite=chrome-full-dart2js-canvaskit-canvaskit", "--suite=chrome-full-dart2js-canvaskit-ui", "--suite=edge-dart2js-canvaskit-engine", "--suite=edge-dart2js-canvaskit-canvaskit", "--suite=edge-dart2js-canvaskit-ui", "--suite=edge-full-dart2js-canvaskit-canvaskit", "--suite=edge-full-dart2js-canvaskit-ui", "--suite=firefox-dart2js-canvaskit-engine", "--suite=firefox-dart2js-canvaskit-canvaskit", "--suite=firefox-dart2js-canvaskit-ui", "--suite=safari-dart2js-canvaskit-engine", "--suite=safari-dart2js-canvaskit-canvaskit", "--suite=safari-dart2js-canvaskit-ui", "--suite=chrome-dart2wasm-canvaskit-engine", "--suite=chrome-coi-dart2wasm-skwasm-ui", "--suite=chrome-force-st-dart2wasm-skwasm-ui", "--suite=chrome-fallbacks", "--suite=chrome-coi-fallbacks", "--suite=chrome-force-st-fallbacks", "--suite=firefox-fallbacks", "--suite=safari-fallbacks"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite firefox-dart2js-canvaskit-engine", "parameters": ["test", "--run", "--suite=firefox-dart2js-canvaskit-engine"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite firefox-dart2js-canvaskit-canvaskit", "parameters": ["test", "--run", "--suite=firefox-dart2js-canvaskit-canvaskit"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite firefox-dart2js-canvaskit-ui", "parameters": ["test", "--run", "--suite=firefox-dart2js-canvaskit-ui"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite firefox-fallbacks", "parameters": ["test", "--run", "--suite=firefox-fallbacks"], "script": "flutter/lib/web_ui/dev/felt"}]}, {"name": "Mac run safari suites", "recipe": "engine_v2/tester_engine", "drone_dimensions": ["device_type=none", "os=Mac-14", "cpu=arm64"], "gclient_variables": {"download_android_deps": false, "download_jdk": false}, "dependencies": ["web_tests/test_bundles/dart2js-canvaskit-engine", "web_tests/test_bundles/dart2js-canvaskit-canvaskit", "web_tests/test_bundles/dart2js-canvaskit-ui", "web_tests/test_bundles/fallbacks"], "test_dependencies": [{"dependency": "goldctl", "version": "git_revision:720a542f6fe4f92922c3b8f0fdcc4d2ac6bb83cd"}], "tasks": [{"name": "copy artifacts", "parameters": ["test", "--copy-artifacts", "--suite=chrome-dart2js-canvaskit-engine", "--suite=chrome-dart2js-canvaskit-canvaskit", "--suite=chrome-dart2js-canvaskit-ui", "--suite=chrome-full-dart2js-canvaskit-canvaskit", "--suite=chrome-full-dart2js-canvaskit-ui", "--suite=edge-dart2js-canvaskit-engine", "--suite=edge-dart2js-canvaskit-canvaskit", "--suite=edge-dart2js-canvaskit-ui", "--suite=edge-full-dart2js-canvaskit-canvaskit", "--suite=edge-full-dart2js-canvaskit-ui", "--suite=firefox-dart2js-canvaskit-engine", "--suite=firefox-dart2js-canvaskit-canvaskit", "--suite=firefox-dart2js-canvaskit-ui", "--suite=safari-dart2js-canvaskit-engine", "--suite=safari-dart2js-canvaskit-canvaskit", "--suite=safari-dart2js-canvaskit-ui", "--suite=chrome-dart2wasm-canvaskit-engine", "--suite=chrome-coi-dart2wasm-skwasm-ui", "--suite=chrome-force-st-dart2wasm-skwasm-ui", "--suite=chrome-fallbacks", "--suite=chrome-coi-fallbacks", "--suite=chrome-force-st-fallbacks", "--suite=firefox-fallbacks", "--suite=safari-fallbacks"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite safari-dart2js-canvaskit-engine", "parameters": ["test", "--run", "--suite=safari-dart2js-canvaskit-engine"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite safari-dart2js-canvaskit-canvaskit", "parameters": ["test", "--run", "--suite=safari-dart2js-canvaskit-canvaskit"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite safari-dart2js-canvaskit-ui", "parameters": ["test", "--run", "--suite=safari-dart2js-canvaskit-ui"], "script": "flutter/lib/web_ui/dev/felt"}, {"name": "run suite safari-fallbacks", "parameters": ["test", "--run", "--suite=safari-fallbacks"], "script": "flutter/lib/web_ui/dev/felt"}]}]}