<!-- Copyright 2014 The Flutter Authors. All rights reserved.
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file. -->

<resources>
  <string name="app_name">Platform View</string>
  <string name="title">Flutter Application</string>
  <string name="button_tap"><PERSON><PERSON> tapped 0 times. </string>
  <string name="switch_view">"Continue in Flutter view"</string>
  <string name="android">Android</string>
</resources>
