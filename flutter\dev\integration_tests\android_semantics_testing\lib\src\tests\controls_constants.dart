// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// The name of the route containing the test suite.
const String selectionControlsRoute = 'controls';

/// The string supplied to the [ValueKey] for the enabled checkbox.
const String checkboxKeyValue = 'SelectionControls#Checkbox1';

/// The string supplied to the [ValueKey] for the disabled checkbox.
const String disabledCheckboxKeyValue = 'SelectionControls#Checkbox2';

/// The string supplied to the [ValueKey] for the radio button with value 1.
const String radio1KeyValue = 'SelectionControls#Radio1';

/// The string supplied to the [ValueKey] for the radio button with value 2.
const String radio2KeyValue = 'SelectionControls#Radio2';

/// The string supplied to the [ValueKey] for the radio button with value 3.
const String radio3KeyValue = 'SelectionControls#Radio3';

/// The string supplied to the [ValueKey] for the switch.
const String switchKeyValue = 'SelectionControls#Switch1';

/// The string supplied to the [ValueKey] for the labeled switch.
const String labeledSwitchKeyValue = 'SelectionControls#Switch2';

/// The label of the labeled switch.
const String switchLabel = 'Label';
