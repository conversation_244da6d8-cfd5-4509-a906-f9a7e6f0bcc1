{"version": "6_1_0", "md.comp.checkbox.container.size": 18.0, "md.comp.checkbox.error.focus.state-layer.color": "error", "md.comp.checkbox.error.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.checkbox.error.hover.state-layer.color": "error", "md.comp.checkbox.error.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.checkbox.error.pressed.state-layer.color": "error", "md.comp.checkbox.error.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.checkbox.focus.indicator.color": "secondary", "md.comp.checkbox.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.checkbox.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.checkbox.icon.size": 18.0, "md.comp.checkbox.selected.container.color": "primary", "md.comp.checkbox.selected.disabled.container.color": "onSurface", "md.comp.checkbox.selected.disabled.container.opacity": 0.38, "md.comp.checkbox.selected.disabled.container.outline.width": 0.0, "md.comp.checkbox.selected.disabled.icon.color": "surface", "md.comp.checkbox.selected.error.container.color": "error", "md.comp.checkbox.selected.error.focus.container.color": "error", "md.comp.checkbox.selected.error.focus.icon.color": "onError", "md.comp.checkbox.selected.error.hover.container.color": "error", "md.comp.checkbox.selected.error.hover.icon.color": "onError", "md.comp.checkbox.selected.error.icon.color": "onError", "md.comp.checkbox.selected.error.pressed.container.color": "error", "md.comp.checkbox.selected.error.pressed.icon.color": "onError", "md.comp.checkbox.selected.focus.container.color": "primary", "md.comp.checkbox.selected.focus.icon.color": "onPrimary", "md.comp.checkbox.selected.focus.outline.width": 0.0, "md.comp.checkbox.selected.focus.state-layer.color": "primary", "md.comp.checkbox.selected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.checkbox.selected.hover.container.color": "primary", "md.comp.checkbox.selected.hover.icon.color": "onPrimary", "md.comp.checkbox.selected.hover.outline.width": 0.0, "md.comp.checkbox.selected.hover.state-layer.color": "primary", "md.comp.checkbox.selected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.checkbox.selected.icon.color": "onPrimary", "md.comp.checkbox.selected.outline.width": 0.0, "md.comp.checkbox.selected.pressed.container.color": "primary", "md.comp.checkbox.selected.pressed.icon.color": "onPrimary", "md.comp.checkbox.selected.pressed.outline.width": 0.0, "md.comp.checkbox.selected.pressed.state-layer.color": "onSurface", "md.comp.checkbox.selected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.checkbox.state-layer.shape": "md.sys.shape.corner.full", "md.comp.checkbox.state-layer.size": 40.0, "md.comp.checkbox.unselected.disabled.container.opacity": 0.38, "md.comp.checkbox.unselected.disabled.outline.color": "onSurface", "md.comp.checkbox.unselected.disabled.outline.width": 2.0, "md.comp.checkbox.unselected.error.focus.outline.color": "error", "md.comp.checkbox.unselected.error.hover.outline.color": "error", "md.comp.checkbox.unselected.error.outline.color": "error", "md.comp.checkbox.unselected.error.pressed.outline.color": "error", "md.comp.checkbox.unselected.focus.outline.color": "onSurface", "md.comp.checkbox.unselected.focus.outline.width": 2.0, "md.comp.checkbox.unselected.focus.state-layer.color": "onSurface", "md.comp.checkbox.unselected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.checkbox.unselected.hover.outline.color": "onSurface", "md.comp.checkbox.unselected.hover.outline.width": 2.0, "md.comp.checkbox.unselected.hover.state-layer.color": "onSurface", "md.comp.checkbox.unselected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.checkbox.unselected.outline.color": "onSurfaceVariant", "md.comp.checkbox.unselected.outline.width": 2.0, "md.comp.checkbox.unselected.pressed.outline.color": "onSurface", "md.comp.checkbox.unselected.pressed.outline.width": 2.0, "md.comp.checkbox.unselected.pressed.state-layer.color": "primary", "md.comp.checkbox.unselected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity"}