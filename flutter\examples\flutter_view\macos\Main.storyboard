<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.Storyboard.XIB" version="3.0" toolsVersion="21208.1" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="21208.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Flutter View Controller-->
        <scene sceneID="MPq-6h-HP0">
            <objects>
                <viewController id="7GY-dJ-cew" userLabel="Flutter View Controller" customClass="FlutterViewController" sceneMemberID="viewController">
                    <view key="view" id="eEt-A6-Jxk">
                        <rect key="frame" x="0.0" y="0.0" width="450" height="404"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </view>
                </viewController>
                <customObject id="eTc-YD-UwH" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="287" y="704"/>
        </scene>
        <!--Main View Controller-->
        <scene sceneID="OoP-iw-RGx">
            <objects>
                <viewController storyboardIdentifier="MainViewController" id="Zco-B0-Eo5" customClass="MainViewController" customModule="flutter_view" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" id="yo2-24-9SJ">
                        <rect key="frame" x="0.0" y="0.0" width="913" height="683"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <stackView distribution="fill" orientation="horizontal" alignment="top" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="vIi-fL-74a">
                                <rect key="frame" x="0.0" y="0.0" width="913" height="683"/>
                                <subviews>
                                    <containerView translatesAutoresizingMaskIntoConstraints="NO" id="NLg-mU-To9">
                                        <rect key="frame" x="0.0" y="1" width="450" height="682"/>
                                        <connections>
                                            <segue destination="fkR-dw-CPc" kind="embed" identifier="NativeViewControllerSegue" id="JOW-cD-XX0"/>
                                        </connections>
                                    </containerView>
                                    <containerView translatesAutoresizingMaskIntoConstraints="NO" id="AGJ-Cf-BSz">
                                        <rect key="frame" x="458" y="1" width="455" height="682"/>
                                        <connections>
                                            <segue destination="7GY-dJ-cew" kind="embed" identifier="FlutterViewControllerSegue" id="0Il-tC-ptk"/>
                                        </connections>
                                    </containerView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="AGJ-Cf-BSz" firstAttribute="height" secondItem="NLg-mU-To9" secondAttribute="height" id="5bU-7q-KyD"/>
                                    <constraint firstItem="AGJ-Cf-BSz" firstAttribute="width" secondItem="NLg-mU-To9" secondAttribute="width" multiplier="1.01111" id="PQE-oI-RoL"/>
                                </constraints>
                                <visibilityPriorities>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                </visibilityPriorities>
                                <customSpacing>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                </customSpacing>
                            </stackView>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="vIi-fL-74a" secondAttribute="bottom" id="ORj-Up-bVE"/>
                            <constraint firstAttribute="trailing" secondItem="vIi-fL-74a" secondAttribute="trailing" id="XqL-pC-rc8"/>
                            <constraint firstItem="vIi-fL-74a" firstAttribute="leading" secondItem="yo2-24-9SJ" secondAttribute="leading" id="kXT-6f-V55"/>
                            <constraint firstItem="vIi-fL-74a" firstAttribute="top" secondItem="yo2-24-9SJ" secondAttribute="top" id="ws7-M4-M5U"/>
                        </constraints>
                    </view>
                </viewController>
                <customObject id="RER-RE-Ii7" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="13.5" y="26.5"/>
        </scene>
        <!--Native View Controller-->
        <scene sceneID="6te-4i-Zpy">
            <objects>
                <viewController id="fkR-dw-CPc" userLabel="Native View Controller" customClass="NativeViewController" customModule="flutter_view" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" id="lMy-hx-gLK">
                        <rect key="frame" x="0.0" y="0.0" width="450" height="404"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <stackView distribution="fillProportionally" orientation="vertical" alignment="leading" spacing="0.0" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="bEP-zh-PC4">
                                <rect key="frame" x="0.0" y="0.0" width="450" height="404"/>
                                <subviews>
                                    <customView translatesAutoresizingMaskIntoConstraints="NO" id="7vK-z0-I0z" userLabel="Top">
                                        <rect key="frame" x="0.0" y="70" width="450" height="334"/>
                                        <subviews>
                                            <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" misplaced="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ic4-Iq-BCT">
                                                <rect key="frame" x="207" y="163" width="4" height="16"/>
                                                <textFieldCell key="cell" lineBreakMode="clipping" id="EHZ-Ff-uN9">
                                                    <font key="font" metaFont="system" size="17"/>
                                                    <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="ic4-Iq-BCT" firstAttribute="centerX" secondItem="7vK-z0-I0z" secondAttribute="centerX" id="edj-sZ-5O7"/>
                                            <constraint firstItem="ic4-Iq-BCT" firstAttribute="centerY" secondItem="7vK-z0-I0z" secondAttribute="centerY" id="v3Q-Xa-flH"/>
                                        </constraints>
                                    </customView>
                                    <customView autoresizesSubviews="NO" translatesAutoresizingMaskIntoConstraints="NO" id="O0r-UF-j95" userLabel="Bottom">
                                        <rect key="frame" x="0.0" y="0.0" width="450" height="70"/>
                                        <subviews>
                                            <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Ocn-t9-rHR">
                                                <rect key="frame" x="18" y="20" width="100" height="35"/>
                                                <textFieldCell key="cell" lineBreakMode="clipping" title="macOS" id="TOF-2b-RIY">
                                                    <font key="font" metaFont="system" size="30"/>
                                                    <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                            <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="uyM-Dx-MIG">
                                                <rect key="frame" x="375" y="14" width="55" height="57"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="55" id="2Zv-ge-8fE"/>
                                                    <constraint firstAttribute="width" constant="55" id="bQo-k1-1eM"/>
                                                </constraints>
                                                <buttonCell key="cell" type="smallSquare" title="+" bezelStyle="smallSquare" alignment="center" lineBreakMode="truncatingTail" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="az3-aa-8jC">
                                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                    <font key="font" metaFont="system" size="18"/>
                                                </buttonCell>
                                                <connections>
                                                    <action selector="handleIncrement:" target="fkR-dw-CPc" id="7k2-NW-4LI"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="Ocn-t9-rHR" firstAttribute="leading" secondItem="O0r-UF-j95" secondAttribute="leading" constant="20" symbolic="YES" id="KhQ-Xf-SiD"/>
                                            <constraint firstAttribute="trailing" secondItem="uyM-Dx-MIG" secondAttribute="trailing" constant="20" symbolic="YES" id="LFX-ph-6nb"/>
                                            <constraint firstAttribute="bottom" secondItem="Ocn-t9-rHR" secondAttribute="bottom" constant="20" symbolic="YES" id="Ofu-fn-RDS"/>
                                            <constraint firstAttribute="height" constant="70" id="V41-3J-PYi"/>
                                            <constraint firstAttribute="bottom" secondItem="uyM-Dx-MIG" secondAttribute="bottom" constant="15" id="Xfq-yS-emb"/>
                                        </constraints>
                                    </customView>
                                </subviews>
                                <visibilityPriorities>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                </visibilityPriorities>
                                <customSpacing>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                </customSpacing>
                            </stackView>
                        </subviews>
                        <constraints>
                            <constraint firstItem="bEP-zh-PC4" firstAttribute="top" secondItem="lMy-hx-gLK" secondAttribute="top" id="6Fg-dO-Yhl"/>
                            <constraint firstItem="bEP-zh-PC4" firstAttribute="leading" secondItem="lMy-hx-gLK" secondAttribute="leading" id="ZiX-CF-CPT"/>
                            <constraint firstAttribute="bottom" secondItem="bEP-zh-PC4" secondAttribute="bottom" id="doI-ip-Pbz"/>
                            <constraint firstAttribute="trailing" secondItem="bEP-zh-PC4" secondAttribute="trailing" id="jrN-ON-H5M"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="incrementLabel" destination="ic4-Iq-BCT" id="M3U-fC-vbY"/>
                    </connections>
                </viewController>
                <customObject id="v3R-qm-QoP" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-218" y="704"/>
        </scene>
    </scenes>
</document>
