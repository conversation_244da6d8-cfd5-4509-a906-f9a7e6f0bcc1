{"name": "flutter_api_samples", "short_name": "flutter_api_samples", "start_url": ".", "display": "standalone", "background_color": "#0175C2", "theme_color": "#0175C2", "description": "A temporary code sample for Curve2D", "orientation": "portrait-primary", "prefer_related_applications": false, "icons": [{"src": "icons/Icon-192.png", "sizes": "192x192", "type": "image/png"}, {"src": "icons/Icon-512.png", "sizes": "512x512", "type": "image/png"}, {"src": "icons/Icon-maskable-192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "icons/Icon-maskable-512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}]}