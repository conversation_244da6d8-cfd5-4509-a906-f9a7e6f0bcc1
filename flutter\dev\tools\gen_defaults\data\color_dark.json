{"version": "6_1_0", "md.sys.color.background": "md.ref.palette.neutral6", "md.sys.color.error": "md.ref.palette.error80", "md.sys.color.error-container": "md.ref.palette.error30", "md.sys.color.inverse-on-surface": "md.ref.palette.neutral20", "md.sys.color.inverse-primary": "md.ref.palette.primary40", "md.sys.color.inverse-surface": "md.ref.palette.neutral90", "md.sys.color.on-background": "md.ref.palette.neutral90", "md.sys.color.on-error": "md.ref.palette.error20", "md.sys.color.on-error-container": "md.ref.palette.error90", "md.sys.color.on-primary": "md.ref.palette.primary20", "md.sys.color.on-primary-container": "md.ref.palette.primary90", "md.sys.color.on-primary-fixed": "md.ref.palette.primary10", "md.sys.color.on-primary-fixed-variant": "md.ref.palette.primary30", "md.sys.color.on-secondary": "md.ref.palette.secondary20", "md.sys.color.on-secondary-container": "md.ref.palette.secondary90", "md.sys.color.on-secondary-fixed": "md.ref.palette.secondary10", "md.sys.color.on-secondary-fixed-variant": "md.ref.palette.secondary30", "md.sys.color.on-surface": "md.ref.palette.neutral90", "md.sys.color.on-surface-variant": "md.ref.palette.neutral-variant80", "md.sys.color.on-tertiary": "md.ref.palette.tertiary20", "md.sys.color.on-tertiary-container": "md.ref.palette.tertiary90", "md.sys.color.on-tertiary-fixed": "md.ref.palette.tertiary10", "md.sys.color.on-tertiary-fixed-variant": "md.ref.palette.tertiary30", "md.sys.color.outline": "md.ref.palette.neutral-variant60", "md.sys.color.outline-variant": "md.ref.palette.neutral-variant30", "md.sys.color.primary": "md.ref.palette.primary80", "md.sys.color.primary-container": "md.ref.palette.primary30", "md.sys.color.primary-fixed": "md.ref.palette.primary90", "md.sys.color.primary-fixed-dim": "md.ref.palette.primary80", "md.sys.color.scrim": "md.ref.palette.neutral0", "md.sys.color.secondary": "md.ref.palette.secondary80", "md.sys.color.secondary-container": "md.ref.palette.secondary30", "md.sys.color.secondary-fixed": "md.ref.palette.secondary90", "md.sys.color.secondary-fixed-dim": "md.ref.palette.secondary80", "md.sys.color.shadow": "md.ref.palette.neutral0", "md.sys.color.surface": "md.ref.palette.neutral6", "md.sys.color.surface-bright": "md.ref.palette.neutral24", "md.sys.color.surface-container": "md.ref.palette.neutral12", "md.sys.color.surface-container-high": "md.ref.palette.neutral17", "md.sys.color.surface-container-highest": "md.ref.palette.neutral22", "md.sys.color.surface-container-low": "md.ref.palette.neutral10", "md.sys.color.surface-container-lowest": "md.ref.palette.neutral4", "md.sys.color.surface-dim": "md.ref.palette.neutral6", "md.sys.color.surface-tint": "primary", "md.sys.color.surface-variant": "md.ref.palette.neutral-variant30", "md.sys.color.tertiary": "md.ref.palette.tertiary80", "md.sys.color.tertiary-container": "md.ref.palette.tertiary30", "md.sys.color.tertiary-fixed": "md.ref.palette.tertiary90", "md.sys.color.tertiary-fixed-dim": "md.ref.palette.tertiary80"}