// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// @docImport 'package:flutter_driver/flutter_driver.dart';
library;

import 'message.dart';

/// A Flutter Driver command that requests a string representation of the layer tree.
class GetLayerTree extends Command {
  /// Create a command to request a string representation of the layer tree.
  const GetLayerTree({super.timeout});

  /// Deserializes this command from the value generated by [serialize].
  GetLayerTree.deserialize(super.json) : super.deserialize();

  @override
  String get kind => 'get_layer_tree';
}

/// A string representation of the layer tree, the result of a
/// [FlutterDriver.getLayerTree] method.
class LayerTree extends Result {
  /// Creates a [LayerTree] object with the given string representation.
  const LayerTree(this.tree);

  /// Deserializes the result from JSON.
  LayerTree.fromJson(Map<String, dynamic> json) : tree = json['tree'] as String;

  /// String representation of the layer tree.
  final String? tree;

  @override
  Map<String, dynamic> toJson() => <String, dynamic>{'tree': tree};
}
