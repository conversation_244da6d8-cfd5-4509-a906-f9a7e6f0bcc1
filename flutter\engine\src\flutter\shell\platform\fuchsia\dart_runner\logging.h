// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_SHELL_PLATFORM_FUCHSIA_DART_RUNNER_LOGGING_H_
#define FLUTTER_SHELL_PLATFORM_FUCHSIA_DART_RUNNER_LOGGING_H_

namespace dart_runner {

// Use to mark logs published via the syslog API.
#define LOG_TAG "dart-runner"

}  // namespace dart_runner

#endif  // FLUTTER_SHELL_PLATFORM_FUCHSIA_DART_RUNNER_LOGGING_H_
