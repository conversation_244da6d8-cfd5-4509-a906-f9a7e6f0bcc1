// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_FML_MATH_H_
#define FLUTTER_FML_MATH_H_

namespace flutter {
namespace math {

// e
constexpr float kE = 2.7182818284590452354;

// log_2 e
constexpr float kLog2E = 1.4426950408889634074;

// log_10 e
constexpr float kLog10E = 0.43429448190325182765;

// log_e 2
constexpr float kLogE2 = 0.69314718055994530942;

// log_e 10
constexpr float kLogE10 = 2.30258509299404568402;

// pi
constexpr float kPi = 3.14159265358979323846;

// pi/2
constexpr float kPiOver2 = 1.57079632679489661923;

// pi/4
constexpr float kPiOver4 = 0.78539816339744830962;

// 1/pi
constexpr float k1OverPi = 0.31830988618379067154;

// 2/pi
constexpr float k2OverPi = 0.63661977236758134308;

// 2/sqrt(pi)
constexpr float k2OverSqrtPi = 1.12837916709551257390;

// sqrt(2)
constexpr float kSqrt2 = 1.41421356237309504880;

// 1/sqrt(2)
constexpr float k1OverSqrt2 = 0.70710678118654752440;

}  // namespace math
}  // namespace flutter

#endif  // FLUTTER_FML_MATH_H_
