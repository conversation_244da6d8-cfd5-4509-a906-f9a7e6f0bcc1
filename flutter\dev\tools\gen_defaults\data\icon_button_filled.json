{"version": "6_1_0", "md.comp.filled-icon-button.container.color": "primary", "md.comp.filled-icon-button.container.height": 40.0, "md.comp.filled-icon-button.container.shape": "md.sys.shape.corner.full", "md.comp.filled-icon-button.container.width": 40.0, "md.comp.filled-icon-button.disabled.container.color": "onSurface", "md.comp.filled-icon-button.disabled.container.opacity": 0.12, "md.comp.filled-icon-button.disabled.icon.color": "onSurface", "md.comp.filled-icon-button.disabled.icon.opacity": 0.38, "md.comp.filled-icon-button.focus.icon.color": "onPrimary", "md.comp.filled-icon-button.focus.indicator.color": "secondary", "md.comp.filled-icon-button.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.filled-icon-button.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.filled-icon-button.focus.state-layer.color": "onPrimary", "md.comp.filled-icon-button.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.filled-icon-button.hover.icon.color": "onPrimary", "md.comp.filled-icon-button.hover.state-layer.color": "onPrimary", "md.comp.filled-icon-button.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.filled-icon-button.icon.color": "onPrimary", "md.comp.filled-icon-button.icon.size": 24.0, "md.comp.filled-icon-button.pressed.icon.color": "onPrimary", "md.comp.filled-icon-button.pressed.state-layer.color": "onPrimary", "md.comp.filled-icon-button.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.filled-icon-button.selected.container.color": "primary", "md.comp.filled-icon-button.toggle.selected.focus.icon.color": "onPrimary", "md.comp.filled-icon-button.toggle.selected.focus.state-layer.color": "onPrimary", "md.comp.filled-icon-button.toggle.selected.hover.icon.color": "onPrimary", "md.comp.filled-icon-button.toggle.selected.hover.state-layer.color": "onPrimary", "md.comp.filled-icon-button.toggle.selected.icon.color": "onPrimary", "md.comp.filled-icon-button.toggle.selected.pressed.icon.color": "onPrimary", "md.comp.filled-icon-button.toggle.selected.pressed.state-layer.color": "onPrimary", "md.comp.filled-icon-button.toggle.unselected.focus.icon.color": "primary", "md.comp.filled-icon-button.toggle.unselected.focus.state-layer.color": "primary", "md.comp.filled-icon-button.toggle.unselected.hover.icon.color": "primary", "md.comp.filled-icon-button.toggle.unselected.hover.state-layer.color": "primary", "md.comp.filled-icon-button.toggle.unselected.icon.color": "primary", "md.comp.filled-icon-button.toggle.unselected.pressed.icon.color": "primary", "md.comp.filled-icon-button.toggle.unselected.pressed.state-layer.color": "primary", "md.comp.filled-icon-button.unselected.container.color": "surfaceContainerHighest"}