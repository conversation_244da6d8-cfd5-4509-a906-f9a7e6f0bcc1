//---------------------------------------------------------------------------------------------
//  Copyright (c) 2022 Google LLC
//  Licensed under the MIT License. See License.txt in the project root for license information.
//--------------------------------------------------------------------------------------------*/

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
//
// This file is auto generated by flutter/engine:flutter/tools/gen_web_keyboard_keymap based on
// @@@COMMIT_URL@@@
//
// Edit the following files instead:
//
//  - Script: lib/main.dart
//  - Templates: data/*.tmpl
//
// See flutter/engine:flutter/tools/gen_web_locale_keymap/README.md for more information.
@@@COMMON@@@

/// Data for [LocaleKeymap] on Windows.
///
/// Do not use this value, but [LocaleKeymap.win] instead.
///
/// The keys are `KeyboardEvent.code` and then `KeyboardEvent.key`. The values
/// are logical keys or [kUseKeyCode]. Entries that can be derived using
/// heuristics have been omitted.
Map<String, Map<String, int>> getMappingDataWin() {
@@@WIN_MAPPING@@@
}

/// Data for [LocaleKeymap] on Linux.
///
/// Do not use this value, but [LocaleKeymap.linux] instead.
///
/// The keys are `KeyboardEvent.code` and then `KeyboardEvent.key`. The values
/// are logical keys or [kUseKeyCode]. Entries that can be derived using
/// heuristics have been omitted.
Map<String, Map<String, int>> getMappingDataLinux() {
@@@LINUX_MAPPING@@@
}

/// Data for [LocaleKeymap] on Darwin.
///
/// Do not use this value, but [LocaleKeymap.darwin] instead.
///
/// The keys are `KeyboardEvent.code` and then `KeyboardEvent.key`. The values
/// are logical keys or [kUseKeyCode]. Entries that can be derived using
/// heuristics have been omitted.
Map<String, Map<String, int>> getMappingDataDarwin() {
@@@DARWIN_MAPPING@@@
}
