# In general, the regular analysis_options.yaml file found at the root of the
# repo also applies to engine code.
#
# This file applies some engine-specific settings to the general rule set.
# Deviations from the general rules are rare. They should be kept to a
# minimum to ensure a unified style across the repo.
#
# The reasoning for deviating from the general style must be documented below.

include: ../../../analysis_options.yaml

analyzer:
  exclude:
    - examples
    # Fixture depends on dart:ui and raises false positives.
    - flutter_frontend_server/test/fixtures/lib/main.dart
    - prebuilts
    - third_party
    - shell/platform/fuchsia

linter:
  rules:
    always_specify_types: false # see https://github.com/flutter/engine/blob/main/CONTRIBUTING.md#dart
    avoid_equals_and_hash_code_on_mutable_classes: false # cannot import the meta package here
    missing_whitespace_between_adjacent_strings: false # too many false positives
    public_member_api_docs: true # dart:ui is public API
    type_annotate_public_apis: true # to compensate for disabeling always_specify_types, see https://github.com/flutter/engine/blob/main/CONTRIBUTING.md#dart
    use_string_in_part_of_directives: false # needs to be evaluated, dart:ui frequently uses non-strings
