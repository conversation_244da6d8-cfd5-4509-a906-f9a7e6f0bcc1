// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'dart:ui';

// The code below was generated by modify several parts of the engine
// May 2020 and running Flutter Gallery.

late PathFillType gFillType;
late Rect gBounds;
late List<dynamic> allPaths;

late Path path8;
late Path path10;
late Path path12;
late Path path14;
late Path path16;
late Path path18;
late Path path34;
late Path path50;
late Path path60;
late Path path63;
late Path path66;
late Path path69;
late Path path72;
late Path path75;
late Path path78;
late Path path80;
late Path path82;
late Path path84;
late Path path86;
late Path path88;
late Path path119;
late Path path120;
late Path path121;
late Path path122;
late Path path123;
late Path path125;
late Path path127;
late Path path129;
late Path path131;
late Path path132;
late Path path134;
late Path path137;
late Path path140;
late Path path143;
late Path path145;
late Path path147;
late Path path208;
late Path path209;
late Path path210;
late Path path211;
late Path path213;
late Path path216;
late Path path219;
late Path path222;
late Path path225;
late Path path227;
late Path path229;
late Path path232;
late Path path235;
late Path path238;
late Path path240;
late Path path242;
late Path path277;
late Path path278;
late Path path279;
late Path path280;
late Path path281;
late Path path282;
late Path path284;
late Path path286;
late Path path288;
late Path path290;
late Path path292;
late Path path295;
late Path path298;
late Path path301;
late Path path330;
late Path path331;
late Path path332;
late Path path333;
late Path path334;
late Path path336;
late Path path338;
late Path path340;
late Path path342;
late Path path344;
late Path path345;
late Path path346;
late Path path349;
late Path path352;
late Path path356;
late Path path358;
late Path path359;
late Path path360;
late Path path361;
late Path path362;
late Path path363;
late Path path366;
late Path path369;
late Path path372;
late Path path373;
late Path path374;
late Path path375;
late Path path376;
late Path path379;
late Path path382;
late Path path385;
late Path path386;
late Path path387;
late Path path388;
late Path path389;
late Path path392;
late Path path395;
late Path path398;
late Path path399;
late Path path400;
late Path path401;
late Path path402;
late Path path405;
late Path path408;
late Path path411;
late Path path412;
late Path path413;
late Path path414;
late Path path415;
late Path path418;
late Path path421;
late Path path424;
late Path path425;
late Path path426;
late Path path427;
late Path path428;
late Path path431;
late Path path434;
late Path path437;
late Path path438;
late Path path439;
late Path path440;
late Path path441;
late Path path444;
late Path path447;
late Path path450;
late Path path451;
late Path path452;
late Path path453;
late Path path454;
late Path path457;
late Path path460;
late Path path463;
late Path path464;
late Path path465;
late Path path466;
late Path path467;
late Path path470;
late Path path473;
late Path path476;
late Path path477;
late Path path478;
late Path path479;
late Path path480;
late Path path483;
late Path path486;
late Path path489;
late Path path490;
late Path path491;
late Path path492;
late Path path493;
late Path path496;
late Path path499;
late Path path502;
late Path path503;
late Path path504;
late Path path505;
late Path path506;
late Path path509;
late Path path512;
late Path path515;
late Path path516;
late Path path517;
late Path path518;
late Path path519;
late Path path522;
late Path path525;
late Path path528;
late Path path529;
late Path path530;
late Path path531;
late Path path532;
late Path path535;
late Path path538;
late Path path541;
late Path path542;
late Path path543;
late Path path544;
late Path path545;
late Path path548;
late Path path551;
late Path path554;
late Path path555;
late Path path556;
late Path path557;
late Path path558;
late Path path561;
late Path path564;
late Path path573;
late Path path577;
late Path path579;
late Path path591;
late Path path592;
late Path path593;
late Path path594;
late Path path595;
late Path path597;
late Path path599;
late Path path601;
late Path path603;
late Path path606;
late Path path608;

void createPaths() {
  allPaths = <dynamic>[];
  pathOps0();
  pathOps1();
  pathOps2();
  pathOps3();
  pathOps4();
  pathOps5();
  pathOps6();
  pathOps7();
  pathOps8();
  pathOps9();
  pathOps10();
  pathOps11();
  pathOps12();
  pathOps13();
  pathOps14();
  pathOps15();
  pathOps16();
  pathOps17();
  pathOps18();
  pathOps19();
  pathOps20();
  pathOps21();
  pathOps22();
  pathOps23();
  pathOps24();
  pathOps25();
  pathOps26();
  pathOps27();
  pathOps28();
  pathOps29();
  pathOps30();
  pathOps31();
  pathOps32();
  pathOps33();
  pathOps34();
  pathOps35();
  pathOps36();
  pathOps37();
  pathOps38();
  pathOps39();
  pathOps40();
  pathOps41();
  pathOps42();
  pathOps43();
  pathOps44();
  pathOps45();
  pathOps46();
  pathOps47();
  pathOps48();
  pathOps49();
  pathOps50();
  pathOps51();
  pathOps52();
  pathOps53();
  pathOps54();
  pathOps55();
  pathOps56();
  pathOps57();
  pathOps58();
  pathOps59();
  pathOps60();
  pathOps61();
  pathOps62();
  pathOps63();
  pathOps64();
  pathOps65();
  pathOps66();
  pathOps67();
  pathOps68();
  pathOps69();
  pathOps70();
  pathOps71();
  pathOps72();
  pathOps73();
  pathOps74();
  pathOps75();
  pathOps76();
  pathOps77();
  pathOps78();
  pathOps79();
  pathOps80();
  pathOps81();
  pathOps82();
  pathOps83();
  pathOps84();
  pathOps85();
  pathOps86();
  pathOps87();
  pathOps88();
  pathOps89();
  pathOps90();
  pathOps91();
  pathOps92();
  pathOps93();
  pathOps94();
  pathOps95();
  pathOps96();
  pathOps97();
  pathOps98();
  pathOps99();
  pathOps100();
  pathOps101();
  pathOps102();
  pathOps103();
  pathOps104();
  pathOps105();
  pathOps106();
  pathOps107();
  pathOps108();
  pathOps109();
  pathOps110();
  pathOps111();
  pathOps112();
  pathOps113();
  pathOps114();
  pathOps115();
  pathOps116();
  pathOps117();
  pathOps118();
  pathOps119();
  pathOps120();
  pathOps121();
  pathOps122();
  pathOps123();
  pathOps124();
  pathOps125();
  pathOps126();
  pathOps127();
  pathOps128();
  pathOps129();
  pathOps130();
  pathOps131();
  pathOps132();
  pathOps133();
  pathOps134();
  pathOps135();
  pathOps136();
  pathOps137();
  pathOps138();
  pathOps139();
  pathOps140();
  pathOps141();
  pathOps142();
  pathOps143();
  pathOps144();
  pathOps145();
  pathOps146();
  pathOps147();
  pathOps149();
  pathOps150();
  pathOps155();
  pathOps156();
  pathOps161();
  pathOps162();
  pathOps164();
  pathOps165();
  pathOps167();
  pathOps168();
  pathOps173();
  pathOps174();
  pathOps178();
  pathOps179();
  pathOps180();
  pathOps181();
  pathOps182();
  pathOps183();
  pathOps184();
  pathOps185();
  pathOps186();
  pathOps187();
  pathOps188();
  pathOps189();
  pathOps190();
  pathOps191();
  pathOps192();
  pathOps193();
  pathOps194();
  pathOps195();
  pathOps196();
  pathOps197();
  pathOps198();
  pathOps199();
  pathOps200();
  pathOps201();
  pathOps202();
  pathOps203();
  pathOps204();
  pathOps205();
  pathOps206();
  pathOps207();
  pathOps208();
  pathOps209();
  pathOps210();
  pathOps211();
  pathOps212();
  pathOps213();
  pathOps214();
  pathOps215();
  pathOps216();
  pathOps217();
  pathOps218();
  pathOps219();
  pathOps220();
  pathOps221();
  pathOps222();
  pathOps223();
  pathOps224();
  pathOps225();
  pathOps226();
  pathOps227();
  pathOps228();
  pathOps229();
  pathOps230();
  pathOps231();
  pathOps232();
  pathOps233();
  pathOps234();
  pathOps235();
  pathOps236();
  pathOps237();
  pathOps238();
  pathOps239();
  pathOps240();
  pathOps241();
  pathOps242();
  pathOps243();
  pathOps244();
  pathOps245();
  pathOps246();
  pathOps247();
  pathOps248();
  pathOps249();
  pathOps250();
  pathOps251();
  pathOps252();
  pathOps253();
  pathOps254();
  pathOps255();
  pathOps256();
  pathOps257();
  pathOps258();
  pathOps259();
  pathOps260();
  pathOps261();
  pathOps262();
  pathOps263();
  pathOps264();
  pathOps265();
  pathOps266();
  pathOps267();
  pathOps268();
  pathOps269();
  pathOps270();
  pathOps271();
  pathOps272();
  pathOps273();
  pathOps274();
  pathOps275();
  pathOps276();
  pathOps277();
  pathOps278();
  pathOps279();
  pathOps280();
  pathOps281();
  pathOps282();
  pathOps283();
  pathOps284();
  pathOps285();
  pathOps286();
  pathOps287();
  pathOps288();
  pathOps289();
  pathOps290();
  pathOps291();
  pathOps292();
  pathOps293();
  pathOps294();
  pathOps295();
  pathOps296();
  pathOps297();
  pathOps298();
  pathOps299();
  pathOps300();
  pathOps301();
  pathOps302();
  pathOps303();
  pathOps304();
  pathOps305();
  pathOps306();
  pathOps307();
  pathOps308();
  pathOps309();
  pathOps310();
  pathOps311();
  pathOps312();
  pathOps313();
  pathOps314();
  pathOps315();
  pathOps316();
  pathOps317();
  pathOps318();
  pathOps319();
  pathOps320();
  pathOps321();
  pathOps322();
  pathOps323();
  pathOps324();
  pathOps325();
  pathOps326();
  pathOps327();
  pathOps328();
  pathOps329();
  pathOps330();
  pathOps331();
  pathOps332();
  pathOps333();
  pathOps334();
  pathOps335();
  pathOps336();
  pathOps337();
  pathOps338();
  pathOps339();
  pathOps340();
  pathOps341();
  pathOps342();
  pathOps343();
  pathOps344();
  pathOps345();
  pathOps346();
  pathOps347();
  pathOps348();
  pathOps349();
  pathOps350();
  pathOps351();
  pathOps352();
  pathOps353();
  pathOps354();
  pathOps355();
  pathOps356();
  pathOps357();
  pathOps358();
  pathOps359();
  pathOps360();
  pathOps361();
  pathOps362();
  pathOps363();
  pathOps364();
  pathOps365();
  pathOps366();
  pathOps367();
  pathOps368();
  pathOps369();
  pathOps370();
  pathOps371();
  pathOps372();
  pathOps373();
  pathOps374();
  pathOps375();
  pathOps376();
  pathOps377();
  pathOps378();
  pathOps379();
  pathOps380();
  pathOps381();
  pathOps382();
  pathOps383();
  pathOps384();
  pathOps385();
  pathOps386();
  pathOps387();
  pathOps388();
  pathOps389();
  pathOps390();
  pathOps391();
  pathOps392();
  pathOps393();
  pathOps394();
  pathOps395();
  pathOps396();
  pathOps397();
  pathOps398();
  pathOps399();
  pathOps400();
  pathOps401();
  pathOps402();
  pathOps403();
  pathOps404();
  pathOps405();
  pathOps406();
  pathOps407();
  pathOps408();
  pathOps409();
  pathOps410();
  pathOps411();
  pathOps412();
  pathOps413();
  pathOps414();
  pathOps415();
  pathOps416();
  pathOps417();
  pathOps418();
  pathOps419();
  pathOps420();
  pathOps421();
  pathOps422();
  pathOps423();
  pathOps424();
  pathOps425();
  pathOps426();
  pathOps427();
  pathOps428();
  pathOps429();
  pathOps430();
  pathOps431();
  pathOps432();
  pathOps433();
  pathOps434();
  pathOps435();
  pathOps436();
  pathOps437();
  pathOps438();
  pathOps439();
  pathOps440();
  pathOps441();
  pathOps442();
  pathOps443();
  pathOps444();
  pathOps445();
  pathOps446();
  pathOps447();
  pathOps448();
  pathOps449();
  pathOps450();
  pathOps451();
  pathOps452();
  pathOps453();
  pathOps454();
  pathOps455();
  pathOps456();
  pathOps457();
  pathOps458();
  pathOps459();
  pathOps460();
  pathOps461();
  pathOps462();
  pathOps463();
  pathOps464();
  pathOps465();
  pathOps466();
  pathOps467();
  pathOps468();
  pathOps469();
  pathOps470();
  pathOps471();
  pathOps472();
  pathOps473();
  pathOps474();
  pathOps475();
  pathOps476();
  pathOps477();
  pathOps478();
  pathOps479();
  pathOps480();
  pathOps481();
  pathOps482();
  pathOps483();
  pathOps484();
  pathOps485();
  pathOps486();
  pathOps487();
  pathOps488();
  pathOps489();
  pathOps490();
  pathOps491();
  pathOps492();
  pathOps493();
  pathOps494();
  pathOps495();
  pathOps496();
  pathOps497();
  pathOps498();
  pathOps499();
  pathOps500();
  pathOps501();
  pathOps502();
  pathOps503();
  pathOps504();
  pathOps505();
  pathOps506();
  pathOps507();
  pathOps508();
  pathOps509();
  pathOps510();
  pathOps511();
  pathOps512();
  pathOps513();
  pathOps514();
  pathOps515();
  pathOps516();
  pathOps517();
  pathOps518();
  pathOps519();
  pathOps520();
  pathOps521();
  pathOps522();
  pathOps523();
  pathOps524();
  pathOps525();
  pathOps526();
  pathOps527();
  pathOps528();
  pathOps529();
  pathOps530();
  pathOps531();
  pathOps532();
  pathOps533();
  pathOps534();
  pathOps535();
  pathOps536();
  pathOps537();
  pathOps538();
  pathOps539();
  pathOps540();
  pathOps541();
  pathOps542();
  pathOps543();
  pathOps544();
  pathOps545();
  pathOps546();
  pathOps547();
  pathOps548();
  pathOps549();
  pathOps550();
  pathOps551();
  pathOps552();
  pathOps553();
  pathOps554();
  pathOps555();
  pathOps556();
  pathOps557();
  pathOps558();
  pathOps559();
  pathOps560();
  pathOps561();
  pathOps562();
  pathOps563();
  pathOps564();
  pathOps565();
  pathOps566();
  pathOps567();
  pathOps568();
  pathOps569();
  pathOps570();
  pathOps571();
  pathOps572();
  pathOps573();
  pathOps574();
  pathOps575();
  pathOps576();
  pathOps577();
  pathOps578();
  pathOps579();
  pathOps580();
  pathOps581();
  pathOps582();
  pathOps583();
  pathOps584();
  pathOps585();
  pathOps586();
  pathOps587();
  pathOps588();
  pathOps589();
  pathOps590();
  pathOps596();
  pathOps598();
  pathOps600();
  pathOps602();
  pathOps604();
  pathOps605();
  pathOps607();
}

void pathOps0() {
  final Path path0 = Path();
  path0.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(20, 20, 60, 60), const Radius.circular(10)),
  );
  gBounds = path0.getBounds();
  gBounds = path0.getBounds();
  gBounds = path0.getBounds();
  _runPathTest(path0);
  gBounds = path0.getBounds();
  _runPathTest(path0);
  allPaths.add(path0);
}

void pathOps1() {
  final Path path1 = Path();
  path1.addOval(const Rect.fromLTRB(20, 20, 60, 60));
  gBounds = path1.getBounds();
  gBounds = path1.getBounds();
  gBounds = path1.getBounds();
  gBounds = path1.getBounds();
  _runPathTest(path1);
  gFillType = path1.fillType;
  _runPathTest(path1);
  gFillType = path1.fillType;
  _runPathTest(path1);
  gFillType = path1.fillType;
  _runPathTest(path1);
  gFillType = path1.fillType;
  allPaths.add(path1);
}

void pathOps2() {
  final Path path2 = Path();
  path2.moveTo(20, 60);
  path2.quadraticBezierTo(60, 20, 60, 60);
  path2.close();
  path2.moveTo(60, 20);
  path2.quadraticBezierTo(60, 60, 20, 60);
  gBounds = path2.getBounds();
  gBounds = path2.getBounds();
  gBounds = path2.getBounds();
  gBounds = path2.getBounds();
  _runPathTest(path2);
  gFillType = path2.fillType;
  _runPathTest(path2);
  gFillType = path2.fillType;
  _runPathTest(path2);
  gFillType = path2.fillType;
  _runPathTest(path2);
  gFillType = path2.fillType;
  allPaths.add(path2);
}

void pathOps3() {
  final Path path3 = Path();
  path3.moveTo(20, 30);
  path3.lineTo(40, 20);
  path3.lineTo(60, 30);
  path3.lineTo(60, 60);
  path3.lineTo(20, 60);
  path3.close();
  gBounds = path3.getBounds();
  gBounds = path3.getBounds();
  gBounds = path3.getBounds();
  gBounds = path3.getBounds();
  _runPathTest(path3);
  gFillType = path3.fillType;
  _runPathTest(path3);
  gFillType = path3.fillType;
  _runPathTest(path3);
  gFillType = path3.fillType;
  _runPathTest(path3);
  gFillType = path3.fillType;
  allPaths.add(path3);
}

void pathOps4() {
  final Path path4 = Path();
  path4.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(8, 8, 328, 248), const Radius.circular(16)),
  );
  _runPathTest(path4);
  allPaths.add(path4);
}

void pathOps5() {
  final Path path5 = Path();
  path5.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(8, 8, 328, 248), const Radius.circular(16)),
  );
  _runPathTest(path5);
  allPaths.add(path5);
}

void pathOps6() {
  final Path path6 = Path();
  path6.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(0, 136, 970, 1144), Radius.zero));
  gBounds = path6.getBounds();
  allPaths.add(path6);
}

void pathOps7() {
  final Path path7 = Path();
  path7.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 179.5, 200), const Radius.circular(10)),
  );
  gFillType = path7.fillType;
  path8 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path121 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path210 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path278 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path332 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path359 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path372 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path385 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path398 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path411 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path424 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path437 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path450 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path463 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path476 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path489 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path502 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path515 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path528 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path541 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path554 = path7.shift(const Offset(15, 8));
  gFillType = path7.fillType;
  path591 = path7.shift(const Offset(15, 8));
  allPaths.add(path7);
}

void pathOps8() {
  gBounds = path8.getBounds();
  allPaths.add(path8);
}

void pathOps9() {
  final Path path9 = Path();
  path9.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 179.5, 200), const Radius.circular(10)),
  );
  gFillType = path9.fillType;
  path10 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path120 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path209 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path279 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path331 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path360 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path373 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path386 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path399 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path412 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path425 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path438 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path451 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path464 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path477 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path490 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path503 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path516 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path529 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path542 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path555 = path9.shift(const Offset(15, 8));
  gFillType = path9.fillType;
  path592 = path9.shift(const Offset(15, 8));
  allPaths.add(path9);
}

void pathOps10() {
  gBounds = path10.getBounds();
  allPaths.add(path10);
}

void pathOps11() {
  final Path path11 = Path();
  path11.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 179.5, 200), const Radius.circular(10)),
  );
  gFillType = path11.fillType;
  path12 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path119 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path208 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path280 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path330 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path361 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path374 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path387 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path400 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path413 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path426 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path439 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path452 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path465 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path478 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path491 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path504 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path517 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path530 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path543 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path556 = path11.shift(const Offset(15, 8));
  gFillType = path11.fillType;
  path593 = path11.shift(const Offset(15, 8));
  allPaths.add(path11);
}

void pathOps12() {
  gBounds = path12.getBounds();
  allPaths.add(path12);
}

void pathOps13() {
  final Path path13 = Path();
  path13.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 179.5, 200), const Radius.circular(10)),
  );
  gFillType = path13.fillType;
  path14 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path122 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path211 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path277 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path333 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path362 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path375 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path388 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path401 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path414 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path427 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path440 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path453 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path466 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path479 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path492 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path505 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path518 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path531 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path544 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path557 = path13.shift(const Offset(15, 8));
  gFillType = path13.fillType;
  path594 = path13.shift(const Offset(15, 8));
  allPaths.add(path13);
}

void pathOps14() {
  gBounds = path14.getBounds();
  allPaths.add(path14);
}

void pathOps15() {
  final Path path15 = Path();
  path15.addOval(const Rect.fromLTRB(0, 0, 58, 58));
  gFillType = path15.fillType;
  path16 = path15.shift(const Offset(860, 79));
  gFillType = path15.fillType;
  path143 = path15.shift(const Offset(860, 79));
  gFillType = path15.fillType;
  path238 = path15.shift(const Offset(860, 79));
  gFillType = path15.fillType;
  path301 = path15.shift(const Offset(860, 79));
  gFillType = path15.fillType;
  path345 = path15.shift(const Offset(860, 79));
  allPaths.add(path15);
}

void pathOps16() {
  gBounds = path16.getBounds();
  allPaths.add(path16);
}

void pathOps17() {
  final Path path17 = Path();
  path17.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(0, 0, 250.66666666666666, 585),
      const Radius.circular(10),
    ),
  );
  gFillType = path17.fillType;
  path18 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path134 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path229 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path292 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path346 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path363 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path376 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path389 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path402 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path415 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path428 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path441 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path454 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path467 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path480 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path493 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path506 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path519 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path532 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path545 = path17.shift(const Offset(81, 0));
  gFillType = path17.fillType;
  path558 = path17.shift(const Offset(81, 0));
  allPaths.add(path17);
}

void pathOps18() {
  gBounds = path18.getBounds();
  allPaths.add(path18);
}

void pathOps19() {
  final Path path19 = Path();
  path19.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path19.getBounds();
  allPaths.add(path19);
}

void pathOps20() {
  final Path path20 = Path();
  path20.reset();
  path20.moveTo(331.66666666666663, 86);
  path20.lineTo(81, 86);
  path20.lineTo(81, 84);
  path20.lineTo(331.66666666666663, 84);
  gBounds = path20.getBounds();
  _runPathTest(path20);
  gFillType = path20.fillType;
  allPaths.add(path20);
}

void pathOps21() {
  final Path path21 = Path();
  path21.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path21.getBounds();
  allPaths.add(path21);
}

void pathOps22() {
  final Path path22 = Path();
  path22.reset();
  path22.moveTo(234.66666666666666, 87);
  path22.lineTo(96, 87);
  path22.lineTo(96, 86);
  path22.lineTo(234.66666666666666, 86);
  gBounds = path22.getBounds();
  _runPathTest(path22);
  gFillType = path22.fillType;
  allPaths.add(path22);
}

void pathOps23() {
  final Path path23 = Path();
  path23.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 101), Radius.zero),
  );
  gBounds = path23.getBounds();
  allPaths.add(path23);
}

void pathOps24() {
  final Path path24 = Path();
  path24.reset();
  path24.moveTo(234.66666666666666, 101);
  path24.lineTo(96, 101);
  path24.lineTo(96, 100);
  path24.lineTo(234.66666666666666, 100);
  gBounds = path24.getBounds();
  _runPathTest(path24);
  gFillType = path24.fillType;
  allPaths.add(path24);
}

void pathOps25() {
  final Path path25 = Path();
  path25.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 101), Radius.zero),
  );
  gBounds = path25.getBounds();
  allPaths.add(path25);
}

void pathOps26() {
  final Path path26 = Path();
  path26.reset();
  path26.moveTo(234.66666666666666, 101);
  path26.lineTo(96, 101);
  path26.lineTo(96, 100);
  path26.lineTo(234.66666666666666, 100);
  gBounds = path26.getBounds();
  _runPathTest(path26);
  gFillType = path26.fillType;
  allPaths.add(path26);
}

void pathOps27() {
  final Path path27 = Path();
  path27.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 101), Radius.zero),
  );
  gBounds = path27.getBounds();
  allPaths.add(path27);
}

void pathOps28() {
  final Path path28 = Path();
  path28.reset();
  path28.moveTo(234.66666666666666, 101);
  path28.lineTo(96, 101);
  path28.lineTo(96, 100);
  path28.lineTo(234.66666666666666, 100);
  gBounds = path28.getBounds();
  _runPathTest(path28);
  gFillType = path28.fillType;
  allPaths.add(path28);
}

void pathOps29() {
  final Path path29 = Path();
  path29.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path29.getBounds();
  allPaths.add(path29);
}

void pathOps30() {
  final Path path30 = Path();
  path30.reset();
  path30.moveTo(234.66666666666666, 87);
  path30.lineTo(96, 87);
  path30.lineTo(96, 86);
  path30.lineTo(234.66666666666666, 86);
  gBounds = path30.getBounds();
  allPaths.add(path30);
}

void pathOps31() {
  final Path path31 = Path();
  path31.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path31.getBounds();
  allPaths.add(path31);
}

void pathOps32() {
  final Path path32 = Path();
  path32.reset();
  path32.moveTo(234.66666666666666, 87);
  path32.lineTo(96, 87);
  path32.lineTo(96, 86);
  path32.lineTo(234.66666666666666, 86);
  gBounds = path32.getBounds();
  allPaths.add(path32);
}

void pathOps33() {
  final Path path33 = Path();
  path33.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(0, 0, 250.66666666666666, 585),
      const Radius.circular(10),
    ),
  );
  gFillType = path33.fillType;
  path34 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path137 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path232 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path295 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path349 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path366 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path379 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path392 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path405 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path418 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path431 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path444 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path457 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path470 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path483 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path496 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path509 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path522 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path535 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path548 = path33.shift(const Offset(359.66666666666663, 0));
  gFillType = path33.fillType;
  path561 = path33.shift(const Offset(359.66666666666663, 0));
  allPaths.add(path33);
}

void pathOps34() {
  gBounds = path34.getBounds();
  allPaths.add(path34);
}

void pathOps35() {
  final Path path35 = Path();
  path35.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path35.getBounds();
  allPaths.add(path35);
}

void pathOps36() {
  final Path path36 = Path();
  path36.reset();
  path36.moveTo(610.3333333333333, 86);
  path36.lineTo(359.66666666666663, 86);
  path36.lineTo(359.66666666666663, 84);
  path36.lineTo(610.3333333333333, 84);
  gBounds = path36.getBounds();
  _runPathTest(path36);
  gFillType = path36.fillType;
  allPaths.add(path36);
}

void pathOps37() {
  final Path path37 = Path();
  path37.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path37.getBounds();
  allPaths.add(path37);
}

void pathOps38() {
  final Path path38 = Path();
  path38.reset();
  path38.moveTo(234.66666666666666, 87);
  path38.lineTo(96, 87);
  path38.lineTo(96, 86);
  path38.lineTo(234.66666666666666, 86);
  gBounds = path38.getBounds();
  _runPathTest(path38);
  gFillType = path38.fillType;
  allPaths.add(path38);
}

void pathOps39() {
  final Path path39 = Path();
  path39.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path39.getBounds();
  allPaths.add(path39);
}

void pathOps40() {
  final Path path40 = Path();
  path40.reset();
  path40.moveTo(234.66666666666666, 87);
  path40.lineTo(96, 87);
  path40.lineTo(96, 86);
  path40.lineTo(234.66666666666666, 86);
  gBounds = path40.getBounds();
  _runPathTest(path40);
  gFillType = path40.fillType;
  allPaths.add(path40);
}

void pathOps41() {
  final Path path41 = Path();
  path41.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 73), Radius.zero),
  );
  gBounds = path41.getBounds();
  allPaths.add(path41);
}

void pathOps42() {
  final Path path42 = Path();
  path42.reset();
  path42.moveTo(234.66666666666666, 73);
  path42.lineTo(96, 73);
  path42.lineTo(96, 72);
  path42.lineTo(234.66666666666666, 72);
  gBounds = path42.getBounds();
  _runPathTest(path42);
  gFillType = path42.fillType;
  allPaths.add(path42);
}

void pathOps43() {
  final Path path43 = Path();
  path43.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path43.getBounds();
  allPaths.add(path43);
}

void pathOps44() {
  final Path path44 = Path();
  path44.reset();
  path44.moveTo(234.66666666666666, 87);
  path44.lineTo(96, 87);
  path44.lineTo(96, 86);
  path44.lineTo(234.66666666666666, 86);
  gBounds = path44.getBounds();
  _runPathTest(path44);
  gFillType = path44.fillType;
  allPaths.add(path44);
}

void pathOps45() {
  final Path path45 = Path();
  path45.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path45.getBounds();
  allPaths.add(path45);
}

void pathOps46() {
  final Path path46 = Path();
  path46.reset();
  path46.moveTo(234.66666666666666, 87);
  path46.lineTo(96, 87);
  path46.lineTo(96, 86);
  path46.lineTo(234.66666666666666, 86);
  gBounds = path46.getBounds();
  _runPathTest(path46);
  gFillType = path46.fillType;
  allPaths.add(path46);
}

void pathOps47() {
  final Path path47 = Path();
  path47.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path47.getBounds();
  allPaths.add(path47);
}

void pathOps48() {
  final Path path48 = Path();
  path48.reset();
  path48.moveTo(234.66666666666666, 87);
  path48.lineTo(96, 87);
  path48.lineTo(96, 86);
  path48.lineTo(234.66666666666666, 86);
  gBounds = path48.getBounds();
  allPaths.add(path48);
}

void pathOps49() {
  final Path path49 = Path();
  path49.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(0, 0, 250.66666666666669, 585),
      const Radius.circular(10),
    ),
  );
  gFillType = path49.fillType;
  path50 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path140 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path235 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path298 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path352 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path369 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path382 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path395 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path408 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path421 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path434 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path447 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path460 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path473 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path486 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path499 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path512 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path525 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path538 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path551 = path49.shift(const Offset(638.3333333333333, 0));
  gFillType = path49.fillType;
  path564 = path49.shift(const Offset(638.3333333333333, 0));
  allPaths.add(path49);
}

void pathOps50() {
  gBounds = path50.getBounds();
  allPaths.add(path50);
}

void pathOps51() {
  final Path path51 = Path();
  path51.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path51.getBounds();
  allPaths.add(path51);
}

void pathOps52() {
  final Path path52 = Path();
  path52.reset();
  path52.moveTo(889, 86);
  path52.lineTo(638.3333333333333, 86);
  path52.lineTo(638.3333333333333, 84);
  path52.lineTo(889, 84);
  gBounds = path52.getBounds();
  _runPathTest(path52);
  gFillType = path52.fillType;
  allPaths.add(path52);
}

void pathOps53() {
  final Path path53 = Path();
  path53.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 87), Radius.zero),
  );
  gBounds = path53.getBounds();
  allPaths.add(path53);
}

void pathOps54() {
  final Path path54 = Path();
  path54.reset();
  path54.moveTo(234.66666666666669, 87);
  path54.lineTo(96, 87);
  path54.lineTo(96, 86);
  path54.lineTo(234.66666666666669, 86);
  gBounds = path54.getBounds();
  _runPathTest(path54);
  gFillType = path54.fillType;
  allPaths.add(path54);
}

void pathOps55() {
  final Path path55 = Path();
  path55.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 87), Radius.zero),
  );
  gBounds = path55.getBounds();
  allPaths.add(path55);
}

void pathOps56() {
  final Path path56 = Path();
  path56.reset();
  path56.moveTo(234.66666666666669, 87);
  path56.lineTo(96, 87);
  path56.lineTo(96, 86);
  path56.lineTo(234.66666666666669, 86);
  gBounds = path56.getBounds();
  _runPathTest(path56);
  gFillType = path56.fillType;
  allPaths.add(path56);
}

void pathOps57() {
  final Path path57 = Path();
  path57.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 73), Radius.zero),
  );
  gBounds = path57.getBounds();
  allPaths.add(path57);
}

void pathOps58() {
  final Path path58 = Path();
  path58.reset();
  path58.moveTo(234.66666666666669, 73);
  path58.lineTo(96, 73);
  path58.lineTo(96, 72);
  path58.lineTo(234.66666666666669, 72);
  gBounds = path58.getBounds();
  _runPathTest(path58);
  gFillType = path58.fillType;
  allPaths.add(path58);
}

void pathOps59() {
  final Path path59 = Path();
  path59.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 520, 560), const Radius.circular(40)),
  );
  gFillType = path59.fillType;
  path60 = path59.shift(const Offset(450, 136));
  gFillType = path59.fillType;
  path82 = path59.shift(const Offset(450, 136));
  gFillType = path59.fillType;
  path86 = path59.shift(const Offset(450, 136));
  gFillType = path59.fillType;
  path145 = path59.shift(const Offset(450, 136));
  gFillType = path59.fillType;
  path240 = path59.shift(const Offset(450, 136));
  gFillType = path59.fillType;
  path356 = path59.shift(const Offset(450, 136));
  gFillType = path59.fillType;
  path606 = path59.shift(const Offset(450, 136));
  allPaths.add(path59);
}

void pathOps60() {
  gBounds = path60.getBounds();
  allPaths.add(path60);
}

void pathOps61() {
  final Path path61 = Path();
  path61.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(450, 136, 970, 696), Radius.zero));
  gBounds = path61.getBounds();
  allPaths.add(path61);
}

void pathOps62() {
  final Path path62 = Path();
  path62.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 52), const Radius.circular(10)),
  );
  gFillType = path62.fillType;
  path63 = path62.shift(const Offset(32, 0));
  gFillType = path62.fillType;
  path123 = path62.shift(const Offset(32, 0));
  allPaths.add(path62);
}

void pathOps63() {
  gBounds = path63.getBounds();
  allPaths.add(path63);
}

void pathOps64() {
  final Path path64 = Path();
  path64.reset();
  path64.moveTo(56, 40);
  path64.lineTo(56, 40);
  path64.lineTo(58, 40);
  path64.lineTo(58, 40);
  gBounds = path64.getBounds();
  allPaths.add(path64);
}

void pathOps65() {
  final Path path65 = Path();
  path65.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 52), const Radius.circular(10)),
  );
  gFillType = path65.fillType;
  path66 = path65.shift(const Offset(32, 0));
  gFillType = path65.fillType;
  path125 = path65.shift(const Offset(32, 0));
  allPaths.add(path65);
}

void pathOps66() {
  gBounds = path66.getBounds();
  allPaths.add(path66);
}

void pathOps67() {
  final Path path67 = Path();
  path67.reset();
  path67.moveTo(56, 40);
  path67.lineTo(56, 40);
  path67.lineTo(58, 40);
  path67.lineTo(58, 40);
  gBounds = path67.getBounds();
  allPaths.add(path67);
}

void pathOps68() {
  final Path path68 = Path();
  path68.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 52), const Radius.circular(10)),
  );
  gFillType = path68.fillType;
  path69 = path68.shift(const Offset(32, 0));
  gFillType = path68.fillType;
  path127 = path68.shift(const Offset(32, 0));
  allPaths.add(path68);
}

void pathOps69() {
  gBounds = path69.getBounds();
  allPaths.add(path69);
}

void pathOps70() {
  final Path path70 = Path();
  path70.reset();
  path70.moveTo(56, 40);
  path70.lineTo(56, 40);
  path70.lineTo(58, 40);
  path70.lineTo(58, 40);
  gBounds = path70.getBounds();
  allPaths.add(path70);
}

void pathOps71() {
  final Path path71 = Path();
  path71.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 52), const Radius.circular(10)),
  );
  gFillType = path71.fillType;
  path72 = path71.shift(const Offset(32, 0));
  gFillType = path71.fillType;
  path129 = path71.shift(const Offset(32, 0));
  allPaths.add(path71);
}

void pathOps72() {
  gBounds = path72.getBounds();
  allPaths.add(path72);
}

void pathOps73() {
  final Path path73 = Path();
  path73.reset();
  path73.moveTo(56, 40);
  path73.lineTo(56, 40);
  path73.lineTo(58, 40);
  path73.lineTo(58, 40);
  gBounds = path73.getBounds();
  allPaths.add(path73);
}

void pathOps74() {
  final Path path74 = Path();
  path74.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 52), const Radius.circular(10)),
  );
  gFillType = path74.fillType;
  path75 = path74.shift(const Offset(32, 0));
  gFillType = path74.fillType;
  path132 = path74.shift(const Offset(32, 0));
  allPaths.add(path74);
}

void pathOps75() {
  gBounds = path75.getBounds();
  allPaths.add(path75);
}

void pathOps76() {
  final Path path76 = Path();
  path76.reset();
  path76.moveTo(56, 40);
  path76.lineTo(56, 40);
  path76.lineTo(58, 40);
  path76.lineTo(58, 40);
  gBounds = path76.getBounds();
  allPaths.add(path76);
}

void pathOps77() {
  final Path path77 = Path();
  path77.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 50), const Radius.circular(10)),
  );
  gFillType = path77.fillType;
  path78 = path77.shift(const Offset(32, 0));
  gFillType = path77.fillType;
  path131 = path77.shift(const Offset(32, 0));
  allPaths.add(path77);
}

void pathOps78() {
  gBounds = path78.getBounds();
  allPaths.add(path78);
}

void pathOps79() {
  final Path path79 = Path();
  path79.addRRect(
    RRect.fromRectAndCorners(
      const Rect.fromLTRB(0, 0, 64, 56),
      bottomLeft: const Radius.circular(10),
    ),
  );
  gFillType = path79.fillType;
  path80 = path79.shift(const Offset(906, 136));
  gFillType = path79.fillType;
  path84 = path79.shift(const Offset(906, 136));
  gFillType = path79.fillType;
  path88 = path79.shift(const Offset(906, 136));
  gFillType = path79.fillType;
  path147 = path79.shift(const Offset(906, 136));
  gFillType = path79.fillType;
  path242 = path79.shift(const Offset(906, 136));
  gFillType = path79.fillType;
  path358 = path79.shift(const Offset(906, 136));
  gFillType = path79.fillType;
  path608 = path79.shift(const Offset(906, 136));
  allPaths.add(path79);
}

void pathOps80() {
  gBounds = path80.getBounds();
  allPaths.add(path80);
}

void pathOps81() {
  final Path path81 = Path();
  path81.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(0, 136, 970, 1144), Radius.zero));
  gBounds = path81.getBounds();
  allPaths.add(path81);
}

void pathOps82() {
  gBounds = path82.getBounds();
  allPaths.add(path82);
}

void pathOps83() {
  final Path path83 = Path();
  path83.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(450, 136, 970, 696), Radius.zero));
  gBounds = path83.getBounds();
  allPaths.add(path83);
}

void pathOps84() {
  gBounds = path84.getBounds();
  allPaths.add(path84);
}

void pathOps85() {
  final Path path85 = Path();
  path85.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(0, 136, 970, 1144), Radius.zero));
  gBounds = path85.getBounds();
  allPaths.add(path85);
}

void pathOps86() {
  gBounds = path86.getBounds();
  allPaths.add(path86);
}

void pathOps87() {
  final Path path87 = Path();
  path87.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(450, 136, 970, 696), Radius.zero));
  gBounds = path87.getBounds();
  allPaths.add(path87);
}

void pathOps88() {
  gBounds = path88.getBounds();
  allPaths.add(path88);
}

void pathOps89() {
  final Path path89 = Path();
  path89.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 87), Radius.zero),
  );
  gBounds = path89.getBounds();
  allPaths.add(path89);
}

void pathOps90() {
  final Path path90 = Path();
  path90.reset();
  path90.moveTo(234.66666666666669, 87);
  path90.lineTo(96, 87);
  path90.lineTo(96, 86);
  path90.lineTo(234.66666666666669, 86);
  gBounds = path90.getBounds();
  _runPathTest(path90);
  gFillType = path90.fillType;
  allPaths.add(path90);
}

void pathOps91() {
  final Path path91 = Path();
  path91.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 73), Radius.zero),
  );
  gBounds = path91.getBounds();
  allPaths.add(path91);
}

void pathOps92() {
  final Path path92 = Path();
  path92.reset();
  path92.moveTo(234.66666666666669, 73);
  path92.lineTo(96, 73);
  path92.lineTo(96, 72);
  path92.lineTo(234.66666666666669, 72);
  gBounds = path92.getBounds();
  _runPathTest(path92);
  gFillType = path92.fillType;
  allPaths.add(path92);
}

void pathOps93() {
  final Path path93 = Path();
  path93.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 101), Radius.zero),
  );
  gBounds = path93.getBounds();
  allPaths.add(path93);
}

void pathOps94() {
  final Path path94 = Path();
  path94.reset();
  path94.moveTo(234.66666666666666, 101);
  path94.lineTo(96, 101);
  path94.lineTo(96, 100);
  path94.lineTo(234.66666666666666, 100);
  gBounds = path94.getBounds();
  _runPathTest(path94);
  gFillType = path94.fillType;
  allPaths.add(path94);
}

void pathOps95() {
  final Path path95 = Path();
  path95.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path95.getBounds();
  allPaths.add(path95);
}

void pathOps96() {
  final Path path96 = Path();
  path96.reset();
  path96.moveTo(234.66666666666666, 87);
  path96.lineTo(96, 87);
  path96.lineTo(96, 86);
  path96.lineTo(234.66666666666666, 86);
  gBounds = path96.getBounds();
  _runPathTest(path96);
  gFillType = path96.fillType;
  allPaths.add(path96);
}

void pathOps97() {
  final Path path97 = Path();
  path97.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 101), Radius.zero),
  );
  gBounds = path97.getBounds();
  allPaths.add(path97);
}

void pathOps98() {
  final Path path98 = Path();
  path98.reset();
  path98.moveTo(234.66666666666666, 101);
  path98.lineTo(96, 101);
  path98.lineTo(96, 100);
  path98.lineTo(234.66666666666666, 100);
  gBounds = path98.getBounds();
  _runPathTest(path98);
  gFillType = path98.fillType;
  allPaths.add(path98);
}

void pathOps99() {
  final Path path99 = Path();
  path99.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 101), Radius.zero),
  );
  gBounds = path99.getBounds();
  allPaths.add(path99);
}

void pathOps100() {
  final Path path100 = Path();
  path100.reset();
  path100.moveTo(234.66666666666666, 101);
  path100.lineTo(96, 101);
  path100.lineTo(96, 100);
  path100.lineTo(234.66666666666666, 100);
  gBounds = path100.getBounds();
  _runPathTest(path100);
  gFillType = path100.fillType;
  allPaths.add(path100);
}

void pathOps101() {
  final Path path101 = Path();
  path101.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 73), Radius.zero),
  );
  gBounds = path101.getBounds();
  allPaths.add(path101);
}

void pathOps102() {
  final Path path102 = Path();
  path102.reset();
  path102.moveTo(234.66666666666666, 73);
  path102.lineTo(96, 73);
  path102.lineTo(96, 72);
  path102.lineTo(234.66666666666666, 72);
  gBounds = path102.getBounds();
  _runPathTest(path102);
  gFillType = path102.fillType;
  allPaths.add(path102);
}

void pathOps103() {
  final Path path103 = Path();
  path103.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path103.getBounds();
  allPaths.add(path103);
}

void pathOps104() {
  final Path path104 = Path();
  path104.reset();
  path104.moveTo(234.66666666666666, 87);
  path104.lineTo(96, 87);
  path104.lineTo(96, 86);
  path104.lineTo(234.66666666666666, 86);
  gBounds = path104.getBounds();
  allPaths.add(path104);
}

void pathOps105() {
  final Path path105 = Path();
  path105.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path105.getBounds();
  allPaths.add(path105);
}

void pathOps106() {
  final Path path106 = Path();
  path106.reset();
  path106.moveTo(234.66666666666666, 87);
  path106.lineTo(96, 87);
  path106.lineTo(96, 86);
  path106.lineTo(234.66666666666666, 86);
  gBounds = path106.getBounds();
  allPaths.add(path106);
}

void pathOps107() {
  final Path path107 = Path();
  path107.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path107.getBounds();
  allPaths.add(path107);
}

void pathOps108() {
  final Path path108 = Path();
  path108.reset();
  path108.moveTo(234.66666666666666, 87);
  path108.lineTo(96, 87);
  path108.lineTo(96, 86);
  path108.lineTo(234.66666666666666, 86);
  gBounds = path108.getBounds();
  _runPathTest(path108);
  gFillType = path108.fillType;
  allPaths.add(path108);
}

void pathOps109() {
  final Path path109 = Path();
  path109.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path109.getBounds();
  allPaths.add(path109);
}

void pathOps110() {
  final Path path110 = Path();
  path110.reset();
  path110.moveTo(234.66666666666666, 87);
  path110.lineTo(96, 87);
  path110.lineTo(96, 86);
  path110.lineTo(234.66666666666666, 86);
  gBounds = path110.getBounds();
  _runPathTest(path110);
  gFillType = path110.fillType;
  allPaths.add(path110);
}

void pathOps111() {
  final Path path111 = Path();
  path111.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path111.getBounds();
  allPaths.add(path111);
}

void pathOps112() {
  final Path path112 = Path();
  path112.reset();
  path112.moveTo(234.66666666666666, 87);
  path112.lineTo(96, 87);
  path112.lineTo(96, 86);
  path112.lineTo(234.66666666666666, 86);
  gBounds = path112.getBounds();
  _runPathTest(path112);
  gFillType = path112.fillType;
  allPaths.add(path112);
}

void pathOps113() {
  final Path path113 = Path();
  path113.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path113.getBounds();
  allPaths.add(path113);
}

void pathOps114() {
  final Path path114 = Path();
  path114.reset();
  path114.moveTo(234.66666666666666, 87);
  path114.lineTo(96, 87);
  path114.lineTo(96, 86);
  path114.lineTo(234.66666666666666, 86);
  gBounds = path114.getBounds();
  _runPathTest(path114);
  gFillType = path114.fillType;
  allPaths.add(path114);
}

void pathOps115() {
  final Path path115 = Path();
  path115.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 87), Radius.zero),
  );
  gBounds = path115.getBounds();
  allPaths.add(path115);
}

void pathOps116() {
  final Path path116 = Path();
  path116.reset();
  path116.moveTo(234.66666666666666, 87);
  path116.lineTo(96, 87);
  path116.lineTo(96, 86);
  path116.lineTo(234.66666666666666, 86);
  gBounds = path116.getBounds();
  allPaths.add(path116);
}

void pathOps117() {
  final Path path117 = Path();
  path117.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 87), Radius.zero),
  );
  gBounds = path117.getBounds();
  allPaths.add(path117);
}

void pathOps118() {
  final Path path118 = Path();
  path118.reset();
  path118.moveTo(234.66666666666669, 87);
  path118.lineTo(96, 87);
  path118.lineTo(96, 86);
  path118.lineTo(234.66666666666669, 86);
  gBounds = path118.getBounds();
  _runPathTest(path118);
  gFillType = path118.fillType;
  allPaths.add(path118);
}

void pathOps119() {
  gBounds = path119.getBounds();
  allPaths.add(path119);
}

void pathOps120() {
  gBounds = path120.getBounds();
  allPaths.add(path120);
}

void pathOps121() {
  gBounds = path121.getBounds();
  allPaths.add(path121);
}

void pathOps122() {
  gBounds = path122.getBounds();
  allPaths.add(path122);
}

void pathOps123() {
  gBounds = path123.getBounds();
  allPaths.add(path123);
}

void pathOps124() {
  final Path path124 = Path();
  path124.reset();
  path124.moveTo(56, 40);
  path124.lineTo(56, 40);
  path124.lineTo(58, 40);
  path124.lineTo(58, 40);
  gBounds = path124.getBounds();
  allPaths.add(path124);
}

void pathOps125() {
  gBounds = path125.getBounds();
  allPaths.add(path125);
}

void pathOps126() {
  final Path path126 = Path();
  path126.reset();
  path126.moveTo(56, 40);
  path126.lineTo(56, 40);
  path126.lineTo(58, 40);
  path126.lineTo(58, 40);
  gBounds = path126.getBounds();
  allPaths.add(path126);
}

void pathOps127() {
  gBounds = path127.getBounds();
  allPaths.add(path127);
}

void pathOps128() {
  final Path path128 = Path();
  path128.reset();
  path128.moveTo(56, 40);
  path128.lineTo(56, 40);
  path128.lineTo(58, 40);
  path128.lineTo(58, 40);
  gBounds = path128.getBounds();
  allPaths.add(path128);
}

void pathOps129() {
  gBounds = path129.getBounds();
  allPaths.add(path129);
}

void pathOps130() {
  final Path path130 = Path();
  path130.reset();
  path130.moveTo(56, 40);
  path130.lineTo(56, 40);
  path130.lineTo(58, 40);
  path130.lineTo(58, 40);
  gBounds = path130.getBounds();
  allPaths.add(path130);
}

void pathOps131() {
  gBounds = path131.getBounds();
  allPaths.add(path131);
}

void pathOps132() {
  gBounds = path132.getBounds();
  allPaths.add(path132);
}

void pathOps133() {
  final Path path133 = Path();
  path133.reset();
  path133.moveTo(56, 40);
  path133.lineTo(56, 40);
  path133.lineTo(58, 40);
  path133.lineTo(58, 40);
  gBounds = path133.getBounds();
  allPaths.add(path133);
}

void pathOps134() {
  gBounds = path134.getBounds();
  allPaths.add(path134);
}

void pathOps135() {
  final Path path135 = Path();
  path135.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path135.getBounds();
  allPaths.add(path135);
}

void pathOps136() {
  final Path path136 = Path();
  path136.reset();
  path136.moveTo(331.66666666666663, 86);
  path136.lineTo(81, 86);
  path136.lineTo(81, 84);
  path136.lineTo(331.66666666666663, 84);
  gBounds = path136.getBounds();
  _runPathTest(path136);
  gFillType = path136.fillType;
  allPaths.add(path136);
}

void pathOps137() {
  gBounds = path137.getBounds();
  allPaths.add(path137);
}

void pathOps138() {
  final Path path138 = Path();
  path138.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path138.getBounds();
  allPaths.add(path138);
}

void pathOps139() {
  final Path path139 = Path();
  path139.reset();
  path139.moveTo(610.3333333333333, 86);
  path139.lineTo(359.66666666666663, 86);
  path139.lineTo(359.66666666666663, 84);
  path139.lineTo(610.3333333333333, 84);
  gBounds = path139.getBounds();
  _runPathTest(path139);
  gFillType = path139.fillType;
  allPaths.add(path139);
}

void pathOps140() {
  gBounds = path140.getBounds();
  allPaths.add(path140);
}

void pathOps141() {
  final Path path141 = Path();
  path141.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path141.getBounds();
  allPaths.add(path141);
}

void pathOps142() {
  final Path path142 = Path();
  path142.reset();
  path142.moveTo(889, 86);
  path142.lineTo(638.3333333333333, 86);
  path142.lineTo(638.3333333333333, 84);
  path142.lineTo(889, 84);
  gBounds = path142.getBounds();
  _runPathTest(path142);
  gFillType = path142.fillType;
  allPaths.add(path142);
}

void pathOps143() {
  gBounds = path143.getBounds();
  allPaths.add(path143);
}

void pathOps144() {
  final Path path144 = Path();
  path144.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(0, 136, 970, 1144), Radius.zero));
  gBounds = path144.getBounds();
  allPaths.add(path144);
}

void pathOps145() {
  gBounds = path145.getBounds();
  allPaths.add(path145);
}

void pathOps146() {
  final Path path146 = Path();
  path146.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(450, 136, 970, 696), Radius.zero));
  gBounds = path146.getBounds();
  allPaths.add(path146);
}

void pathOps147() {
  gBounds = path147.getBounds();
  allPaths.add(path147);
}

void pathOps149() {
  final Path path149 = Path();
  path149.reset();
  path149.moveTo(-2, 0);
  path149.cubicTo(-2, -1.100000023841858, -1.100000023841858, -2, 0, -2);
  path149.cubicTo(1.100000023841858, -2, 2, -1.100000023841858, 2, 0);
  path149.cubicTo(2, 1.100000023841858, 1.100000023841858, 2, 0, 2);
  path149.cubicTo(-1.100000023841858, 2, -2, 1.100000023841858, -2, 0);
  allPaths.add(path149);
}

void pathOps150() {
  final Path path150 = Path();
  path150.reset();
  path150.fillType = PathFillType.nonZero;
  gBounds = path150.getBounds();
  _runPathTest(path150);
  gFillType = path150.fillType;
  path150.fillType = PathFillType.nonZero;
  gBounds = path150.getBounds();
  _runPathTest(path150);
  gFillType = path150.fillType;
  path150.fillType = PathFillType.nonZero;
  gBounds = path150.getBounds();
  allPaths.add(path150);
}

void pathOps155() {
  final Path path155 = Path();
  path155.reset();
  path155.moveTo(-3, -9);
  path155.cubicTo(-3, -10.649999618530273, -1.649999976158142, -12, 0, -12);
  path155.cubicTo(0, -12, 0, -12, 0, -12);
  path155.cubicTo(1.649999976158142, -12, 3, -10.649999618530273, 3, -9);
  path155.cubicTo(3, -9, 3, 9, 3, 9);
  path155.cubicTo(3, 10.649999618530273, 1.649999976158142, 12, 0, 12);
  path155.cubicTo(0, 12, 0, 12, 0, 12);
  path155.cubicTo(-1.649999976158142, 12, -3, 10.649999618530273, -3, 9);
  path155.cubicTo(-3, 9, -3, -9, -3, -9);
  path155.close();
  allPaths.add(path155);
}

void pathOps156() {
  final Path path156 = Path();
  path156.reset();
  path156.fillType = PathFillType.nonZero;
  gBounds = path156.getBounds();
  _runPathTest(path156);
  gFillType = path156.fillType;
  path156.fillType = PathFillType.nonZero;
  gBounds = path156.getBounds();
  _runPathTest(path156);
  gFillType = path156.fillType;
  path156.fillType = PathFillType.nonZero;
  gBounds = path156.getBounds();
  allPaths.add(path156);
}

void pathOps161() {
  final Path path161 = Path();
  path161.reset();
  path161.moveTo(-2, -10);
  path161.cubicTo(-2, -11.100000381469727, -1.100000023841858, -12, 0, -12);
  path161.cubicTo(0, -12, 0, -12, 0, -12);
  path161.cubicTo(1.100000023841858, -12, 2, -11.100000381469727, 2, -10);
  path161.cubicTo(2, -10, 2, 10, 2, 10);
  path161.cubicTo(2, 11.100000381469727, 1.100000023841858, 12, 0, 12);
  path161.cubicTo(0, 12, 0, 12, 0, 12);
  path161.cubicTo(-1.100000023841858, 12, -2, 11.100000381469727, -2, 10);
  path161.cubicTo(-2, 10, -2, -10, -2, -10);
  path161.close();
  allPaths.add(path161);
}

void pathOps162() {
  final Path path162 = Path();
  path162.reset();
  path162.fillType = PathFillType.nonZero;
  gBounds = path162.getBounds();
  _runPathTest(path162);
  gFillType = path162.fillType;
  path162.fillType = PathFillType.nonZero;
  gBounds = path162.getBounds();
  _runPathTest(path162);
  gFillType = path162.fillType;
  path162.fillType = PathFillType.nonZero;
  gBounds = path162.getBounds();
  allPaths.add(path162);
}

void pathOps164() {
  final Path path164 = Path();
  path164.reset();
  path164.moveTo(-3, -9);
  path164.cubicTo(-3, -10.649999618530273, -1.649999976158142, -12, 0, -12);
  path164.cubicTo(0, -12, 0, -12, 0, -12);
  path164.cubicTo(1.649999976158142, -12, 3, -10.649999618530273, 3, -9);
  path164.cubicTo(3, -9, 3, 9, 3, 9);
  path164.cubicTo(3, 10.649999618530273, 1.649999976158142, 12, 0, 12);
  path164.cubicTo(0, 12, 0, 12, 0, 12);
  path164.cubicTo(-1.649999976158142, 12, -3, 10.649999618530273, -3, 9);
  path164.cubicTo(-3, 9, -3, -9, -3, -9);
  path164.close();
  allPaths.add(path164);
}

void pathOps165() {
  final Path path165 = Path();
  path165.reset();
  path165.fillType = PathFillType.nonZero;
  gBounds = path165.getBounds();
  _runPathTest(path165);
  gFillType = path165.fillType;
  path165.fillType = PathFillType.nonZero;
  gBounds = path165.getBounds();
  _runPathTest(path165);
  gFillType = path165.fillType;
  path165.fillType = PathFillType.nonZero;
  gBounds = path165.getBounds();
  allPaths.add(path165);
}

void pathOps167() {
  final Path path167 = Path();
  path167.reset();
  path167.moveTo(2, 0);
  path167.cubicTo(2, 1.1100000143051147, 1.100000023841858, 2, 0, 2);
  path167.cubicTo(-1.1100000143051147, 2, -2, 1.1100000143051147, -2, 0);
  path167.cubicTo(-2, -1.100000023841858, -1.1100000143051147, -2, 0, -2);
  path167.cubicTo(1.100000023841858, -2, 2, -1.100000023841858, 2, 0);
  allPaths.add(path167);
}

void pathOps168() {
  final Path path168 = Path();
  path168.reset();
  path168.fillType = PathFillType.nonZero;
  gBounds = path168.getBounds();
  _runPathTest(path168);
  gFillType = path168.fillType;
  path168.fillType = PathFillType.nonZero;
  gBounds = path168.getBounds();
  _runPathTest(path168);
  gFillType = path168.fillType;
  path168.fillType = PathFillType.nonZero;
  gBounds = path168.getBounds();
  allPaths.add(path168);
}

void pathOps173() {
  final Path path173 = Path();
  path173.reset();
  path173.moveTo(-2, -10);
  path173.cubicTo(-2, -11.100000381469727, -1.100000023841858, -12, 0, -12);
  path173.cubicTo(0, -12, 0, -12, 0, -12);
  path173.cubicTo(1.100000023841858, -12, 2, -11.100000381469727, 2, -10);
  path173.cubicTo(2, -10, 2, 10, 2, 10);
  path173.cubicTo(2, 11.100000381469727, 1.100000023841858, 12, 0, 12);
  path173.cubicTo(0, 12, 0, 12, 0, 12);
  path173.cubicTo(-1.100000023841858, 12, -2, 11.100000381469727, -2, 10);
  path173.cubicTo(-2, 10, -2, -10, -2, -10);
  path173.close();
  allPaths.add(path173);
}

void pathOps174() {
  final Path path174 = Path();
  path174.reset();
  path174.fillType = PathFillType.nonZero;
  gBounds = path174.getBounds();
  _runPathTest(path174);
  gFillType = path174.fillType;
  path174.fillType = PathFillType.nonZero;
  gBounds = path174.getBounds();
  _runPathTest(path174);
  gFillType = path174.fillType;
  path174.fillType = PathFillType.nonZero;
  gBounds = path174.getBounds();
  allPaths.add(path174);
}

void pathOps178() {
  final Path path178 = Path();
  path178.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 105), Radius.zero),
  );
  gBounds = path178.getBounds();
  allPaths.add(path178);
}

void pathOps179() {
  final Path path179 = Path();
  path179.reset();
  path179.moveTo(234.66666666666669, 105);
  path179.lineTo(96, 105);
  path179.lineTo(96, 104);
  path179.lineTo(234.66666666666669, 104);
  gBounds = path179.getBounds();
  _runPathTest(path179);
  gFillType = path179.fillType;
  allPaths.add(path179);
}

void pathOps180() {
  final Path path180 = Path();
  path180.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 94), Radius.zero),
  );
  gBounds = path180.getBounds();
  allPaths.add(path180);
}

void pathOps181() {
  final Path path181 = Path();
  path181.reset();
  path181.moveTo(234.66666666666669, 94);
  path181.lineTo(96, 94);
  path181.lineTo(96, 93);
  path181.lineTo(234.66666666666669, 93);
  gBounds = path181.getBounds();
  _runPathTest(path181);
  gFillType = path181.fillType;
  allPaths.add(path181);
}

void pathOps182() {
  final Path path182 = Path();
  path182.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 105), Radius.zero),
  );
  gBounds = path182.getBounds();
  allPaths.add(path182);
}

void pathOps183() {
  final Path path183 = Path();
  path183.reset();
  path183.moveTo(234.66666666666666, 105);
  path183.lineTo(96, 105);
  path183.lineTo(96, 104);
  path183.lineTo(234.66666666666666, 104);
  gBounds = path183.getBounds();
  allPaths.add(path183);
}

void pathOps184() {
  final Path path184 = Path();
  path184.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 105), Radius.zero),
  );
  gBounds = path184.getBounds();
  allPaths.add(path184);
}

void pathOps185() {
  final Path path185 = Path();
  path185.reset();
  path185.moveTo(234.66666666666666, 105);
  path185.lineTo(96, 105);
  path185.lineTo(96, 104);
  path185.lineTo(234.66666666666666, 104);
  gBounds = path185.getBounds();
  _runPathTest(path185);
  gFillType = path185.fillType;
  allPaths.add(path185);
}

void pathOps186() {
  final Path path186 = Path();
  path186.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path186.getBounds();
  allPaths.add(path186);
}

void pathOps187() {
  final Path path187 = Path();
  path187.reset();
  path187.moveTo(234.66666666666666, 120);
  path187.lineTo(96, 120);
  path187.lineTo(96, 119);
  path187.lineTo(234.66666666666666, 119);
  gBounds = path187.getBounds();
  _runPathTest(path187);
  gFillType = path187.fillType;
  allPaths.add(path187);
}

void pathOps188() {
  final Path path188 = Path();
  path188.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 139), Radius.zero),
  );
  gBounds = path188.getBounds();
  allPaths.add(path188);
}

void pathOps189() {
  final Path path189 = Path();
  path189.reset();
  path189.moveTo(234.66666666666666, 139);
  path189.lineTo(96, 139);
  path189.lineTo(96, 138);
  path189.lineTo(234.66666666666666, 138);
  gBounds = path189.getBounds();
  _runPathTest(path189);
  gFillType = path189.fillType;
  allPaths.add(path189);
}

void pathOps190() {
  final Path path190 = Path();
  path190.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 75), Radius.zero),
  );
  gBounds = path190.getBounds();
  allPaths.add(path190);
}

void pathOps191() {
  final Path path191 = Path();
  path191.reset();
  path191.moveTo(234.66666666666666, 75);
  path191.lineTo(96, 75);
  path191.lineTo(96, 74);
  path191.lineTo(234.66666666666666, 74);
  gBounds = path191.getBounds();
  _runPathTest(path191);
  gFillType = path191.fillType;
  allPaths.add(path191);
}

void pathOps192() {
  final Path path192 = Path();
  path192.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path192.getBounds();
  allPaths.add(path192);
}

void pathOps193() {
  final Path path193 = Path();
  path193.reset();
  path193.moveTo(234.66666666666666, 90);
  path193.lineTo(96, 90);
  path193.lineTo(96, 89);
  path193.lineTo(234.66666666666666, 89);
  gBounds = path193.getBounds();
  allPaths.add(path193);
}

void pathOps194() {
  final Path path194 = Path();
  path194.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 105), Radius.zero),
  );
  allPaths.add(path194);
}

void pathOps195() {
  final Path path195 = Path();
  path195.reset();
  path195.moveTo(234.66666666666666, 105);
  path195.lineTo(96, 105);
  path195.lineTo(96, 104);
  path195.lineTo(234.66666666666666, 104);
  gBounds = path195.getBounds();
  allPaths.add(path195);
}

void pathOps196() {
  final Path path196 = Path();
  path196.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path196.getBounds();
  allPaths.add(path196);
}

void pathOps197() {
  final Path path197 = Path();
  path197.reset();
  path197.moveTo(234.66666666666666, 90);
  path197.lineTo(96, 90);
  path197.lineTo(96, 89);
  path197.lineTo(234.66666666666666, 89);
  gBounds = path197.getBounds();
  _runPathTest(path197);
  gFillType = path197.fillType;
  allPaths.add(path197);
}

void pathOps198() {
  final Path path198 = Path();
  path198.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path198.getBounds();
  allPaths.add(path198);
}

void pathOps199() {
  final Path path199 = Path();
  path199.reset();
  path199.moveTo(234.66666666666666, 90);
  path199.lineTo(96, 90);
  path199.lineTo(96, 89);
  path199.lineTo(234.66666666666666, 89);
  gBounds = path199.getBounds();
  _runPathTest(path199);
  gFillType = path199.fillType;
  allPaths.add(path199);
}

void pathOps200() {
  final Path path200 = Path();
  path200.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path200.getBounds();
  allPaths.add(path200);
}

void pathOps201() {
  final Path path201 = Path();
  path201.reset();
  path201.moveTo(234.66666666666666, 90);
  path201.lineTo(96, 90);
  path201.lineTo(96, 89);
  path201.lineTo(234.66666666666666, 89);
  gBounds = path201.getBounds();
  _runPathTest(path201);
  gFillType = path201.fillType;
  allPaths.add(path201);
}

void pathOps202() {
  final Path path202 = Path();
  path202.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path202.getBounds();
  allPaths.add(path202);
}

void pathOps203() {
  final Path path203 = Path();
  path203.reset();
  path203.moveTo(234.66666666666666, 90);
  path203.lineTo(96, 90);
  path203.lineTo(96, 89);
  path203.lineTo(234.66666666666666, 89);
  gBounds = path203.getBounds();
  _runPathTest(path203);
  gFillType = path203.fillType;
  allPaths.add(path203);
}

void pathOps204() {
  final Path path204 = Path();
  path204.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path204.getBounds();
  allPaths.add(path204);
}

void pathOps205() {
  final Path path205 = Path();
  path205.reset();
  path205.moveTo(234.66666666666666, 90);
  path205.lineTo(96, 90);
  path205.lineTo(96, 89);
  path205.lineTo(234.66666666666666, 89);
  gBounds = path205.getBounds();
  allPaths.add(path205);
}

void pathOps206() {
  final Path path206 = Path();
  path206.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 90), Radius.zero),
  );
  gBounds = path206.getBounds();
  allPaths.add(path206);
}

void pathOps207() {
  final Path path207 = Path();
  path207.reset();
  path207.moveTo(234.66666666666669, 90);
  path207.lineTo(96, 90);
  path207.lineTo(96, 89);
  path207.lineTo(234.66666666666669, 89);
  gBounds = path207.getBounds();
  _runPathTest(path207);
  gFillType = path207.fillType;
  allPaths.add(path207);
}

void pathOps208() {
  gBounds = path208.getBounds();
  allPaths.add(path208);
}

void pathOps209() {
  gBounds = path209.getBounds();
  allPaths.add(path209);
}

void pathOps210() {
  gBounds = path210.getBounds();
  allPaths.add(path210);
}

void pathOps211() {
  gBounds = path211.getBounds();
  allPaths.add(path211);
}

void pathOps212() {
  final Path path212 = Path();
  path212.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 54), const Radius.circular(10)),
  );
  gFillType = path212.fillType;
  path213 = path212.shift(const Offset(32, 0));
  gFillType = path212.fillType;
  path290 = path212.shift(const Offset(32, 0));
  gFillType = path212.fillType;
  path334 = path212.shift(const Offset(32, 0));
  gFillType = path212.fillType;
  path595 = path212.shift(const Offset(32, 0));
  allPaths.add(path212);
}

void pathOps213() {
  gBounds = path213.getBounds();
  allPaths.add(path213);
}

void pathOps214() {
  final Path path214 = Path();
  path214.reset();
  path214.moveTo(56, 42);
  path214.lineTo(56, 42);
  path214.lineTo(58, 42);
  path214.lineTo(58, 42);
  gBounds = path214.getBounds();
  allPaths.add(path214);
}

void pathOps215() {
  final Path path215 = Path();
  path215.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 54), const Radius.circular(10)),
  );
  gFillType = path215.fillType;
  path216 = path215.shift(const Offset(32, 0));
  gFillType = path215.fillType;
  path288 = path215.shift(const Offset(32, 0));
  gFillType = path215.fillType;
  path336 = path215.shift(const Offset(32, 0));
  gFillType = path215.fillType;
  path597 = path215.shift(const Offset(32, 0));
  allPaths.add(path215);
}

void pathOps216() {
  gBounds = path216.getBounds();
  allPaths.add(path216);
}

void pathOps217() {
  final Path path217 = Path();
  path217.reset();
  path217.moveTo(56, 42);
  path217.lineTo(56, 42);
  path217.lineTo(58, 42);
  path217.lineTo(58, 42);
  gBounds = path217.getBounds();
  allPaths.add(path217);
}

void pathOps218() {
  final Path path218 = Path();
  path218.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 54), const Radius.circular(10)),
  );
  gFillType = path218.fillType;
  path219 = path218.shift(const Offset(32, 0));
  gFillType = path218.fillType;
  path286 = path218.shift(const Offset(32, 0));
  gFillType = path218.fillType;
  path338 = path218.shift(const Offset(32, 0));
  gFillType = path218.fillType;
  path599 = path218.shift(const Offset(32, 0));
  allPaths.add(path218);
}

void pathOps219() {
  gBounds = path219.getBounds();
  allPaths.add(path219);
}

void pathOps220() {
  final Path path220 = Path();
  path220.reset();
  path220.moveTo(56, 42);
  path220.lineTo(56, 42);
  path220.lineTo(58, 42);
  path220.lineTo(58, 42);
  gBounds = path220.getBounds();
  allPaths.add(path220);
}

void pathOps221() {
  final Path path221 = Path();
  path221.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 54), const Radius.circular(10)),
  );
  gFillType = path221.fillType;
  path222 = path221.shift(const Offset(32, 0));
  gFillType = path221.fillType;
  path284 = path221.shift(const Offset(32, 0));
  gFillType = path221.fillType;
  path340 = path221.shift(const Offset(32, 0));
  gFillType = path221.fillType;
  path601 = path221.shift(const Offset(32, 0));
  allPaths.add(path221);
}

void pathOps222() {
  gBounds = path222.getBounds();
  allPaths.add(path222);
}

void pathOps223() {
  final Path path223 = Path();
  path223.reset();
  path223.moveTo(56, 42);
  path223.lineTo(56, 42);
  path223.lineTo(58, 42);
  path223.lineTo(58, 42);
  gBounds = path223.getBounds();
  allPaths.add(path223);
}

void pathOps224() {
  final Path path224 = Path();
  path224.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 51), const Radius.circular(10)),
  );
  gFillType = path224.fillType;
  path225 = path224.shift(const Offset(32, 0));
  gFillType = path224.fillType;
  path281 = path224.shift(const Offset(32, 0));
  gFillType = path224.fillType;
  path344 = path224.shift(const Offset(32, 0));
  allPaths.add(path224);
}

void pathOps225() {
  gBounds = path225.getBounds();
  allPaths.add(path225);
}

void pathOps226() {
  final Path path226 = Path();
  path226.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 456, 54), const Radius.circular(10)),
  );
  gFillType = path226.fillType;
  path227 = path226.shift(const Offset(32, 0));
  gFillType = path226.fillType;
  path282 = path226.shift(const Offset(32, 0));
  gFillType = path226.fillType;
  path342 = path226.shift(const Offset(32, 0));
  gFillType = path226.fillType;
  path603 = path226.shift(const Offset(32, 0));
  allPaths.add(path226);
}

void pathOps227() {
  gBounds = path227.getBounds();
  allPaths.add(path227);
}

void pathOps228() {
  final Path path228 = Path();
  path228.reset();
  path228.moveTo(56, 42);
  path228.lineTo(56, 42);
  path228.lineTo(58, 42);
  path228.lineTo(58, 42);
  gBounds = path228.getBounds();
  allPaths.add(path228);
}

void pathOps229() {
  gBounds = path229.getBounds();
  allPaths.add(path229);
}

void pathOps230() {
  final Path path230 = Path();
  path230.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path230.getBounds();
  allPaths.add(path230);
}

void pathOps231() {
  final Path path231 = Path();
  path231.reset();
  path231.moveTo(331.66666666666663, 86);
  path231.lineTo(81, 86);
  path231.lineTo(81, 84);
  path231.lineTo(331.66666666666663, 84);
  gBounds = path231.getBounds();
  _runPathTest(path231);
  gFillType = path231.fillType;
  allPaths.add(path231);
}

void pathOps232() {
  gBounds = path232.getBounds();
  allPaths.add(path232);
}

void pathOps233() {
  final Path path233 = Path();
  path233.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path233.getBounds();
  allPaths.add(path233);
}

void pathOps234() {
  final Path path234 = Path();
  path234.reset();
  path234.moveTo(610.3333333333333, 86);
  path234.lineTo(359.66666666666663, 86);
  path234.lineTo(359.66666666666663, 84);
  path234.lineTo(610.3333333333333, 84);
  gBounds = path234.getBounds();
  _runPathTest(path234);
  gFillType = path234.fillType;
  allPaths.add(path234);
}

void pathOps235() {
  gBounds = path235.getBounds();
  allPaths.add(path235);
}

void pathOps236() {
  final Path path236 = Path();
  path236.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path236.getBounds();
  allPaths.add(path236);
}

void pathOps237() {
  final Path path237 = Path();
  path237.reset();
  path237.moveTo(889, 86);
  path237.lineTo(638.3333333333333, 86);
  path237.lineTo(638.3333333333333, 84);
  path237.lineTo(889, 84);
  gBounds = path237.getBounds();
  _runPathTest(path237);
  gFillType = path237.fillType;
  allPaths.add(path237);
}

void pathOps238() {
  gBounds = path238.getBounds();
  allPaths.add(path238);
}

void pathOps239() {
  final Path path239 = Path();
  path239.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(0, 136, 970, 1144), Radius.zero));
  gBounds = path239.getBounds();
  gBounds = path239.getBounds();
  gBounds = path239.getBounds();
  allPaths.add(path239);
}

void pathOps240() {
  gBounds = path240.getBounds();
  gBounds = path240.getBounds();
  gBounds = path240.getBounds();
  allPaths.add(path240);
}

void pathOps241() {
  final Path path241 = Path();
  path241.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(450, 136, 970, 696), Radius.zero));
  gBounds = path241.getBounds();
  gBounds = path241.getBounds();
  gBounds = path241.getBounds();
  allPaths.add(path241);
}

void pathOps242() {
  gBounds = path242.getBounds();
  allPaths.add(path242);
}

void pathOps243() {
  final Path path243 = Path();
  path243.moveTo(-3.0000008960834634, -8.999999251003146);
  path243.cubicTo(
    -3.0000008960834634,
    -10.64999886953342,
    -1.6500008722416055,
    -11.999999251003146,
    -8.960834634308412e-7,
    -11.999999251003146,
  );
  path243.cubicTo(
    -8.960834634308412e-7,
    -11.999999251003146,
    -8.960834634308412e-7,
    -11.999999251003146,
    -8.960834634308412e-7,
    -11.999999251003146,
  );
  path243.cubicTo(
    1.6499990800746787,
    -11.999999251003146,
    2.9999991039165366,
    -10.64999886953342,
    2.9999991039165366,
    -8.999999251003146,
  );
  path243.cubicTo(
    2.9999991039165366,
    -8.999999251003146,
    2.9999991039165366,
    9.000000748996854,
    2.9999991039165366,
    9.000000748996854,
  );
  path243.cubicTo(
    2.9999991039165366,
    10.650000367527127,
    1.6499990800746787,
    12.000000748996854,
    -8.960834634308412e-7,
    12.000000748996854,
  );
  path243.cubicTo(
    -8.960834634308412e-7,
    12.000000748996854,
    -8.960834634308412e-7,
    12.000000748996854,
    -8.960834634308412e-7,
    12.000000748996854,
  );
  path243.cubicTo(
    -1.6500008722416055,
    12.000000748996854,
    -3.0000008960834634,
    10.650000367527127,
    -3.0000008960834634,
    9.000000748996854,
  );
  path243.cubicTo(
    -3.0000008960834634,
    9.000000748996854,
    -3.0000008960834634,
    -8.999999251003146,
    -3.0000008960834634,
    -8.999999251003146,
  );
  path243.close();
  allPaths.add(path243);
}

void pathOps244() {
  final Path path244 = Path();
  path244.moveTo(-2.0000008960834634, -9.999999251003146);
  path244.cubicTo(
    -2.0000008960834634,
    -11.099999632472873,
    -1.1000009199253213,
    -11.999999251003146,
    -8.960834634308412e-7,
    -11.999999251003146,
  );
  path244.cubicTo(
    -8.960834634308412e-7,
    -11.999999251003146,
    -8.960834634308412e-7,
    -11.999999251003146,
    -8.960834634308412e-7,
    -11.999999251003146,
  );
  path244.cubicTo(
    1.0999991277583945,
    -11.999999251003146,
    1.9999991039165366,
    -11.099999632472873,
    1.9999991039165366,
    -9.999999251003146,
  );
  path244.cubicTo(
    1.9999991039165366,
    -9.999999251003146,
    1.9999991039165366,
    10.000000748996854,
    1.9999991039165366,
    10.000000748996854,
  );
  path244.cubicTo(
    1.9999991039165366,
    11.10000113046658,
    1.0999991277583945,
    12.000000748996854,
    -8.960834634308412e-7,
    12.000000748996854,
  );
  path244.cubicTo(
    -8.960834634308412e-7,
    12.000000748996854,
    -8.960834634308412e-7,
    12.000000748996854,
    -8.960834634308412e-7,
    12.000000748996854,
  );
  path244.cubicTo(
    -1.1000009199253213,
    12.000000748996854,
    -2.0000008960834634,
    11.10000113046658,
    -2.0000008960834634,
    10.000000748996854,
  );
  path244.cubicTo(
    -2.0000008960834634,
    10.000000748996854,
    -2.0000008960834634,
    -9.999999251003146,
    -2.0000008960834634,
    -9.999999251003146,
  );
  path244.close();
  allPaths.add(path244);
}

void pathOps245() {
  final Path path245 = Path();
  path245.moveTo(2.0000006178626677, 7.489968538720859e-7);
  path245.cubicTo(
    2.0000006178626677,
    1.1100007633019686,
    1.1000006417045256,
    2.000000748996854,
    6.178626676955901e-7,
    2.000000748996854,
  );
  path245.cubicTo(
    -1.109999396442447,
    2.000000748996854,
    -1.9999993821373323,
    1.1100007633019686,
    -1.9999993821373323,
    7.489968538720859e-7,
  );
  path245.cubicTo(
    -1.9999993821373323,
    -1.099999274845004,
    -1.109999396442447,
    -1.9999992510031461,
    6.178626676955901e-7,
    -1.9999992510031461,
  );
  path245.cubicTo(
    1.1000006417045256,
    -1.9999992510031461,
    2.0000006178626677,
    -1.099999274845004,
    2.0000006178626677,
    7.489968538720859e-7,
  );
  allPaths.add(path245);
}

void pathOps246() {
  final Path path246 = Path();
  path246.moveTo(-3.0000008960834634, -9.000000721237882);
  path246.cubicTo(
    -3.0000008960834634,
    -10.650000339768155,
    -1.6500008722416055,
    -12.000000721237882,
    -8.960834634308412e-7,
    -12.000000721237882,
  );
  path246.cubicTo(
    -8.960834634308412e-7,
    -12.000000721237882,
    -8.960834634308412e-7,
    -12.000000721237882,
    -8.960834634308412e-7,
    -12.000000721237882,
  );
  path246.cubicTo(
    1.6499990800746787,
    -12.000000721237882,
    2.9999991039165366,
    -10.650000339768155,
    2.9999991039165366,
    -9.000000721237882,
  );
  path246.cubicTo(
    2.9999991039165366,
    -9.000000721237882,
    2.9999991039165366,
    8.999999278762118,
    2.9999991039165366,
    8.999999278762118,
  );
  path246.cubicTo(
    2.9999991039165366,
    10.649998897292392,
    1.6499990800746787,
    11.999999278762118,
    -8.960834634308412e-7,
    11.999999278762118,
  );
  path246.cubicTo(
    -8.960834634308412e-7,
    11.999999278762118,
    -8.960834634308412e-7,
    11.999999278762118,
    -8.960834634308412e-7,
    11.999999278762118,
  );
  path246.cubicTo(
    -1.6500008722416055,
    11.999999278762118,
    -3.0000008960834634,
    10.649998897292392,
    -3.0000008960834634,
    8.999999278762118,
  );
  path246.cubicTo(
    -3.0000008960834634,
    8.999999278762118,
    -3.0000008960834634,
    -9.000000721237882,
    -3.0000008960834634,
    -9.000000721237882,
  );
  path246.close();
  allPaths.add(path246);
}

void pathOps247() {
  final Path path247 = Path();
  path247.moveTo(-2.0000008960834634, -10.000000721237882);
  path247.cubicTo(
    -2.0000008960834634,
    -11.100001102707608,
    -1.1000009199253213,
    -12.000000721237882,
    -8.960834634308412e-7,
    -12.000000721237882,
  );
  path247.cubicTo(
    -8.960834634308412e-7,
    -12.000000721237882,
    -8.960834634308412e-7,
    -12.000000721237882,
    -8.960834634308412e-7,
    -12.000000721237882,
  );
  path247.cubicTo(
    1.0999991277583945,
    -12.000000721237882,
    1.9999991039165366,
    -11.100001102707608,
    1.9999991039165366,
    -10.000000721237882,
  );
  path247.cubicTo(
    1.9999991039165366,
    -10.000000721237882,
    1.9999991039165366,
    9.999999278762118,
    1.9999991039165366,
    9.999999278762118,
  );
  path247.cubicTo(
    1.9999991039165366,
    11.099999660231845,
    1.0999991277583945,
    11.999999278762118,
    -8.960834634308412e-7,
    11.999999278762118,
  );
  path247.cubicTo(
    -8.960834634308412e-7,
    11.999999278762118,
    -8.960834634308412e-7,
    11.999999278762118,
    -8.960834634308412e-7,
    11.999999278762118,
  );
  path247.cubicTo(
    -1.1000009199253213,
    11.999999278762118,
    -2.0000008960834634,
    11.099999660231845,
    -2.0000008960834634,
    9.999999278762118,
  );
  path247.cubicTo(
    -2.0000008960834634,
    9.999999278762118,
    -2.0000008960834634,
    -10.000000721237882,
    -2.0000008960834634,
    -10.000000721237882,
  );
  path247.close();
  allPaths.add(path247);
}

void pathOps248() {
  final Path path248 = Path();
  path248.moveTo(-2.0000005026809617, 2.324364061223605e-7);
  path248.cubicTo(
    -2.0000005026809617,
    -1.0999997914054518,
    -1.1000005265228197,
    -1.9999997675635939,
    -5.026809617447725e-7,
    -1.9999997675635939,
  );
  path248.cubicTo(
    1.0999995211608962,
    -1.9999997675635939,
    1.9999994973190383,
    -1.0999997914054518,
    1.9999994973190383,
    2.324364061223605e-7,
  );
  path248.cubicTo(
    1.9999994973190383,
    1.100000256278264,
    1.0999995211608962,
    2.000000232436406,
    -5.026809617447725e-7,
    2.000000232436406,
  );
  path248.cubicTo(
    -1.1000005265228197,
    2.000000232436406,
    -2.0000005026809617,
    1.100000256278264,
    -2.0000005026809617,
    2.324364061223605e-7,
  );
  allPaths.add(path248);
}

void pathOps249() {
  final Path path249 = Path();
  path249.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path249.getBounds();
  allPaths.add(path249);
}

void pathOps250() {
  final Path path250 = Path();
  path250.reset();
  path250.moveTo(234.66666666666666, 90);
  path250.lineTo(96, 90);
  path250.lineTo(96, 89);
  path250.lineTo(234.66666666666666, 89);
  gBounds = path250.getBounds();
  allPaths.add(path250);
}

void pathOps251() {
  final Path path251 = Path();
  path251.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 105), Radius.zero),
  );
  gBounds = path251.getBounds();
  allPaths.add(path251);
}

void pathOps252() {
  final Path path252 = Path();
  path252.reset();
  path252.moveTo(234.66666666666669, 105);
  path252.lineTo(96, 105);
  path252.lineTo(96, 104);
  path252.lineTo(234.66666666666669, 104);
  gBounds = path252.getBounds();
  _runPathTest(path252);
  gFillType = path252.fillType;
  allPaths.add(path252);
}

void pathOps253() {
  final Path path253 = Path();
  path253.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 90), Radius.zero),
  );
  gBounds = path253.getBounds();
  allPaths.add(path253);
}

void pathOps254() {
  final Path path254 = Path();
  path254.reset();
  path254.moveTo(234.66666666666669, 90);
  path254.lineTo(96, 90);
  path254.lineTo(96, 89);
  path254.lineTo(234.66666666666669, 89);
  gBounds = path254.getBounds();
  _runPathTest(path254);
  gFillType = path254.fillType;
  allPaths.add(path254);
}

void pathOps255() {
  final Path path255 = Path();
  path255.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path255.getBounds();
  allPaths.add(path255);
}

void pathOps256() {
  final Path path256 = Path();
  path256.reset();
  path256.moveTo(234.66666666666666, 90);
  path256.lineTo(96, 90);
  path256.lineTo(96, 89);
  path256.lineTo(234.66666666666666, 89);
  gBounds = path256.getBounds();
  allPaths.add(path256);
}

void pathOps257() {
  final Path path257 = Path();
  path257.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path257.getBounds();
  allPaths.add(path257);
}

void pathOps258() {
  final Path path258 = Path();
  path258.reset();
  path258.moveTo(234.66666666666666, 90);
  path258.lineTo(96, 90);
  path258.lineTo(96, 89);
  path258.lineTo(234.66666666666666, 89);
  gBounds = path258.getBounds();
  _runPathTest(path258);
  gFillType = path258.fillType;
  allPaths.add(path258);
}

void pathOps259() {
  final Path path259 = Path();
  path259.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 105), Radius.zero),
  );
  gBounds = path259.getBounds();
  allPaths.add(path259);
}

void pathOps260() {
  final Path path260 = Path();
  path260.reset();
  path260.moveTo(234.66666666666666, 105);
  path260.lineTo(96, 105);
  path260.lineTo(96, 104);
  path260.lineTo(234.66666666666666, 104);
  gBounds = path260.getBounds();
  _runPathTest(path260);
  gFillType = path260.fillType;
  allPaths.add(path260);
}

void pathOps261() {
  final Path path261 = Path();
  path261.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path261.getBounds();
  allPaths.add(path261);
}

void pathOps262() {
  final Path path262 = Path();
  path262.reset();
  path262.moveTo(234.66666666666666, 90);
  path262.lineTo(96, 90);
  path262.lineTo(96, 89);
  path262.lineTo(234.66666666666666, 89);
  gBounds = path262.getBounds();
  _runPathTest(path262);
  gFillType = path262.fillType;
  allPaths.add(path262);
}

void pathOps263() {
  final Path path263 = Path();
  path263.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path263.getBounds();
  allPaths.add(path263);
}

void pathOps264() {
  final Path path264 = Path();
  path264.reset();
  path264.moveTo(234.66666666666666, 90);
  path264.lineTo(96, 90);
  path264.lineTo(96, 89);
  path264.lineTo(234.66666666666666, 89);
  gBounds = path264.getBounds();
  _runPathTest(path264);
  gFillType = path264.fillType;
  allPaths.add(path264);
}

void pathOps265() {
  final Path path265 = Path();
  path265.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path265.getBounds();
  allPaths.add(path265);
}

void pathOps266() {
  final Path path266 = Path();
  path266.reset();
  path266.moveTo(234.66666666666666, 90);
  path266.lineTo(96, 90);
  path266.lineTo(96, 89);
  path266.lineTo(234.66666666666666, 89);
  gBounds = path266.getBounds();
  _runPathTest(path266);
  gFillType = path266.fillType;
  allPaths.add(path266);
}

void pathOps267() {
  final Path path267 = Path();
  path267.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 75), Radius.zero),
  );
  gBounds = path267.getBounds();
  allPaths.add(path267);
}

void pathOps268() {
  final Path path268 = Path();
  path268.reset();
  path268.moveTo(234.66666666666666, 75);
  path268.lineTo(96, 75);
  path268.lineTo(96, 74);
  path268.lineTo(234.66666666666666, 74);
  gBounds = path268.getBounds();
  _runPathTest(path268);
  gFillType = path268.fillType;
  allPaths.add(path268);
}

void pathOps269() {
  final Path path269 = Path();
  path269.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 105), Radius.zero),
  );
  gBounds = path269.getBounds();
  allPaths.add(path269);
}

void pathOps270() {
  final Path path270 = Path();
  path270.reset();
  path270.moveTo(234.66666666666666, 105);
  path270.lineTo(96, 105);
  path270.lineTo(96, 104);
  path270.lineTo(234.66666666666666, 104);
  gBounds = path270.getBounds();
  allPaths.add(path270);
}

void pathOps271() {
  final Path path271 = Path();
  path271.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 139), Radius.zero),
  );
  gBounds = path271.getBounds();
  allPaths.add(path271);
}

void pathOps272() {
  final Path path272 = Path();
  path272.reset();
  path272.moveTo(234.66666666666666, 139);
  path272.lineTo(96, 139);
  path272.lineTo(96, 138);
  path272.lineTo(234.66666666666666, 138);
  gBounds = path272.getBounds();
  _runPathTest(path272);
  gFillType = path272.fillType;
  allPaths.add(path272);
}

void pathOps273() {
  final Path path273 = Path();
  path273.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path273.getBounds();
  allPaths.add(path273);
}

void pathOps274() {
  final Path path274 = Path();
  path274.reset();
  path274.moveTo(234.66666666666666, 120);
  path274.lineTo(96, 120);
  path274.lineTo(96, 119);
  path274.lineTo(234.66666666666666, 119);
  gBounds = path274.getBounds();
  _runPathTest(path274);
  gFillType = path274.fillType;
  allPaths.add(path274);
}

void pathOps275() {
  final Path path275 = Path();
  path275.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 94), Radius.zero),
  );
  gBounds = path275.getBounds();
  allPaths.add(path275);
}

void pathOps276() {
  final Path path276 = Path();
  path276.reset();
  path276.moveTo(234.66666666666669, 94);
  path276.lineTo(96, 94);
  path276.lineTo(96, 93);
  path276.lineTo(234.66666666666669, 93);
  gBounds = path276.getBounds();
  _runPathTest(path276);
  gFillType = path276.fillType;
  allPaths.add(path276);
}

void pathOps277() {
  gBounds = path277.getBounds();
  allPaths.add(path277);
}

void pathOps278() {
  gBounds = path278.getBounds();
  allPaths.add(path278);
}

void pathOps279() {
  gBounds = path279.getBounds();
  allPaths.add(path279);
}

void pathOps280() {
  gBounds = path280.getBounds();
  allPaths.add(path280);
}

void pathOps281() {
  gBounds = path281.getBounds();
  allPaths.add(path281);
}

void pathOps282() {
  gBounds = path282.getBounds();
  allPaths.add(path282);
}

void pathOps283() {
  final Path path283 = Path();
  path283.reset();
  path283.moveTo(56, 42);
  path283.lineTo(56, 42);
  path283.lineTo(58, 42);
  path283.lineTo(58, 42);
  gBounds = path283.getBounds();
  allPaths.add(path283);
}

void pathOps284() {
  gBounds = path284.getBounds();
  allPaths.add(path284);
}

void pathOps285() {
  final Path path285 = Path();
  path285.reset();
  path285.moveTo(56, 42);
  path285.lineTo(56, 42);
  path285.lineTo(58, 42);
  path285.lineTo(58, 42);
  gBounds = path285.getBounds();
  allPaths.add(path285);
}

void pathOps286() {
  gBounds = path286.getBounds();
  allPaths.add(path286);
}

void pathOps287() {
  final Path path287 = Path();
  path287.reset();
  path287.moveTo(56, 42);
  path287.lineTo(56, 42);
  path287.lineTo(58, 42);
  path287.lineTo(58, 42);
  gBounds = path287.getBounds();
  allPaths.add(path287);
}

void pathOps288() {
  gBounds = path288.getBounds();
  allPaths.add(path288);
}

void pathOps289() {
  final Path path289 = Path();
  path289.reset();
  path289.moveTo(56, 42);
  path289.lineTo(56, 42);
  path289.lineTo(58, 42);
  path289.lineTo(58, 42);
  gBounds = path289.getBounds();
  allPaths.add(path289);
}

void pathOps290() {
  gBounds = path290.getBounds();
  allPaths.add(path290);
}

void pathOps291() {
  final Path path291 = Path();
  path291.reset();
  path291.moveTo(56, 42);
  path291.lineTo(56, 42);
  path291.lineTo(58, 42);
  path291.lineTo(58, 42);
  gBounds = path291.getBounds();
  allPaths.add(path291);
}

void pathOps292() {
  gBounds = path292.getBounds();
  allPaths.add(path292);
}

void pathOps293() {
  final Path path293 = Path();
  path293.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path293.getBounds();
  allPaths.add(path293);
}

void pathOps294() {
  final Path path294 = Path();
  path294.reset();
  path294.moveTo(331.66666666666663, 86);
  path294.lineTo(81, 86);
  path294.lineTo(81, 84);
  path294.lineTo(331.66666666666663, 84);
  gBounds = path294.getBounds();
  _runPathTest(path294);
  gFillType = path294.fillType;
  allPaths.add(path294);
}

void pathOps295() {
  gBounds = path295.getBounds();
  allPaths.add(path295);
}

void pathOps296() {
  final Path path296 = Path();
  path296.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path296.getBounds();
  allPaths.add(path296);
}

void pathOps297() {
  final Path path297 = Path();
  path297.reset();
  path297.moveTo(610.3333333333333, 86);
  path297.lineTo(359.66666666666663, 86);
  path297.lineTo(359.66666666666663, 84);
  path297.lineTo(610.3333333333333, 84);
  gBounds = path297.getBounds();
  _runPathTest(path297);
  gFillType = path297.fillType;
  allPaths.add(path297);
}

void pathOps298() {
  gBounds = path298.getBounds();
  allPaths.add(path298);
}

void pathOps299() {
  final Path path299 = Path();
  path299.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path299.getBounds();
  allPaths.add(path299);
}

void pathOps300() {
  final Path path300 = Path();
  path300.reset();
  path300.moveTo(889, 86);
  path300.lineTo(638.3333333333333, 86);
  path300.lineTo(638.3333333333333, 84);
  path300.lineTo(889, 84);
  gBounds = path300.getBounds();
  _runPathTest(path300);
  gFillType = path300.fillType;
  allPaths.add(path300);
}

void pathOps301() {
  gBounds = path301.getBounds();
  allPaths.add(path301);
}

void pathOps302() {
  final Path path302 = Path();
  path302.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 94), Radius.zero),
  );
  gBounds = path302.getBounds();
  allPaths.add(path302);
}

void pathOps303() {
  final Path path303 = Path();
  path303.reset();
  path303.moveTo(234.66666666666669, 94);
  path303.lineTo(96, 94);
  path303.lineTo(96, 93);
  path303.lineTo(234.66666666666669, 93);
  gBounds = path303.getBounds();
  _runPathTest(path303);
  gFillType = path303.fillType;
  allPaths.add(path303);
}

void pathOps304() {
  final Path path304 = Path();
  path304.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 105), Radius.zero),
  );
  gBounds = path304.getBounds();
  allPaths.add(path304);
}

void pathOps305() {
  final Path path305 = Path();
  path305.reset();
  path305.moveTo(234.66666666666666, 105);
  path305.lineTo(96, 105);
  path305.lineTo(96, 104);
  path305.lineTo(234.66666666666666, 104);
  gBounds = path305.getBounds();
  allPaths.add(path305);
}

void pathOps306() {
  final Path path306 = Path();
  path306.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 105), Radius.zero),
  );
  gBounds = path306.getBounds();
  allPaths.add(path306);
}

void pathOps307() {
  final Path path307 = Path();
  path307.reset();
  path307.moveTo(234.66666666666669, 105);
  path307.lineTo(96, 105);
  path307.lineTo(96, 104);
  path307.lineTo(234.66666666666669, 104);
  gBounds = path307.getBounds();
  _runPathTest(path307);
  gFillType = path307.fillType;
  allPaths.add(path307);
}

void pathOps308() {
  final Path path308 = Path();
  path308.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 105), Radius.zero),
  );
  gBounds = path308.getBounds();
  allPaths.add(path308);
}

void pathOps309() {
  final Path path309 = Path();
  path309.reset();
  path309.moveTo(234.66666666666666, 105);
  path309.lineTo(96, 105);
  path309.lineTo(96, 104);
  path309.lineTo(234.66666666666666, 104);
  gBounds = path309.getBounds();
  _runPathTest(path309);
  gFillType = path309.fillType;
  allPaths.add(path309);
}

void pathOps310() {
  final Path path310 = Path();
  path310.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path310.getBounds();
  allPaths.add(path310);
}

void pathOps311() {
  final Path path311 = Path();
  path311.reset();
  path311.moveTo(234.66666666666666, 120);
  path311.lineTo(96, 120);
  path311.lineTo(96, 119);
  path311.lineTo(234.66666666666666, 119);
  gBounds = path311.getBounds();
  _runPathTest(path311);
  gFillType = path311.fillType;
  allPaths.add(path311);
}

void pathOps312() {
  final Path path312 = Path();
  path312.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 139), Radius.zero),
  );
  gBounds = path312.getBounds();
  allPaths.add(path312);
}

void pathOps313() {
  final Path path313 = Path();
  path313.reset();
  path313.moveTo(234.66666666666666, 139);
  path313.lineTo(96, 139);
  path313.lineTo(96, 138);
  path313.lineTo(234.66666666666666, 138);
  gBounds = path313.getBounds();
  _runPathTest(path313);
  gFillType = path313.fillType;
  allPaths.add(path313);
}

void pathOps314() {
  final Path path314 = Path();
  path314.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 75), Radius.zero),
  );
  gBounds = path314.getBounds();
  allPaths.add(path314);
}

void pathOps315() {
  final Path path315 = Path();
  path315.reset();
  path315.moveTo(234.66666666666666, 75);
  path315.lineTo(96, 75);
  path315.lineTo(96, 74);
  path315.lineTo(234.66666666666666, 74);
  gBounds = path315.getBounds();
  _runPathTest(path315);
  gFillType = path315.fillType;
  allPaths.add(path315);
}

void pathOps316() {
  final Path path316 = Path();
  path316.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path316.getBounds();
  allPaths.add(path316);
}

void pathOps317() {
  final Path path317 = Path();
  path317.reset();
  path317.moveTo(234.66666666666666, 90);
  path317.lineTo(96, 90);
  path317.lineTo(96, 89);
  path317.lineTo(234.66666666666666, 89);
  gBounds = path317.getBounds();
  allPaths.add(path317);
}

void pathOps318() {
  final Path path318 = Path();
  path318.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path318.getBounds();
  allPaths.add(path318);
}

void pathOps319() {
  final Path path319 = Path();
  path319.reset();
  path319.moveTo(234.66666666666666, 90);
  path319.lineTo(96, 90);
  path319.lineTo(96, 89);
  path319.lineTo(234.66666666666666, 89);
  gBounds = path319.getBounds();
  _runPathTest(path319);
  gFillType = path319.fillType;
  allPaths.add(path319);
}

void pathOps320() {
  final Path path320 = Path();
  path320.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path320.getBounds();
  allPaths.add(path320);
}

void pathOps321() {
  final Path path321 = Path();
  path321.reset();
  path321.moveTo(234.66666666666666, 90);
  path321.lineTo(96, 90);
  path321.lineTo(96, 89);
  path321.lineTo(234.66666666666666, 89);
  gBounds = path321.getBounds();
  _runPathTest(path321);
  gFillType = path321.fillType;
  allPaths.add(path321);
}

void pathOps322() {
  final Path path322 = Path();
  path322.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path322.getBounds();
  allPaths.add(path322);
}

void pathOps323() {
  final Path path323 = Path();
  path323.reset();
  path323.moveTo(234.66666666666666, 90);
  path323.lineTo(96, 90);
  path323.lineTo(96, 89);
  path323.lineTo(234.66666666666666, 89);
  gBounds = path323.getBounds();
  _runPathTest(path323);
  gFillType = path323.fillType;
  allPaths.add(path323);
}

void pathOps324() {
  final Path path324 = Path();
  path324.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path324.getBounds();
  allPaths.add(path324);
}

void pathOps325() {
  final Path path325 = Path();
  path325.reset();
  path325.moveTo(234.66666666666666, 90);
  path325.lineTo(96, 90);
  path325.lineTo(96, 89);
  path325.lineTo(234.66666666666666, 89);
  gBounds = path325.getBounds();
  _runPathTest(path325);
  gFillType = path325.fillType;
  allPaths.add(path325);
}

void pathOps326() {
  final Path path326 = Path();
  path326.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 90), Radius.zero),
  );
  gBounds = path326.getBounds();
  allPaths.add(path326);
}

void pathOps327() {
  final Path path327 = Path();
  path327.reset();
  path327.moveTo(234.66666666666666, 90);
  path327.lineTo(96, 90);
  path327.lineTo(96, 89);
  path327.lineTo(234.66666666666666, 89);
  gBounds = path327.getBounds();
  allPaths.add(path327);
}

void pathOps328() {
  final Path path328 = Path();
  path328.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666669, 90), Radius.zero),
  );
  gBounds = path328.getBounds();
  allPaths.add(path328);
}

void pathOps329() {
  final Path path329 = Path();
  path329.reset();
  path329.moveTo(234.66666666666669, 90);
  path329.lineTo(96, 90);
  path329.lineTo(96, 89);
  path329.lineTo(234.66666666666669, 89);
  gBounds = path329.getBounds();
  _runPathTest(path329);
  gFillType = path329.fillType;
  allPaths.add(path329);
}

void pathOps330() {
  gBounds = path330.getBounds();
  allPaths.add(path330);
}

void pathOps331() {
  gBounds = path331.getBounds();
  allPaths.add(path331);
}

void pathOps332() {
  gBounds = path332.getBounds();
  allPaths.add(path332);
}

void pathOps333() {
  gBounds = path333.getBounds();
  allPaths.add(path333);
}

void pathOps334() {
  gBounds = path334.getBounds();
  allPaths.add(path334);
}

void pathOps335() {
  final Path path335 = Path();
  path335.reset();
  path335.moveTo(56, 42);
  path335.lineTo(56, 42);
  path335.lineTo(58, 42);
  path335.lineTo(58, 42);
  gBounds = path335.getBounds();
  allPaths.add(path335);
}

void pathOps336() {
  gBounds = path336.getBounds();
  allPaths.add(path336);
}

void pathOps337() {
  final Path path337 = Path();
  path337.reset();
  path337.moveTo(56, 42);
  path337.lineTo(56, 42);
  path337.lineTo(58, 42);
  path337.lineTo(58, 42);
  gBounds = path337.getBounds();
  allPaths.add(path337);
}

void pathOps338() {
  gBounds = path338.getBounds();
  allPaths.add(path338);
}

void pathOps339() {
  final Path path339 = Path();
  path339.reset();
  path339.moveTo(56, 42);
  path339.lineTo(56, 42);
  path339.lineTo(58, 42);
  path339.lineTo(58, 42);
  gBounds = path339.getBounds();
  allPaths.add(path339);
}

void pathOps340() {
  gBounds = path340.getBounds();
  allPaths.add(path340);
}

void pathOps341() {
  final Path path341 = Path();
  path341.reset();
  path341.moveTo(56, 42);
  path341.lineTo(56, 42);
  path341.lineTo(58, 42);
  path341.lineTo(58, 42);
  gBounds = path341.getBounds();
  allPaths.add(path341);
}

void pathOps342() {
  gBounds = path342.getBounds();
  allPaths.add(path342);
}

void pathOps343() {
  final Path path343 = Path();
  path343.reset();
  path343.moveTo(56, 42);
  path343.lineTo(56, 42);
  path343.lineTo(58, 42);
  path343.lineTo(58, 42);
  gBounds = path343.getBounds();
  allPaths.add(path343);
}

void pathOps344() {
  gBounds = path344.getBounds();
  allPaths.add(path344);
}

void pathOps345() {
  gBounds = path345.getBounds();
  allPaths.add(path345);
}

void pathOps346() {
  gBounds = path346.getBounds();
  allPaths.add(path346);
}

void pathOps347() {
  final Path path347 = Path();
  path347.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path347.getBounds();
  allPaths.add(path347);
}

void pathOps348() {
  final Path path348 = Path();
  path348.reset();
  path348.moveTo(331.66666666666663, 86);
  path348.lineTo(81, 86);
  path348.lineTo(81, 84);
  path348.lineTo(331.66666666666663, 84);
  gBounds = path348.getBounds();
  _runPathTest(path348);
  gFillType = path348.fillType;
  allPaths.add(path348);
}

void pathOps349() {
  gBounds = path349.getBounds();
  allPaths.add(path349);
}

void pathOps350() {
  final Path path350 = Path();
  path350.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path350.getBounds();
  allPaths.add(path350);
}

void pathOps351() {
  final Path path351 = Path();
  path351.reset();
  path351.moveTo(610.3333333333333, 86);
  path351.lineTo(359.66666666666663, 86);
  path351.lineTo(359.66666666666663, 84);
  path351.lineTo(610.3333333333333, 84);
  gBounds = path351.getBounds();
  _runPathTest(path351);
  gFillType = path351.fillType;
  allPaths.add(path351);
}

void pathOps352() {
  gBounds = path352.getBounds();
  allPaths.add(path352);
}

void pathOps353() {
  final Path path353 = Path();
  path353.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path353.getBounds();
  allPaths.add(path353);
}

void pathOps354() {
  final Path path354 = Path();
  path354.reset();
  path354.moveTo(889, 86);
  path354.lineTo(638.3333333333333, 86);
  path354.lineTo(638.3333333333333, 84);
  path354.lineTo(889, 84);
  gBounds = path354.getBounds();
  _runPathTest(path354);
  gFillType = path354.fillType;
  allPaths.add(path354);
}

void pathOps355() {
  final Path path355 = Path();
  path355.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(0, 136, 970, 1144), Radius.zero));
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  gBounds = path355.getBounds();
  allPaths.add(path355);
}

void pathOps356() {
  gBounds = path356.getBounds();
  allPaths.add(path356);
}

void pathOps357() {
  final Path path357 = Path();
  path357.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(450, 136, 970, 696), Radius.zero));
  gBounds = path357.getBounds();
  allPaths.add(path357);
}

void pathOps358() {
  gBounds = path358.getBounds();
  allPaths.add(path358);
}

void pathOps359() {
  gBounds = path359.getBounds();
  allPaths.add(path359);
}

void pathOps360() {
  gBounds = path360.getBounds();
  allPaths.add(path360);
}

void pathOps361() {
  gBounds = path361.getBounds();
  allPaths.add(path361);
}

void pathOps362() {
  gBounds = path362.getBounds();
  allPaths.add(path362);
}

void pathOps363() {
  gBounds = path363.getBounds();
  allPaths.add(path363);
}

void pathOps364() {
  final Path path364 = Path();
  path364.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path364.getBounds();
  allPaths.add(path364);
}

void pathOps365() {
  final Path path365 = Path();
  path365.reset();
  path365.moveTo(331.66666666666663, 86);
  path365.lineTo(81, 86);
  path365.lineTo(81, 84);
  path365.lineTo(331.66666666666663, 84);
  gBounds = path365.getBounds();
  _runPathTest(path365);
  gFillType = path365.fillType;
  allPaths.add(path365);
}

void pathOps366() {
  gBounds = path366.getBounds();
  allPaths.add(path366);
}

void pathOps367() {
  final Path path367 = Path();
  path367.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path367.getBounds();
  allPaths.add(path367);
}

void pathOps368() {
  final Path path368 = Path();
  path368.reset();
  path368.moveTo(610.3333333333333, 86);
  path368.lineTo(359.66666666666663, 86);
  path368.lineTo(359.66666666666663, 84);
  path368.lineTo(610.3333333333333, 84);
  gBounds = path368.getBounds();
  _runPathTest(path368);
  gFillType = path368.fillType;
  allPaths.add(path368);
}

void pathOps369() {
  gBounds = path369.getBounds();
  allPaths.add(path369);
}

void pathOps370() {
  final Path path370 = Path();
  path370.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path370.getBounds();
  allPaths.add(path370);
}

void pathOps371() {
  final Path path371 = Path();
  path371.reset();
  path371.moveTo(889, 86);
  path371.lineTo(638.3333333333333, 86);
  path371.lineTo(638.3333333333333, 84);
  path371.lineTo(889, 84);
  gBounds = path371.getBounds();
  _runPathTest(path371);
  gFillType = path371.fillType;
  allPaths.add(path371);
}

void pathOps372() {
  gBounds = path372.getBounds();
  allPaths.add(path372);
}

void pathOps373() {
  gBounds = path373.getBounds();
  allPaths.add(path373);
}

void pathOps374() {
  gBounds = path374.getBounds();
  allPaths.add(path374);
}

void pathOps375() {
  gBounds = path375.getBounds();
  allPaths.add(path375);
}

void pathOps376() {
  gBounds = path376.getBounds();
  allPaths.add(path376);
}

void pathOps377() {
  final Path path377 = Path();
  path377.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path377.getBounds();
  allPaths.add(path377);
}

void pathOps378() {
  final Path path378 = Path();
  path378.reset();
  path378.moveTo(331.66666666666663, 86);
  path378.lineTo(81, 86);
  path378.lineTo(81, 84);
  path378.lineTo(331.66666666666663, 84);
  gBounds = path378.getBounds();
  _runPathTest(path378);
  gFillType = path378.fillType;
  allPaths.add(path378);
}

void pathOps379() {
  gBounds = path379.getBounds();
  allPaths.add(path379);
}

void pathOps380() {
  final Path path380 = Path();
  path380.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path380.getBounds();
  allPaths.add(path380);
}

void pathOps381() {
  final Path path381 = Path();
  path381.reset();
  path381.moveTo(610.3333333333333, 86);
  path381.lineTo(359.66666666666663, 86);
  path381.lineTo(359.66666666666663, 84);
  path381.lineTo(610.3333333333333, 84);
  gBounds = path381.getBounds();
  _runPathTest(path381);
  gFillType = path381.fillType;
  allPaths.add(path381);
}

void pathOps382() {
  gBounds = path382.getBounds();
  allPaths.add(path382);
}

void pathOps383() {
  final Path path383 = Path();
  path383.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path383.getBounds();
  allPaths.add(path383);
}

void pathOps384() {
  final Path path384 = Path();
  path384.reset();
  path384.moveTo(889, 86);
  path384.lineTo(638.3333333333333, 86);
  path384.lineTo(638.3333333333333, 84);
  path384.lineTo(889, 84);
  gBounds = path384.getBounds();
  _runPathTest(path384);
  gFillType = path384.fillType;
  allPaths.add(path384);
}

void pathOps385() {
  gBounds = path385.getBounds();
  allPaths.add(path385);
}

void pathOps386() {
  gBounds = path386.getBounds();
  allPaths.add(path386);
}

void pathOps387() {
  gBounds = path387.getBounds();
  allPaths.add(path387);
}

void pathOps388() {
  gBounds = path388.getBounds();
  allPaths.add(path388);
}

void pathOps389() {
  gBounds = path389.getBounds();
  allPaths.add(path389);
}

void pathOps390() {
  final Path path390 = Path();
  path390.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path390.getBounds();
  allPaths.add(path390);
}

void pathOps391() {
  final Path path391 = Path();
  path391.reset();
  path391.moveTo(331.66666666666663, 86);
  path391.lineTo(81, 86);
  path391.lineTo(81, 84);
  path391.lineTo(331.66666666666663, 84);
  gBounds = path391.getBounds();
  _runPathTest(path391);
  gFillType = path391.fillType;
  allPaths.add(path391);
}

void pathOps392() {
  gBounds = path392.getBounds();
  allPaths.add(path392);
}

void pathOps393() {
  final Path path393 = Path();
  path393.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path393.getBounds();
  allPaths.add(path393);
}

void pathOps394() {
  final Path path394 = Path();
  path394.reset();
  path394.moveTo(610.3333333333333, 86);
  path394.lineTo(359.66666666666663, 86);
  path394.lineTo(359.66666666666663, 84);
  path394.lineTo(610.3333333333333, 84);
  gBounds = path394.getBounds();
  _runPathTest(path394);
  gFillType = path394.fillType;
  allPaths.add(path394);
}

void pathOps395() {
  gBounds = path395.getBounds();
  allPaths.add(path395);
}

void pathOps396() {
  final Path path396 = Path();
  path396.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path396.getBounds();
  allPaths.add(path396);
}

void pathOps397() {
  final Path path397 = Path();
  path397.reset();
  path397.moveTo(889, 86);
  path397.lineTo(638.3333333333333, 86);
  path397.lineTo(638.3333333333333, 84);
  path397.lineTo(889, 84);
  gBounds = path397.getBounds();
  _runPathTest(path397);
  gFillType = path397.fillType;
  allPaths.add(path397);
}

void pathOps398() {
  gBounds = path398.getBounds();
  allPaths.add(path398);
}

void pathOps399() {
  gBounds = path399.getBounds();
  allPaths.add(path399);
}

void pathOps400() {
  gBounds = path400.getBounds();
  allPaths.add(path400);
}

void pathOps401() {
  gBounds = path401.getBounds();
  allPaths.add(path401);
}

void pathOps402() {
  gBounds = path402.getBounds();
  allPaths.add(path402);
}

void pathOps403() {
  final Path path403 = Path();
  path403.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path403.getBounds();
  allPaths.add(path403);
}

void pathOps404() {
  final Path path404 = Path();
  path404.reset();
  path404.moveTo(331.66666666666663, 86);
  path404.lineTo(81, 86);
  path404.lineTo(81, 84);
  path404.lineTo(331.66666666666663, 84);
  gBounds = path404.getBounds();
  _runPathTest(path404);
  gFillType = path404.fillType;
  allPaths.add(path404);
}

void pathOps405() {
  gBounds = path405.getBounds();
  allPaths.add(path405);
}

void pathOps406() {
  final Path path406 = Path();
  path406.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path406.getBounds();
  allPaths.add(path406);
}

void pathOps407() {
  final Path path407 = Path();
  path407.reset();
  path407.moveTo(610.3333333333333, 86);
  path407.lineTo(359.66666666666663, 86);
  path407.lineTo(359.66666666666663, 84);
  path407.lineTo(610.3333333333333, 84);
  gBounds = path407.getBounds();
  _runPathTest(path407);
  gFillType = path407.fillType;
  allPaths.add(path407);
}

void pathOps408() {
  gBounds = path408.getBounds();
  allPaths.add(path408);
}

void pathOps409() {
  final Path path409 = Path();
  path409.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path409.getBounds();
  allPaths.add(path409);
}

void pathOps410() {
  final Path path410 = Path();
  path410.reset();
  path410.moveTo(889, 86);
  path410.lineTo(638.3333333333333, 86);
  path410.lineTo(638.3333333333333, 84);
  path410.lineTo(889, 84);
  gBounds = path410.getBounds();
  _runPathTest(path410);
  gFillType = path410.fillType;
  allPaths.add(path410);
}

void pathOps411() {
  gBounds = path411.getBounds();
  allPaths.add(path411);
}

void pathOps412() {
  gBounds = path412.getBounds();
  allPaths.add(path412);
}

void pathOps413() {
  gBounds = path413.getBounds();
  allPaths.add(path413);
}

void pathOps414() {
  gBounds = path414.getBounds();
  allPaths.add(path414);
}

void pathOps415() {
  gBounds = path415.getBounds();
  allPaths.add(path415);
}

void pathOps416() {
  final Path path416 = Path();
  path416.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path416.getBounds();
  allPaths.add(path416);
}

void pathOps417() {
  final Path path417 = Path();
  path417.reset();
  path417.moveTo(331.66666666666663, 86);
  path417.lineTo(81, 86);
  path417.lineTo(81, 84);
  path417.lineTo(331.66666666666663, 84);
  gBounds = path417.getBounds();
  _runPathTest(path417);
  gFillType = path417.fillType;
  allPaths.add(path417);
}

void pathOps418() {
  gBounds = path418.getBounds();
  allPaths.add(path418);
}

void pathOps419() {
  final Path path419 = Path();
  path419.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path419.getBounds();
  allPaths.add(path419);
}

void pathOps420() {
  final Path path420 = Path();
  path420.reset();
  path420.moveTo(610.3333333333333, 86);
  path420.lineTo(359.66666666666663, 86);
  path420.lineTo(359.66666666666663, 84);
  path420.lineTo(610.3333333333333, 84);
  gBounds = path420.getBounds();
  _runPathTest(path420);
  gFillType = path420.fillType;
  allPaths.add(path420);
}

void pathOps421() {
  gBounds = path421.getBounds();
  allPaths.add(path421);
}

void pathOps422() {
  final Path path422 = Path();
  path422.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path422.getBounds();
  allPaths.add(path422);
}

void pathOps423() {
  final Path path423 = Path();
  path423.reset();
  path423.moveTo(889, 86);
  path423.lineTo(638.3333333333333, 86);
  path423.lineTo(638.3333333333333, 84);
  path423.lineTo(889, 84);
  gBounds = path423.getBounds();
  _runPathTest(path423);
  gFillType = path423.fillType;
  allPaths.add(path423);
}

void pathOps424() {
  gBounds = path424.getBounds();
  allPaths.add(path424);
}

void pathOps425() {
  gBounds = path425.getBounds();
  allPaths.add(path425);
}

void pathOps426() {
  gBounds = path426.getBounds();
  allPaths.add(path426);
}

void pathOps427() {
  gBounds = path427.getBounds();
  allPaths.add(path427);
}

void pathOps428() {
  gBounds = path428.getBounds();
  allPaths.add(path428);
}

void pathOps429() {
  final Path path429 = Path();
  path429.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path429.getBounds();
  allPaths.add(path429);
}

void pathOps430() {
  final Path path430 = Path();
  path430.reset();
  path430.moveTo(331.66666666666663, 86);
  path430.lineTo(81, 86);
  path430.lineTo(81, 84);
  path430.lineTo(331.66666666666663, 84);
  gBounds = path430.getBounds();
  _runPathTest(path430);
  gFillType = path430.fillType;
  allPaths.add(path430);
}

void pathOps431() {
  gBounds = path431.getBounds();
  allPaths.add(path431);
}

void pathOps432() {
  final Path path432 = Path();
  path432.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path432.getBounds();
  allPaths.add(path432);
}

void pathOps433() {
  final Path path433 = Path();
  path433.reset();
  path433.moveTo(610.3333333333333, 86);
  path433.lineTo(359.66666666666663, 86);
  path433.lineTo(359.66666666666663, 84);
  path433.lineTo(610.3333333333333, 84);
  gBounds = path433.getBounds();
  _runPathTest(path433);
  gFillType = path433.fillType;
  allPaths.add(path433);
}

void pathOps434() {
  gBounds = path434.getBounds();
  allPaths.add(path434);
}

void pathOps435() {
  final Path path435 = Path();
  path435.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path435.getBounds();
  allPaths.add(path435);
}

void pathOps436() {
  final Path path436 = Path();
  path436.reset();
  path436.moveTo(889, 86);
  path436.lineTo(638.3333333333333, 86);
  path436.lineTo(638.3333333333333, 84);
  path436.lineTo(889, 84);
  gBounds = path436.getBounds();
  _runPathTest(path436);
  gFillType = path436.fillType;
  allPaths.add(path436);
}

void pathOps437() {
  gBounds = path437.getBounds();
  allPaths.add(path437);
}

void pathOps438() {
  gBounds = path438.getBounds();
  allPaths.add(path438);
}

void pathOps439() {
  gBounds = path439.getBounds();
  allPaths.add(path439);
}

void pathOps440() {
  gBounds = path440.getBounds();
  allPaths.add(path440);
}

void pathOps441() {
  gBounds = path441.getBounds();
  allPaths.add(path441);
}

void pathOps442() {
  final Path path442 = Path();
  path442.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path442.getBounds();
  allPaths.add(path442);
}

void pathOps443() {
  final Path path443 = Path();
  path443.reset();
  path443.moveTo(331.66666666666663, 86);
  path443.lineTo(81, 86);
  path443.lineTo(81, 84);
  path443.lineTo(331.66666666666663, 84);
  gBounds = path443.getBounds();
  _runPathTest(path443);
  gFillType = path443.fillType;
  allPaths.add(path443);
}

void pathOps444() {
  gBounds = path444.getBounds();
  allPaths.add(path444);
}

void pathOps445() {
  final Path path445 = Path();
  path445.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path445.getBounds();
  allPaths.add(path445);
}

void pathOps446() {
  final Path path446 = Path();
  path446.reset();
  path446.moveTo(610.3333333333333, 86);
  path446.lineTo(359.66666666666663, 86);
  path446.lineTo(359.66666666666663, 84);
  path446.lineTo(610.3333333333333, 84);
  gBounds = path446.getBounds();
  _runPathTest(path446);
  gFillType = path446.fillType;
  allPaths.add(path446);
}

void pathOps447() {
  gBounds = path447.getBounds();
  allPaths.add(path447);
}

void pathOps448() {
  final Path path448 = Path();
  path448.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path448.getBounds();
  allPaths.add(path448);
}

void pathOps449() {
  final Path path449 = Path();
  path449.reset();
  path449.moveTo(889, 86);
  path449.lineTo(638.3333333333333, 86);
  path449.lineTo(638.3333333333333, 84);
  path449.lineTo(889, 84);
  gBounds = path449.getBounds();
  _runPathTest(path449);
  gFillType = path449.fillType;
  allPaths.add(path449);
}

void pathOps450() {
  gBounds = path450.getBounds();
  allPaths.add(path450);
}

void pathOps451() {
  gBounds = path451.getBounds();
  allPaths.add(path451);
}

void pathOps452() {
  gBounds = path452.getBounds();
  allPaths.add(path452);
}

void pathOps453() {
  gBounds = path453.getBounds();
  allPaths.add(path453);
}

void pathOps454() {
  gBounds = path454.getBounds();
  allPaths.add(path454);
}

void pathOps455() {
  final Path path455 = Path();
  path455.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path455.getBounds();
  allPaths.add(path455);
}

void pathOps456() {
  final Path path456 = Path();
  path456.reset();
  path456.moveTo(331.66666666666663, 86);
  path456.lineTo(81, 86);
  path456.lineTo(81, 84);
  path456.lineTo(331.66666666666663, 84);
  gBounds = path456.getBounds();
  _runPathTest(path456);
  gFillType = path456.fillType;
  allPaths.add(path456);
}

void pathOps457() {
  gBounds = path457.getBounds();
  allPaths.add(path457);
}

void pathOps458() {
  final Path path458 = Path();
  path458.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path458.getBounds();
  allPaths.add(path458);
}

void pathOps459() {
  final Path path459 = Path();
  path459.reset();
  path459.moveTo(610.3333333333333, 86);
  path459.lineTo(359.66666666666663, 86);
  path459.lineTo(359.66666666666663, 84);
  path459.lineTo(610.3333333333333, 84);
  gBounds = path459.getBounds();
  _runPathTest(path459);
  gFillType = path459.fillType;
  allPaths.add(path459);
}

void pathOps460() {
  gBounds = path460.getBounds();
  allPaths.add(path460);
}

void pathOps461() {
  final Path path461 = Path();
  path461.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path461.getBounds();
  allPaths.add(path461);
}

void pathOps462() {
  final Path path462 = Path();
  path462.reset();
  path462.moveTo(889, 86);
  path462.lineTo(638.3333333333333, 86);
  path462.lineTo(638.3333333333333, 84);
  path462.lineTo(889, 84);
  gBounds = path462.getBounds();
  _runPathTest(path462);
  gFillType = path462.fillType;
  allPaths.add(path462);
}

void pathOps463() {
  gBounds = path463.getBounds();
  allPaths.add(path463);
}

void pathOps464() {
  gBounds = path464.getBounds();
  allPaths.add(path464);
}

void pathOps465() {
  gBounds = path465.getBounds();
  allPaths.add(path465);
}

void pathOps466() {
  gBounds = path466.getBounds();
  allPaths.add(path466);
}

void pathOps467() {
  gBounds = path467.getBounds();
  allPaths.add(path467);
}

void pathOps468() {
  final Path path468 = Path();
  path468.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path468.getBounds();
  allPaths.add(path468);
}

void pathOps469() {
  final Path path469 = Path();
  path469.reset();
  path469.moveTo(331.66666666666663, 86);
  path469.lineTo(81, 86);
  path469.lineTo(81, 84);
  path469.lineTo(331.66666666666663, 84);
  gBounds = path469.getBounds();
  _runPathTest(path469);
  gFillType = path469.fillType;
  allPaths.add(path469);
}

void pathOps470() {
  gBounds = path470.getBounds();
  allPaths.add(path470);
}

void pathOps471() {
  final Path path471 = Path();
  path471.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path471.getBounds();
  allPaths.add(path471);
}

void pathOps472() {
  final Path path472 = Path();
  path472.reset();
  path472.moveTo(610.3333333333333, 86);
  path472.lineTo(359.66666666666663, 86);
  path472.lineTo(359.66666666666663, 84);
  path472.lineTo(610.3333333333333, 84);
  gBounds = path472.getBounds();
  _runPathTest(path472);
  gFillType = path472.fillType;
  allPaths.add(path472);
}

void pathOps473() {
  gBounds = path473.getBounds();
  allPaths.add(path473);
}

void pathOps474() {
  final Path path474 = Path();
  path474.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path474.getBounds();
  allPaths.add(path474);
}

void pathOps475() {
  final Path path475 = Path();
  path475.reset();
  path475.moveTo(889, 86);
  path475.lineTo(638.3333333333333, 86);
  path475.lineTo(638.3333333333333, 84);
  path475.lineTo(889, 84);
  gBounds = path475.getBounds();
  _runPathTest(path475);
  gFillType = path475.fillType;
  allPaths.add(path475);
}

void pathOps476() {
  gBounds = path476.getBounds();
  allPaths.add(path476);
}

void pathOps477() {
  gBounds = path477.getBounds();
  allPaths.add(path477);
}

void pathOps478() {
  gBounds = path478.getBounds();
  allPaths.add(path478);
}

void pathOps479() {
  gBounds = path479.getBounds();
  allPaths.add(path479);
}

void pathOps480() {
  gBounds = path480.getBounds();
  allPaths.add(path480);
}

void pathOps481() {
  final Path path481 = Path();
  path481.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path481.getBounds();
  allPaths.add(path481);
}

void pathOps482() {
  final Path path482 = Path();
  path482.reset();
  path482.moveTo(331.66666666666663, 86);
  path482.lineTo(81, 86);
  path482.lineTo(81, 84);
  path482.lineTo(331.66666666666663, 84);
  gBounds = path482.getBounds();
  _runPathTest(path482);
  gFillType = path482.fillType;
  allPaths.add(path482);
}

void pathOps483() {
  gBounds = path483.getBounds();
  allPaths.add(path483);
}

void pathOps484() {
  final Path path484 = Path();
  path484.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path484.getBounds();
  allPaths.add(path484);
}

void pathOps485() {
  final Path path485 = Path();
  path485.reset();
  path485.moveTo(610.3333333333333, 86);
  path485.lineTo(359.66666666666663, 86);
  path485.lineTo(359.66666666666663, 84);
  path485.lineTo(610.3333333333333, 84);
  gBounds = path485.getBounds();
  _runPathTest(path485);
  gFillType = path485.fillType;
  allPaths.add(path485);
}

void pathOps486() {
  gBounds = path486.getBounds();
  allPaths.add(path486);
}

void pathOps487() {
  final Path path487 = Path();
  path487.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path487.getBounds();
  allPaths.add(path487);
}

void pathOps488() {
  final Path path488 = Path();
  path488.reset();
  path488.moveTo(889, 86);
  path488.lineTo(638.3333333333333, 86);
  path488.lineTo(638.3333333333333, 84);
  path488.lineTo(889, 84);
  gBounds = path488.getBounds();
  _runPathTest(path488);
  gFillType = path488.fillType;
  allPaths.add(path488);
}

void pathOps489() {
  gBounds = path489.getBounds();
  allPaths.add(path489);
}

void pathOps490() {
  gBounds = path490.getBounds();
  allPaths.add(path490);
}

void pathOps491() {
  gBounds = path491.getBounds();
  allPaths.add(path491);
}

void pathOps492() {
  gBounds = path492.getBounds();
  allPaths.add(path492);
}

void pathOps493() {
  gBounds = path493.getBounds();
  allPaths.add(path493);
}

void pathOps494() {
  final Path path494 = Path();
  path494.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path494.getBounds();
  allPaths.add(path494);
}

void pathOps495() {
  final Path path495 = Path();
  path495.reset();
  path495.moveTo(331.66666666666663, 86);
  path495.lineTo(81, 86);
  path495.lineTo(81, 84);
  path495.lineTo(331.66666666666663, 84);
  gBounds = path495.getBounds();
  _runPathTest(path495);
  gFillType = path495.fillType;
  allPaths.add(path495);
}

void pathOps496() {
  gBounds = path496.getBounds();
  allPaths.add(path496);
}

void pathOps497() {
  final Path path497 = Path();
  path497.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path497.getBounds();
  allPaths.add(path497);
}

void pathOps498() {
  final Path path498 = Path();
  path498.reset();
  path498.moveTo(610.3333333333333, 86);
  path498.lineTo(359.66666666666663, 86);
  path498.lineTo(359.66666666666663, 84);
  path498.lineTo(610.3333333333333, 84);
  gBounds = path498.getBounds();
  _runPathTest(path498);
  gFillType = path498.fillType;
  allPaths.add(path498);
}

void pathOps499() {
  gBounds = path499.getBounds();
  allPaths.add(path499);
}

void pathOps500() {
  final Path path500 = Path();
  path500.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path500.getBounds();
  allPaths.add(path500);
}

void pathOps501() {
  final Path path501 = Path();
  path501.reset();
  path501.moveTo(889, 86);
  path501.lineTo(638.3333333333333, 86);
  path501.lineTo(638.3333333333333, 84);
  path501.lineTo(889, 84);
  gBounds = path501.getBounds();
  _runPathTest(path501);
  gFillType = path501.fillType;
  allPaths.add(path501);
}

void pathOps502() {
  gBounds = path502.getBounds();
  allPaths.add(path502);
}

void pathOps503() {
  gBounds = path503.getBounds();
  allPaths.add(path503);
}

void pathOps504() {
  gBounds = path504.getBounds();
  allPaths.add(path504);
}

void pathOps505() {
  gBounds = path505.getBounds();
  allPaths.add(path505);
}

void pathOps506() {
  gBounds = path506.getBounds();
  allPaths.add(path506);
}

void pathOps507() {
  final Path path507 = Path();
  path507.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path507.getBounds();
  allPaths.add(path507);
}

void pathOps508() {
  final Path path508 = Path();
  path508.reset();
  path508.moveTo(331.66666666666663, 86);
  path508.lineTo(81, 86);
  path508.lineTo(81, 84);
  path508.lineTo(331.66666666666663, 84);
  gBounds = path508.getBounds();
  _runPathTest(path508);
  gFillType = path508.fillType;
  allPaths.add(path508);
}

void pathOps509() {
  gBounds = path509.getBounds();
  allPaths.add(path509);
}

void pathOps510() {
  final Path path510 = Path();
  path510.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path510.getBounds();
  allPaths.add(path510);
}

void pathOps511() {
  final Path path511 = Path();
  path511.reset();
  path511.moveTo(610.3333333333333, 86);
  path511.lineTo(359.66666666666663, 86);
  path511.lineTo(359.66666666666663, 84);
  path511.lineTo(610.3333333333333, 84);
  gBounds = path511.getBounds();
  _runPathTest(path511);
  gFillType = path511.fillType;
  allPaths.add(path511);
}

void pathOps512() {
  gBounds = path512.getBounds();
  allPaths.add(path512);
}

void pathOps513() {
  final Path path513 = Path();
  path513.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path513.getBounds();
  allPaths.add(path513);
}

void pathOps514() {
  final Path path514 = Path();
  path514.reset();
  path514.moveTo(889, 86);
  path514.lineTo(638.3333333333333, 86);
  path514.lineTo(638.3333333333333, 84);
  path514.lineTo(889, 84);
  gBounds = path514.getBounds();
  _runPathTest(path514);
  gFillType = path514.fillType;
  allPaths.add(path514);
}

void pathOps515() {
  gBounds = path515.getBounds();
  allPaths.add(path515);
}

void pathOps516() {
  gBounds = path516.getBounds();
  allPaths.add(path516);
}

void pathOps517() {
  gBounds = path517.getBounds();
  allPaths.add(path517);
}

void pathOps518() {
  gBounds = path518.getBounds();
  allPaths.add(path518);
}

void pathOps519() {
  gBounds = path519.getBounds();
  allPaths.add(path519);
}

void pathOps520() {
  final Path path520 = Path();
  path520.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path520.getBounds();
  allPaths.add(path520);
}

void pathOps521() {
  final Path path521 = Path();
  path521.reset();
  path521.moveTo(331.66666666666663, 86);
  path521.lineTo(81, 86);
  path521.lineTo(81, 84);
  path521.lineTo(331.66666666666663, 84);
  gBounds = path521.getBounds();
  _runPathTest(path521);
  gFillType = path521.fillType;
  allPaths.add(path521);
}

void pathOps522() {
  gBounds = path522.getBounds();
  allPaths.add(path522);
}

void pathOps523() {
  final Path path523 = Path();
  path523.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path523.getBounds();
  allPaths.add(path523);
}

void pathOps524() {
  final Path path524 = Path();
  path524.reset();
  path524.moveTo(610.3333333333333, 86);
  path524.lineTo(359.66666666666663, 86);
  path524.lineTo(359.66666666666663, 84);
  path524.lineTo(610.3333333333333, 84);
  gBounds = path524.getBounds();
  _runPathTest(path524);
  gFillType = path524.fillType;
  allPaths.add(path524);
}

void pathOps525() {
  gBounds = path525.getBounds();
  allPaths.add(path525);
}

void pathOps526() {
  final Path path526 = Path();
  path526.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path526.getBounds();
  allPaths.add(path526);
}

void pathOps527() {
  final Path path527 = Path();
  path527.reset();
  path527.moveTo(889, 86);
  path527.lineTo(638.3333333333333, 86);
  path527.lineTo(638.3333333333333, 84);
  path527.lineTo(889, 84);
  gBounds = path527.getBounds();
  _runPathTest(path527);
  gFillType = path527.fillType;
  allPaths.add(path527);
}

void pathOps528() {
  gBounds = path528.getBounds();
  allPaths.add(path528);
}

void pathOps529() {
  gBounds = path529.getBounds();
  allPaths.add(path529);
}

void pathOps530() {
  gBounds = path530.getBounds();
  allPaths.add(path530);
}

void pathOps531() {
  gBounds = path531.getBounds();
  allPaths.add(path531);
}

void pathOps532() {
  gBounds = path532.getBounds();
  allPaths.add(path532);
}

void pathOps533() {
  final Path path533 = Path();
  path533.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path533.getBounds();
  allPaths.add(path533);
}

void pathOps534() {
  final Path path534 = Path();
  path534.reset();
  path534.moveTo(331.66666666666663, 86);
  path534.lineTo(81, 86);
  path534.lineTo(81, 84);
  path534.lineTo(331.66666666666663, 84);
  gBounds = path534.getBounds();
  _runPathTest(path534);
  gFillType = path534.fillType;
  allPaths.add(path534);
}

void pathOps535() {
  gBounds = path535.getBounds();
  allPaths.add(path535);
}

void pathOps536() {
  final Path path536 = Path();
  path536.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path536.getBounds();
  allPaths.add(path536);
}

void pathOps537() {
  final Path path537 = Path();
  path537.reset();
  path537.moveTo(610.3333333333333, 86);
  path537.lineTo(359.66666666666663, 86);
  path537.lineTo(359.66666666666663, 84);
  path537.lineTo(610.3333333333333, 84);
  gBounds = path537.getBounds();
  _runPathTest(path537);
  gFillType = path537.fillType;
  allPaths.add(path537);
}

void pathOps538() {
  gBounds = path538.getBounds();
  allPaths.add(path538);
}

void pathOps539() {
  final Path path539 = Path();
  path539.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path539.getBounds();
  allPaths.add(path539);
}

void pathOps540() {
  final Path path540 = Path();
  path540.reset();
  path540.moveTo(889, 86);
  path540.lineTo(638.3333333333333, 86);
  path540.lineTo(638.3333333333333, 84);
  path540.lineTo(889, 84);
  gBounds = path540.getBounds();
  _runPathTest(path540);
  gFillType = path540.fillType;
  allPaths.add(path540);
}

void pathOps541() {
  gBounds = path541.getBounds();
  allPaths.add(path541);
}

void pathOps542() {
  gBounds = path542.getBounds();
  allPaths.add(path542);
}

void pathOps543() {
  gBounds = path543.getBounds();
  allPaths.add(path543);
}

void pathOps544() {
  gBounds = path544.getBounds();
  allPaths.add(path544);
}

void pathOps545() {
  gBounds = path545.getBounds();
  allPaths.add(path545);
}

void pathOps546() {
  final Path path546 = Path();
  path546.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path546.getBounds();
  allPaths.add(path546);
}

void pathOps547() {
  final Path path547 = Path();
  path547.reset();
  path547.moveTo(331.66666666666663, 86);
  path547.lineTo(81, 86);
  path547.lineTo(81, 84);
  path547.lineTo(331.66666666666663, 84);
  gBounds = path547.getBounds();
  _runPathTest(path547);
  gFillType = path547.fillType;
  allPaths.add(path547);
}

void pathOps548() {
  gBounds = path548.getBounds();
  allPaths.add(path548);
}

void pathOps549() {
  final Path path549 = Path();
  path549.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path549.getBounds();
  allPaths.add(path549);
}

void pathOps550() {
  final Path path550 = Path();
  path550.reset();
  path550.moveTo(610.3333333333333, 86);
  path550.lineTo(359.66666666666663, 86);
  path550.lineTo(359.66666666666663, 84);
  path550.lineTo(610.3333333333333, 84);
  gBounds = path550.getBounds();
  _runPathTest(path550);
  gFillType = path550.fillType;
  allPaths.add(path550);
}

void pathOps551() {
  gBounds = path551.getBounds();
  allPaths.add(path551);
}

void pathOps552() {
  final Path path552 = Path();
  path552.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path552.getBounds();
  allPaths.add(path552);
}

void pathOps553() {
  final Path path553 = Path();
  path553.reset();
  path553.moveTo(889, 86);
  path553.lineTo(638.3333333333333, 86);
  path553.lineTo(638.3333333333333, 84);
  path553.lineTo(889, 84);
  gBounds = path553.getBounds();
  _runPathTest(path553);
  gFillType = path553.fillType;
  allPaths.add(path553);
}

void pathOps554() {
  gBounds = path554.getBounds();
  allPaths.add(path554);
}

void pathOps555() {
  gBounds = path555.getBounds();
  allPaths.add(path555);
}

void pathOps556() {
  gBounds = path556.getBounds();
  allPaths.add(path556);
}

void pathOps557() {
  gBounds = path557.getBounds();
  allPaths.add(path557);
}

void pathOps558() {
  gBounds = path558.getBounds();
  gBounds = path558.getBounds();
  gBounds = path558.getBounds();
  gBounds = path558.getBounds();
  gBounds = path558.getBounds();
  gBounds = path558.getBounds();
  gBounds = path558.getBounds();
  gBounds = path558.getBounds();
  allPaths.add(path558);
}

void pathOps559() {
  final Path path559 = Path();
  path559.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(81, 0, 331.66666666666663, 84), Radius.zero),
  );
  gBounds = path559.getBounds();
  allPaths.add(path559);
}

void pathOps560() {
  final Path path560 = Path();
  path560.reset();
  path560.moveTo(331.66666666666663, 86);
  path560.lineTo(81, 86);
  path560.lineTo(81, 84);
  path560.lineTo(331.66666666666663, 84);
  gBounds = path560.getBounds();
  _runPathTest(path560);
  gFillType = path560.fillType;
  allPaths.add(path560);
}

void pathOps561() {
  gBounds = path561.getBounds();
  allPaths.add(path561);
}

void pathOps562() {
  final Path path562 = Path();
  path562.addRRect(
    RRect.fromRectAndRadius(
      const Rect.fromLTRB(359.66666666666663, 0, 610.3333333333333, 84),
      Radius.zero,
    ),
  );
  gBounds = path562.getBounds();
  allPaths.add(path562);
}

void pathOps563() {
  final Path path563 = Path();
  path563.reset();
  path563.moveTo(610.3333333333333, 86);
  path563.lineTo(359.66666666666663, 86);
  path563.lineTo(359.66666666666663, 84);
  path563.lineTo(610.3333333333333, 84);
  gBounds = path563.getBounds();
  _runPathTest(path563);
  gFillType = path563.fillType;
  allPaths.add(path563);
}

void pathOps564() {
  gBounds = path564.getBounds();
  allPaths.add(path564);
}

void pathOps565() {
  final Path path565 = Path();
  path565.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(638.3333333333333, 0, 889, 84), Radius.zero),
  );
  gBounds = path565.getBounds();
  allPaths.add(path565);
}

void pathOps566() {
  final Path path566 = Path();
  path566.reset();
  path566.moveTo(889, 86);
  path566.lineTo(638.3333333333333, 86);
  path566.lineTo(638.3333333333333, 84);
  path566.lineTo(889, 84);
  gBounds = path566.getBounds();
  _runPathTest(path566);
  gFillType = path566.fillType;
  allPaths.add(path566);
}

void pathOps567() {
  final Path path567 = Path();
  path567.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path567.getBounds();
  allPaths.add(path567);
}

void pathOps568() {
  final Path path568 = Path();
  path568.reset();
  path568.moveTo(234.66666666666666, 120);
  path568.lineTo(96, 120);
  path568.lineTo(96, 119);
  path568.lineTo(234.66666666666666, 119);
  gBounds = path568.getBounds();
  _runPathTest(path568);
  gFillType = path568.fillType;
  allPaths.add(path568);
}

void pathOps569() {
  final Path path569 = Path();
  path569.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path569.getBounds();
  allPaths.add(path569);
}

void pathOps570() {
  final Path path570 = Path();
  path570.reset();
  path570.moveTo(234.66666666666666, 120);
  path570.lineTo(96, 120);
  path570.lineTo(96, 119);
  path570.lineTo(234.66666666666666, 119);
  gBounds = path570.getBounds();
  _runPathTest(path570);
  gFillType = path570.fillType;
  allPaths.add(path570);
}

void pathOps571() {
  final Path path571 = Path();
  path571.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(116.39999999999999, 136, 853.6, 1144), Radius.zero),
  );
  gBounds = path571.getBounds();
  allPaths.add(path571);
}

void pathOps572() {
  final Path path572 = Path();
  path572.addRRect(
    RRect.fromRectAndCorners(
      const Rect.fromLTRB(0, 0, 312.6, 880),
      topLeft: const Radius.circular(10),
      topRight: const Radius.circular(10),
      bottomLeft: const Radius.circular(2),
      bottomRight: const Radius.circular(2),
    ),
  );
  gFillType = path572.fillType;
  path573 = path572.shift(const Offset(525, 248));
  allPaths.add(path572);
}

void pathOps573() {
  gBounds = path573.getBounds();
  allPaths.add(path573);
}

void pathOps574() {
  final Path path574 = Path();
  path574.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(525, 248, 837.6, 1128), Radius.zero),
  );
  gBounds = path574.getBounds();
  allPaths.add(path574);
}

void pathOps575() {
  final Path path575 = Path();
  path575.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(525, 248, 837.6, 304), Radius.zero));
  gBounds = path575.getBounds();
  allPaths.add(path575);
}

void pathOps576() {
  final Path path576 = Path();
  path576.moveTo(0, 0);
  path576.lineTo(220.60000000000002, 0);
  path576.quadraticBezierTo(235.60000000000002, 0, 237.569696969697, 7.817946907441011);
  path576.arcToPoint(
    const Offset(299.630303030303, 7.817946907441011),
    radius: const Radius.circular(32),
    clockwise: false,
  );
  path576.quadraticBezierTo(301.6, 0, 316.6, 0);
  path576.lineTo(312.6, 0);
  path576.lineTo(312.6, 48);
  path576.lineTo(0, 48);
  path576.close();
  gFillType = path576.fillType;
  path577 = path576.shift(const Offset(525, 1080));
  allPaths.add(path576);
}

void pathOps577() {
  gBounds = path577.getBounds();
  allPaths.add(path577);
}

void pathOps578() {
  final Path path578 = Path();
  path578.addOval(const Rect.fromLTRB(0, 0, 56, 56));
  gFillType = path578.fillType;
  path579 = path578.shift(const Offset(765.6, 1052));
  allPaths.add(path578);
}

void pathOps579() {
  gBounds = path579.getBounds();
  allPaths.add(path579);
}

void pathOps580() {
  final Path path580 = Path();
  path580.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(116.39999999999999, 136, 853.6, 192), Radius.zero),
  );
  gBounds = path580.getBounds();
  allPaths.add(path580);
}

void pathOps581() {
  final Path path581 = Path();
  path581.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path581.getBounds();
  allPaths.add(path581);
}

void pathOps582() {
  final Path path582 = Path();
  path582.reset();
  path582.moveTo(234.66666666666666, 120);
  path582.lineTo(96, 120);
  path582.lineTo(96, 119);
  path582.lineTo(234.66666666666666, 119);
  gBounds = path582.getBounds();
  _runPathTest(path582);
  gFillType = path582.fillType;
  allPaths.add(path582);
}

void pathOps583() {
  final Path path583 = Path();
  path583.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path583.getBounds();
  allPaths.add(path583);
}

void pathOps584() {
  final Path path584 = Path();
  path584.reset();
  path584.moveTo(234.66666666666666, 120);
  path584.lineTo(96, 120);
  path584.lineTo(96, 119);
  path584.lineTo(234.66666666666666, 119);
  gBounds = path584.getBounds();
  _runPathTest(path584);
  gFillType = path584.fillType;
  allPaths.add(path584);
}

void pathOps585() {
  final Path path585 = Path();
  path585.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path585.getBounds();
  allPaths.add(path585);
}

void pathOps586() {
  final Path path586 = Path();
  path586.reset();
  path586.moveTo(234.66666666666666, 120);
  path586.lineTo(96, 120);
  path586.lineTo(96, 119);
  path586.lineTo(234.66666666666666, 119);
  gBounds = path586.getBounds();
  _runPathTest(path586);
  gFillType = path586.fillType;
  allPaths.add(path586);
}

void pathOps587() {
  final Path path587 = Path();
  path587.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path587.getBounds();
  allPaths.add(path587);
}

void pathOps588() {
  final Path path588 = Path();
  path588.reset();
  path588.moveTo(234.66666666666666, 120);
  path588.lineTo(96, 120);
  path588.lineTo(96, 119);
  path588.lineTo(234.66666666666666, 119);
  gBounds = path588.getBounds();
  _runPathTest(path588);
  gFillType = path588.fillType;
  allPaths.add(path588);
}

void pathOps589() {
  final Path path589 = Path();
  path589.addRRect(
    RRect.fromRectAndRadius(const Rect.fromLTRB(0, 0, 250.66666666666666, 120), Radius.zero),
  );
  gBounds = path589.getBounds();
  allPaths.add(path589);
}

void pathOps590() {
  final Path path590 = Path();
  path590.reset();
  path590.moveTo(234.66666666666666, 120);
  path590.lineTo(96, 120);
  path590.lineTo(96, 119);
  path590.lineTo(234.66666666666666, 119);
  gBounds = path590.getBounds();
  _runPathTest(path590);
  gFillType = path590.fillType;
  allPaths.add(path590);
}

void pathOps596() {
  final Path path596 = Path();
  path596.reset();
  path596.moveTo(56, 42);
  path596.lineTo(56, 42);
  path596.lineTo(58, 42);
  path596.lineTo(58, 42);
  gBounds = path596.getBounds();
  allPaths.add(path596);
}

void pathOps598() {
  final Path path598 = Path();
  path598.reset();
  path598.moveTo(56, 42);
  path598.lineTo(56, 42);
  path598.lineTo(58, 42);
  path598.lineTo(58, 42);
  gBounds = path598.getBounds();
  allPaths.add(path598);
}

void pathOps600() {
  final Path path600 = Path();
  path600.reset();
  path600.moveTo(56, 42);
  path600.lineTo(56, 42);
  path600.lineTo(58, 42);
  path600.lineTo(58, 42);
  gBounds = path600.getBounds();
  allPaths.add(path600);
}

void pathOps602() {
  final Path path602 = Path();
  path602.reset();
  path602.moveTo(56, 42);
  path602.lineTo(56, 42);
  path602.lineTo(58, 42);
  path602.lineTo(58, 42);
  gBounds = path602.getBounds();
  allPaths.add(path602);
}

void pathOps604() {
  final Path path604 = Path();
  path604.reset();
  path604.moveTo(56, 42);
  path604.lineTo(56, 42);
  path604.lineTo(58, 42);
  path604.lineTo(58, 42);
  gBounds = path604.getBounds();
  allPaths.add(path604);
}

void pathOps605() {
  final Path path605 = Path();
  path605.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(0, 136, 970, 1144), Radius.zero));
  allPaths.add(path605);
}

void pathOps607() {
  final Path path607 = Path();
  path607.addRRect(RRect.fromRectAndRadius(const Rect.fromLTRB(450, 136, 970, 696), Radius.zero));
  allPaths.add(path607);
}

void _runPathTest(Path path) {}
