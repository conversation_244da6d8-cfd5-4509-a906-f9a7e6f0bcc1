// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by dev/tools/gen_keycodes/bin/gen_keycodes.dart and
// should not be edited directly.
//
// Edit the template dev/tools/gen_keycodes/data/keyboard_maps.tmpl instead.
// See dev/tools/gen_keycodes/README.md for more information.

import 'keyboard_key.g.dart';

export 'keyboard_key.g.dart' show LogicalKeyboardKey, PhysicalKeyboardKey;

/// Maps Android-specific key codes to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kAndroidToLogicalKey = <int, LogicalKeyboardKey>{
  3: LogicalKeyboardKey.goHome,
  4: LogicalKeyboardKey.goBack,
  5: LogicalKeyboardKey.call,
  6: LogicalKeyboardKey.endCall,
  7: LogicalKeyboardKey.digit0,
  8: LogicalKeyboardKey.digit1,
  9: LogicalKeyboardKey.digit2,
  10: LogicalKeyboardKey.digit3,
  11: LogicalKeyboardKey.digit4,
  12: Logical<PERSON>eyboardKey.digit5,
  13: LogicalKeyboardKey.digit6,
  14: LogicalKeyboardKey.digit7,
  15: LogicalKeyboardKey.digit8,
  16: LogicalKeyboardKey.digit9,
  17: LogicalKeyboardKey.asterisk,
  18: LogicalKeyboardKey.numberSign,
  19: LogicalKeyboardKey.arrowUp,
  20: LogicalKeyboardKey.arrowDown,
  21: LogicalKeyboardKey.arrowLeft,
  22: LogicalKeyboardKey.arrowRight,
  23: LogicalKeyboardKey.select,
  24: LogicalKeyboardKey.audioVolumeUp,
  25: LogicalKeyboardKey.audioVolumeDown,
  26: LogicalKeyboardKey.power,
  27: LogicalKeyboardKey.camera,
  28: LogicalKeyboardKey.clear,
  29: LogicalKeyboardKey.keyA,
  30: LogicalKeyboardKey.keyB,
  31: LogicalKeyboardKey.keyC,
  32: LogicalKeyboardKey.keyD,
  33: LogicalKeyboardKey.keyE,
  34: LogicalKeyboardKey.keyF,
  35: LogicalKeyboardKey.keyG,
  36: LogicalKeyboardKey.keyH,
  37: LogicalKeyboardKey.keyI,
  38: LogicalKeyboardKey.keyJ,
  39: LogicalKeyboardKey.keyK,
  40: LogicalKeyboardKey.keyL,
  41: LogicalKeyboardKey.keyM,
  42: LogicalKeyboardKey.keyN,
  43: LogicalKeyboardKey.keyO,
  44: LogicalKeyboardKey.keyP,
  45: LogicalKeyboardKey.keyQ,
  46: LogicalKeyboardKey.keyR,
  47: LogicalKeyboardKey.keyS,
  48: LogicalKeyboardKey.keyT,
  49: LogicalKeyboardKey.keyU,
  50: LogicalKeyboardKey.keyV,
  51: LogicalKeyboardKey.keyW,
  52: LogicalKeyboardKey.keyX,
  53: LogicalKeyboardKey.keyY,
  54: LogicalKeyboardKey.keyZ,
  55: LogicalKeyboardKey.comma,
  56: LogicalKeyboardKey.period,
  57: LogicalKeyboardKey.altLeft,
  58: LogicalKeyboardKey.altRight,
  59: LogicalKeyboardKey.shiftLeft,
  60: LogicalKeyboardKey.shiftRight,
  61: LogicalKeyboardKey.tab,
  62: LogicalKeyboardKey.space,
  63: LogicalKeyboardKey.symbol,
  64: LogicalKeyboardKey.launchWebBrowser,
  65: LogicalKeyboardKey.launchMail,
  66: LogicalKeyboardKey.enter,
  67: LogicalKeyboardKey.backspace,
  68: LogicalKeyboardKey.backquote,
  69: LogicalKeyboardKey.minus,
  70: LogicalKeyboardKey.equal,
  71: LogicalKeyboardKey.bracketLeft,
  72: LogicalKeyboardKey.bracketRight,
  73: LogicalKeyboardKey.backslash,
  74: LogicalKeyboardKey.semicolon,
  75: LogicalKeyboardKey.quote,
  76: LogicalKeyboardKey.slash,
  77: LogicalKeyboardKey.at,
  79: LogicalKeyboardKey.headsetHook,
  80: LogicalKeyboardKey.cameraFocus,
  81: LogicalKeyboardKey.add,
  82: LogicalKeyboardKey.contextMenu,
  83: LogicalKeyboardKey.notification,
  84: LogicalKeyboardKey.browserSearch,
  85: LogicalKeyboardKey.mediaPlayPause,
  86: LogicalKeyboardKey.mediaStop,
  87: LogicalKeyboardKey.mediaTrackNext,
  88: LogicalKeyboardKey.mediaTrackPrevious,
  89: LogicalKeyboardKey.mediaRewind,
  90: LogicalKeyboardKey.mediaFastForward,
  91: LogicalKeyboardKey.microphoneVolumeMute,
  92: LogicalKeyboardKey.pageUp,
  93: LogicalKeyboardKey.pageDown,
  95: LogicalKeyboardKey.modeChange,
  96: LogicalKeyboardKey.gameButtonA,
  97: LogicalKeyboardKey.gameButtonB,
  98: LogicalKeyboardKey.gameButtonC,
  99: LogicalKeyboardKey.gameButtonX,
  100: LogicalKeyboardKey.gameButtonY,
  101: LogicalKeyboardKey.gameButtonZ,
  102: LogicalKeyboardKey.gameButtonLeft1,
  103: LogicalKeyboardKey.gameButtonRight1,
  104: LogicalKeyboardKey.gameButtonLeft2,
  105: LogicalKeyboardKey.gameButtonRight2,
  106: LogicalKeyboardKey.gameButtonThumbLeft,
  107: LogicalKeyboardKey.gameButtonThumbRight,
  108: LogicalKeyboardKey.gameButtonStart,
  109: LogicalKeyboardKey.gameButtonSelect,
  110: LogicalKeyboardKey.gameButtonMode,
  111: LogicalKeyboardKey.escape,
  112: LogicalKeyboardKey.delete,
  113: LogicalKeyboardKey.controlLeft,
  114: LogicalKeyboardKey.controlRight,
  115: LogicalKeyboardKey.capsLock,
  116: LogicalKeyboardKey.scrollLock,
  117: LogicalKeyboardKey.metaLeft,
  118: LogicalKeyboardKey.metaRight,
  119: LogicalKeyboardKey.fn,
  120: LogicalKeyboardKey.printScreen,
  121: LogicalKeyboardKey.pause,
  122: LogicalKeyboardKey.home,
  123: LogicalKeyboardKey.end,
  124: LogicalKeyboardKey.insert,
  125: LogicalKeyboardKey.browserForward,
  126: LogicalKeyboardKey.mediaPlay,
  127: LogicalKeyboardKey.mediaPause,
  128: LogicalKeyboardKey.close,
  129: LogicalKeyboardKey.eject,
  130: LogicalKeyboardKey.mediaRecord,
  131: LogicalKeyboardKey.f1,
  132: LogicalKeyboardKey.f2,
  133: LogicalKeyboardKey.f3,
  134: LogicalKeyboardKey.f4,
  135: LogicalKeyboardKey.f5,
  136: LogicalKeyboardKey.f6,
  137: LogicalKeyboardKey.f7,
  138: LogicalKeyboardKey.f8,
  139: LogicalKeyboardKey.f9,
  140: LogicalKeyboardKey.f10,
  141: LogicalKeyboardKey.f11,
  142: LogicalKeyboardKey.f12,
  143: LogicalKeyboardKey.numLock,
  144: LogicalKeyboardKey.numpad0,
  145: LogicalKeyboardKey.numpad1,
  146: LogicalKeyboardKey.numpad2,
  147: LogicalKeyboardKey.numpad3,
  148: LogicalKeyboardKey.numpad4,
  149: LogicalKeyboardKey.numpad5,
  150: LogicalKeyboardKey.numpad6,
  151: LogicalKeyboardKey.numpad7,
  152: LogicalKeyboardKey.numpad8,
  153: LogicalKeyboardKey.numpad9,
  154: LogicalKeyboardKey.numpadDivide,
  155: LogicalKeyboardKey.numpadMultiply,
  156: LogicalKeyboardKey.numpadSubtract,
  157: LogicalKeyboardKey.numpadAdd,
  158: LogicalKeyboardKey.numpadDecimal,
  159: LogicalKeyboardKey.numpadComma,
  160: LogicalKeyboardKey.numpadEnter,
  161: LogicalKeyboardKey.numpadEqual,
  162: LogicalKeyboardKey.numpadParenLeft,
  163: LogicalKeyboardKey.numpadParenRight,
  164: LogicalKeyboardKey.audioVolumeMute,
  165: LogicalKeyboardKey.info,
  166: LogicalKeyboardKey.channelUp,
  167: LogicalKeyboardKey.channelDown,
  168: LogicalKeyboardKey.zoomIn,
  169: LogicalKeyboardKey.zoomOut,
  170: LogicalKeyboardKey.tv,
  172: LogicalKeyboardKey.guide,
  173: LogicalKeyboardKey.dvr,
  174: LogicalKeyboardKey.browserFavorites,
  175: LogicalKeyboardKey.closedCaptionToggle,
  176: LogicalKeyboardKey.settings,
  177: LogicalKeyboardKey.tvPower,
  178: LogicalKeyboardKey.tvInput,
  179: LogicalKeyboardKey.stbPower,
  180: LogicalKeyboardKey.stbInput,
  181: LogicalKeyboardKey.avrPower,
  182: LogicalKeyboardKey.avrInput,
  183: LogicalKeyboardKey.colorF0Red,
  184: LogicalKeyboardKey.colorF1Green,
  185: LogicalKeyboardKey.colorF2Yellow,
  186: LogicalKeyboardKey.colorF3Blue,
  187: LogicalKeyboardKey.appSwitch,
  188: LogicalKeyboardKey.gameButton1,
  189: LogicalKeyboardKey.gameButton2,
  190: LogicalKeyboardKey.gameButton3,
  191: LogicalKeyboardKey.gameButton4,
  192: LogicalKeyboardKey.gameButton5,
  193: LogicalKeyboardKey.gameButton6,
  194: LogicalKeyboardKey.gameButton7,
  195: LogicalKeyboardKey.gameButton8,
  196: LogicalKeyboardKey.gameButton9,
  197: LogicalKeyboardKey.gameButton10,
  198: LogicalKeyboardKey.gameButton11,
  199: LogicalKeyboardKey.gameButton12,
  200: LogicalKeyboardKey.gameButton13,
  201: LogicalKeyboardKey.gameButton14,
  202: LogicalKeyboardKey.gameButton15,
  203: LogicalKeyboardKey.gameButton16,
  204: LogicalKeyboardKey.groupNext,
  205: LogicalKeyboardKey.mannerMode,
  206: LogicalKeyboardKey.tv3DMode,
  207: LogicalKeyboardKey.launchContacts,
  208: LogicalKeyboardKey.launchCalendar,
  209: LogicalKeyboardKey.launchMusicPlayer,
  211: LogicalKeyboardKey.zenkakuHankaku,
  212: LogicalKeyboardKey.eisu,
  213: LogicalKeyboardKey.nonConvert,
  214: LogicalKeyboardKey.convert,
  215: LogicalKeyboardKey.hiraganaKatakana,
  216: LogicalKeyboardKey.intlYen,
  217: LogicalKeyboardKey.intlRo,
  218: LogicalKeyboardKey.kanjiMode,
  219: LogicalKeyboardKey.launchAssistant,
  220: LogicalKeyboardKey.brightnessDown,
  221: LogicalKeyboardKey.brightnessUp,
  222: LogicalKeyboardKey.mediaAudioTrack,
  223: LogicalKeyboardKey.sleep,
  224: LogicalKeyboardKey.wakeUp,
  225: LogicalKeyboardKey.pairing,
  226: LogicalKeyboardKey.mediaTopMenu,
  229: LogicalKeyboardKey.mediaLast,
  230: LogicalKeyboardKey.tvDataService,
  232: LogicalKeyboardKey.tvRadioService,
  233: LogicalKeyboardKey.teletext,
  234: LogicalKeyboardKey.tvNumberEntry,
  235: LogicalKeyboardKey.tvTerrestrialAnalog,
  236: LogicalKeyboardKey.tvTerrestrialDigital,
  237: LogicalKeyboardKey.tvSatellite,
  238: LogicalKeyboardKey.tvSatelliteBS,
  239: LogicalKeyboardKey.tvSatelliteCS,
  240: LogicalKeyboardKey.tvSatelliteToggle,
  241: LogicalKeyboardKey.tvNetwork,
  242: LogicalKeyboardKey.tvAntennaCable,
  243: LogicalKeyboardKey.tvInputHDMI1,
  244: LogicalKeyboardKey.tvInputHDMI2,
  245: LogicalKeyboardKey.tvInputHDMI3,
  246: LogicalKeyboardKey.tvInputHDMI4,
  247: LogicalKeyboardKey.tvInputComposite1,
  248: LogicalKeyboardKey.tvInputComposite2,
  249: LogicalKeyboardKey.tvInputComponent1,
  250: LogicalKeyboardKey.tvInputComponent2,
  251: LogicalKeyboardKey.tvInputVGA1,
  252: LogicalKeyboardKey.tvAudioDescription,
  253: LogicalKeyboardKey.tvAudioDescriptionMixUp,
  254: LogicalKeyboardKey.tvAudioDescriptionMixDown,
  255: LogicalKeyboardKey.zoomToggle,
  256: LogicalKeyboardKey.tvContentsMenu,
  258: LogicalKeyboardKey.tvTimer,
  259: LogicalKeyboardKey.help,
  260: LogicalKeyboardKey.navigatePrevious,
  261: LogicalKeyboardKey.navigateNext,
  262: LogicalKeyboardKey.navigateIn,
  263: LogicalKeyboardKey.navigateOut,
  272: LogicalKeyboardKey.mediaSkipForward,
  273: LogicalKeyboardKey.mediaSkipBackward,
  274: LogicalKeyboardKey.mediaStepForward,
  275: LogicalKeyboardKey.mediaStepBackward,
  277: LogicalKeyboardKey.cut,
  278: LogicalKeyboardKey.copy,
  279: LogicalKeyboardKey.paste,
};

/// Maps Android-specific scan codes to the matching [PhysicalKeyboardKey].
const Map<int, PhysicalKeyboardKey> kAndroidToPhysicalKey = <int, PhysicalKeyboardKey>{
  1: PhysicalKeyboardKey.escape,
  2: PhysicalKeyboardKey.digit1,
  3: PhysicalKeyboardKey.digit2,
  4: PhysicalKeyboardKey.digit3,
  5: PhysicalKeyboardKey.digit4,
  6: PhysicalKeyboardKey.digit5,
  7: PhysicalKeyboardKey.digit6,
  8: PhysicalKeyboardKey.digit7,
  9: PhysicalKeyboardKey.digit8,
  10: PhysicalKeyboardKey.digit9,
  11: PhysicalKeyboardKey.digit0,
  12: PhysicalKeyboardKey.minus,
  13: PhysicalKeyboardKey.equal,
  14: PhysicalKeyboardKey.backspace,
  15: PhysicalKeyboardKey.tab,
  16: PhysicalKeyboardKey.keyQ,
  17: PhysicalKeyboardKey.keyW,
  18: PhysicalKeyboardKey.keyE,
  19: PhysicalKeyboardKey.keyR,
  20: PhysicalKeyboardKey.keyT,
  21: PhysicalKeyboardKey.keyY,
  22: PhysicalKeyboardKey.keyU,
  23: PhysicalKeyboardKey.keyI,
  24: PhysicalKeyboardKey.keyO,
  25: PhysicalKeyboardKey.keyP,
  26: PhysicalKeyboardKey.bracketLeft,
  27: PhysicalKeyboardKey.bracketRight,
  28: PhysicalKeyboardKey.enter,
  29: PhysicalKeyboardKey.controlLeft,
  30: PhysicalKeyboardKey.keyA,
  31: PhysicalKeyboardKey.keyS,
  32: PhysicalKeyboardKey.keyD,
  33: PhysicalKeyboardKey.keyF,
  34: PhysicalKeyboardKey.keyG,
  35: PhysicalKeyboardKey.keyH,
  36: PhysicalKeyboardKey.keyJ,
  37: PhysicalKeyboardKey.keyK,
  38: PhysicalKeyboardKey.keyL,
  39: PhysicalKeyboardKey.semicolon,
  40: PhysicalKeyboardKey.quote,
  41: PhysicalKeyboardKey.backquote,
  42: PhysicalKeyboardKey.shiftLeft,
  43: PhysicalKeyboardKey.backslash,
  44: PhysicalKeyboardKey.keyZ,
  45: PhysicalKeyboardKey.keyX,
  46: PhysicalKeyboardKey.keyC,
  47: PhysicalKeyboardKey.keyV,
  48: PhysicalKeyboardKey.keyB,
  49: PhysicalKeyboardKey.keyN,
  50: PhysicalKeyboardKey.keyM,
  51: PhysicalKeyboardKey.comma,
  52: PhysicalKeyboardKey.period,
  53: PhysicalKeyboardKey.slash,
  54: PhysicalKeyboardKey.shiftRight,
  55: PhysicalKeyboardKey.numpadMultiply,
  56: PhysicalKeyboardKey.altLeft,
  57: PhysicalKeyboardKey.space,
  58: PhysicalKeyboardKey.capsLock,
  59: PhysicalKeyboardKey.f1,
  60: PhysicalKeyboardKey.f2,
  61: PhysicalKeyboardKey.f3,
  62: PhysicalKeyboardKey.f4,
  63: PhysicalKeyboardKey.f5,
  64: PhysicalKeyboardKey.f6,
  65: PhysicalKeyboardKey.f7,
  66: PhysicalKeyboardKey.f8,
  67: PhysicalKeyboardKey.f9,
  68: PhysicalKeyboardKey.f10,
  69: PhysicalKeyboardKey.numLock,
  70: PhysicalKeyboardKey.scrollLock,
  71: PhysicalKeyboardKey.numpad7,
  72: PhysicalKeyboardKey.numpad8,
  73: PhysicalKeyboardKey.numpad9,
  74: PhysicalKeyboardKey.numpadSubtract,
  75: PhysicalKeyboardKey.numpad4,
  76: PhysicalKeyboardKey.numpad5,
  77: PhysicalKeyboardKey.numpad6,
  78: PhysicalKeyboardKey.numpadAdd,
  79: PhysicalKeyboardKey.numpad1,
  80: PhysicalKeyboardKey.numpad2,
  81: PhysicalKeyboardKey.numpad3,
  82: PhysicalKeyboardKey.numpad0,
  83: PhysicalKeyboardKey.numpadDecimal,
  86: PhysicalKeyboardKey.backslash,
  87: PhysicalKeyboardKey.f11,
  88: PhysicalKeyboardKey.f12,
  89: PhysicalKeyboardKey.intlRo,
  90: PhysicalKeyboardKey.lang3,
  91: PhysicalKeyboardKey.lang4,
  92: PhysicalKeyboardKey.convert,
  94: PhysicalKeyboardKey.nonConvert,
  95: PhysicalKeyboardKey.numpadComma,
  96: PhysicalKeyboardKey.numpadEnter,
  97: PhysicalKeyboardKey.controlRight,
  98: PhysicalKeyboardKey.numpadDivide,
  99: PhysicalKeyboardKey.printScreen,
  100: PhysicalKeyboardKey.altRight,
  102: PhysicalKeyboardKey.home,
  103: PhysicalKeyboardKey.arrowUp,
  104: PhysicalKeyboardKey.pageUp,
  105: PhysicalKeyboardKey.arrowLeft,
  106: PhysicalKeyboardKey.arrowRight,
  107: PhysicalKeyboardKey.end,
  108: PhysicalKeyboardKey.arrowDown,
  109: PhysicalKeyboardKey.pageDown,
  110: PhysicalKeyboardKey.insert,
  111: PhysicalKeyboardKey.delete,
  113: PhysicalKeyboardKey.audioVolumeMute,
  114: PhysicalKeyboardKey.audioVolumeDown,
  115: PhysicalKeyboardKey.audioVolumeUp,
  116: PhysicalKeyboardKey.power,
  117: PhysicalKeyboardKey.numpadEqual,
  119: PhysicalKeyboardKey.pause,
  121: PhysicalKeyboardKey.numpadComma,
  124: PhysicalKeyboardKey.intlYen,
  125: PhysicalKeyboardKey.metaLeft,
  126: PhysicalKeyboardKey.metaRight,
  127: PhysicalKeyboardKey.contextMenu,
  128: PhysicalKeyboardKey.mediaStop,
  129: PhysicalKeyboardKey.again,
  130: PhysicalKeyboardKey.props,
  131: PhysicalKeyboardKey.undo,
  133: PhysicalKeyboardKey.copy,
  134: PhysicalKeyboardKey.open,
  135: PhysicalKeyboardKey.paste,
  136: PhysicalKeyboardKey.find,
  137: PhysicalKeyboardKey.cut,
  138: PhysicalKeyboardKey.help,
  139: PhysicalKeyboardKey.contextMenu,
  142: PhysicalKeyboardKey.sleep,
  143: PhysicalKeyboardKey.wakeUp,
  152: PhysicalKeyboardKey.power,
  155: PhysicalKeyboardKey.launchMail,
  156: PhysicalKeyboardKey.browserFavorites,
  159: PhysicalKeyboardKey.browserForward,
  160: PhysicalKeyboardKey.close,
  161: PhysicalKeyboardKey.eject,
  162: PhysicalKeyboardKey.eject,
  163: PhysicalKeyboardKey.mediaTrackNext,
  164: PhysicalKeyboardKey.mediaPlayPause,
  165: PhysicalKeyboardKey.mediaTrackPrevious,
  166: PhysicalKeyboardKey.mediaStop,
  167: PhysicalKeyboardKey.mediaRecord,
  168: PhysicalKeyboardKey.mediaRewind,
  174: PhysicalKeyboardKey.exit,
  177: PhysicalKeyboardKey.pageUp,
  178: PhysicalKeyboardKey.pageDown,
  179: PhysicalKeyboardKey.numpadParenLeft,
  180: PhysicalKeyboardKey.numpadParenRight,
  182: PhysicalKeyboardKey.redo,
  183: PhysicalKeyboardKey.f13,
  184: PhysicalKeyboardKey.f14,
  185: PhysicalKeyboardKey.f15,
  186: PhysicalKeyboardKey.f16,
  187: PhysicalKeyboardKey.f17,
  188: PhysicalKeyboardKey.f18,
  189: PhysicalKeyboardKey.f19,
  190: PhysicalKeyboardKey.f20,
  191: PhysicalKeyboardKey.f21,
  192: PhysicalKeyboardKey.f22,
  193: PhysicalKeyboardKey.f23,
  194: PhysicalKeyboardKey.f24,
  200: PhysicalKeyboardKey.mediaPlay,
  201: PhysicalKeyboardKey.mediaPause,
  205: PhysicalKeyboardKey.suspend,
  206: PhysicalKeyboardKey.close,
  207: PhysicalKeyboardKey.mediaPlay,
  208: PhysicalKeyboardKey.mediaFastForward,
  209: PhysicalKeyboardKey.bassBoost,
  210: PhysicalKeyboardKey.print,
  215: PhysicalKeyboardKey.launchMail,
  217: PhysicalKeyboardKey.browserSearch,
  224: PhysicalKeyboardKey.brightnessDown,
  225: PhysicalKeyboardKey.brightnessUp,
  256: PhysicalKeyboardKey.gameButton1,
  257: PhysicalKeyboardKey.gameButton2,
  258: PhysicalKeyboardKey.gameButton3,
  259: PhysicalKeyboardKey.gameButton4,
  260: PhysicalKeyboardKey.gameButton5,
  261: PhysicalKeyboardKey.gameButton6,
  262: PhysicalKeyboardKey.gameButton7,
  263: PhysicalKeyboardKey.gameButton8,
  264: PhysicalKeyboardKey.gameButton9,
  265: PhysicalKeyboardKey.gameButton10,
  266: PhysicalKeyboardKey.gameButton11,
  267: PhysicalKeyboardKey.gameButton12,
  268: PhysicalKeyboardKey.gameButton13,
  269: PhysicalKeyboardKey.gameButton14,
  270: PhysicalKeyboardKey.gameButton15,
  271: PhysicalKeyboardKey.gameButton16,
  288: PhysicalKeyboardKey.gameButton1,
  289: PhysicalKeyboardKey.gameButton2,
  290: PhysicalKeyboardKey.gameButton3,
  291: PhysicalKeyboardKey.gameButton4,
  292: PhysicalKeyboardKey.gameButton5,
  293: PhysicalKeyboardKey.gameButton6,
  294: PhysicalKeyboardKey.gameButton7,
  295: PhysicalKeyboardKey.gameButton8,
  296: PhysicalKeyboardKey.gameButton9,
  297: PhysicalKeyboardKey.gameButton10,
  298: PhysicalKeyboardKey.gameButton11,
  299: PhysicalKeyboardKey.gameButton12,
  300: PhysicalKeyboardKey.gameButton13,
  301: PhysicalKeyboardKey.gameButton14,
  302: PhysicalKeyboardKey.gameButton15,
  303: PhysicalKeyboardKey.gameButton16,
  304: PhysicalKeyboardKey.gameButtonA,
  305: PhysicalKeyboardKey.gameButtonB,
  306: PhysicalKeyboardKey.gameButtonC,
  307: PhysicalKeyboardKey.gameButtonX,
  308: PhysicalKeyboardKey.gameButtonY,
  309: PhysicalKeyboardKey.gameButtonZ,
  310: PhysicalKeyboardKey.gameButtonLeft1,
  311: PhysicalKeyboardKey.gameButtonRight1,
  312: PhysicalKeyboardKey.gameButtonLeft2,
  313: PhysicalKeyboardKey.gameButtonRight2,
  314: PhysicalKeyboardKey.gameButtonSelect,
  315: PhysicalKeyboardKey.gameButtonStart,
  316: PhysicalKeyboardKey.gameButtonMode,
  317: PhysicalKeyboardKey.gameButtonThumbLeft,
  318: PhysicalKeyboardKey.gameButtonThumbRight,
  353: PhysicalKeyboardKey.select,
  358: PhysicalKeyboardKey.info,
  370: PhysicalKeyboardKey.closedCaptionToggle,
  397: PhysicalKeyboardKey.launchCalendar,
  402: PhysicalKeyboardKey.channelUp,
  403: PhysicalKeyboardKey.channelDown,
  405: PhysicalKeyboardKey.mediaLast,
  411: PhysicalKeyboardKey.pause,
  429: PhysicalKeyboardKey.launchContacts,
  464: PhysicalKeyboardKey.fn,
  583: PhysicalKeyboardKey.launchAssistant,
};

/// A map of Android key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kAndroidNumPadMap = <int, LogicalKeyboardKey>{
  144: LogicalKeyboardKey.numpad0,
  145: LogicalKeyboardKey.numpad1,
  146: LogicalKeyboardKey.numpad2,
  147: LogicalKeyboardKey.numpad3,
  148: LogicalKeyboardKey.numpad4,
  149: LogicalKeyboardKey.numpad5,
  150: LogicalKeyboardKey.numpad6,
  151: LogicalKeyboardKey.numpad7,
  152: LogicalKeyboardKey.numpad8,
  153: LogicalKeyboardKey.numpad9,
  154: LogicalKeyboardKey.numpadDivide,
  155: LogicalKeyboardKey.numpadMultiply,
  156: LogicalKeyboardKey.numpadSubtract,
  157: LogicalKeyboardKey.numpadAdd,
  158: LogicalKeyboardKey.numpadDecimal,
  159: LogicalKeyboardKey.numpadComma,
  161: LogicalKeyboardKey.numpadEqual,
  162: LogicalKeyboardKey.numpadParenLeft,
  163: LogicalKeyboardKey.numpadParenRight,
};

/// Maps Fuchsia-specific IDs to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kFuchsiaToLogicalKey = <int, LogicalKeyboardKey>{
  0x1200000010: LogicalKeyboardKey.hyper,
  0x1200000011: LogicalKeyboardKey.superKey,
  0x1200000012: LogicalKeyboardKey.fn,
  0x1200000013: LogicalKeyboardKey.fnLock,
  0x1200000014: LogicalKeyboardKey.suspend,
  0x1200000015: LogicalKeyboardKey.resume,
  0x1200010082: LogicalKeyboardKey.sleep,
  0x1200010083: LogicalKeyboardKey.wakeUp,
  0x120005ff01: LogicalKeyboardKey.gameButton1,
  0x120005ff02: LogicalKeyboardKey.gameButton2,
  0x120005ff03: LogicalKeyboardKey.gameButton3,
  0x120005ff04: LogicalKeyboardKey.gameButton4,
  0x120005ff05: LogicalKeyboardKey.gameButton5,
  0x120005ff06: LogicalKeyboardKey.gameButton6,
  0x120005ff07: LogicalKeyboardKey.gameButton7,
  0x120005ff08: LogicalKeyboardKey.gameButton8,
  0x120005ff09: LogicalKeyboardKey.gameButton9,
  0x120005ff0a: LogicalKeyboardKey.gameButton10,
  0x120005ff0b: LogicalKeyboardKey.gameButton11,
  0x120005ff0c: LogicalKeyboardKey.gameButton12,
  0x120005ff0d: LogicalKeyboardKey.gameButton13,
  0x120005ff0e: LogicalKeyboardKey.gameButton14,
  0x120005ff0f: LogicalKeyboardKey.gameButton15,
  0x120005ff10: LogicalKeyboardKey.gameButton16,
  0x120005ff11: LogicalKeyboardKey.gameButtonA,
  0x120005ff12: LogicalKeyboardKey.gameButtonB,
  0x120005ff13: LogicalKeyboardKey.gameButtonC,
  0x120005ff14: LogicalKeyboardKey.gameButtonLeft1,
  0x120005ff15: LogicalKeyboardKey.gameButtonLeft2,
  0x120005ff16: LogicalKeyboardKey.gameButtonMode,
  0x120005ff17: LogicalKeyboardKey.gameButtonRight1,
  0x120005ff18: LogicalKeyboardKey.gameButtonRight2,
  0x120005ff19: LogicalKeyboardKey.gameButtonSelect,
  0x120005ff1a: LogicalKeyboardKey.gameButtonStart,
  0x120005ff1b: LogicalKeyboardKey.gameButtonThumbLeft,
  0x120005ff1c: LogicalKeyboardKey.gameButtonThumbRight,
  0x120005ff1d: LogicalKeyboardKey.gameButtonX,
  0x120005ff1e: LogicalKeyboardKey.gameButtonY,
  0x120005ff1f: LogicalKeyboardKey.gameButtonZ,
  0x1200070004: LogicalKeyboardKey.keyA,
  0x1200070005: LogicalKeyboardKey.keyB,
  0x1200070006: LogicalKeyboardKey.keyC,
  0x1200070007: LogicalKeyboardKey.keyD,
  0x1200070008: LogicalKeyboardKey.keyE,
  0x1200070009: LogicalKeyboardKey.keyF,
  0x120007000a: LogicalKeyboardKey.keyG,
  0x120007000b: LogicalKeyboardKey.keyH,
  0x120007000c: LogicalKeyboardKey.keyI,
  0x120007000d: LogicalKeyboardKey.keyJ,
  0x120007000e: LogicalKeyboardKey.keyK,
  0x120007000f: LogicalKeyboardKey.keyL,
  0x1200070010: LogicalKeyboardKey.keyM,
  0x1200070011: LogicalKeyboardKey.keyN,
  0x1200070012: LogicalKeyboardKey.keyO,
  0x1200070013: LogicalKeyboardKey.keyP,
  0x1200070014: LogicalKeyboardKey.keyQ,
  0x1200070015: LogicalKeyboardKey.keyR,
  0x1200070016: LogicalKeyboardKey.keyS,
  0x1200070017: LogicalKeyboardKey.keyT,
  0x1200070018: LogicalKeyboardKey.keyU,
  0x1200070019: LogicalKeyboardKey.keyV,
  0x120007001a: LogicalKeyboardKey.keyW,
  0x120007001b: LogicalKeyboardKey.keyX,
  0x120007001c: LogicalKeyboardKey.keyY,
  0x120007001d: LogicalKeyboardKey.keyZ,
  0x120007001e: LogicalKeyboardKey.digit1,
  0x120007001f: LogicalKeyboardKey.digit2,
  0x1200070020: LogicalKeyboardKey.digit3,
  0x1200070021: LogicalKeyboardKey.digit4,
  0x1200070022: LogicalKeyboardKey.digit5,
  0x1200070023: LogicalKeyboardKey.digit6,
  0x1200070024: LogicalKeyboardKey.digit7,
  0x1200070025: LogicalKeyboardKey.digit8,
  0x1200070026: LogicalKeyboardKey.digit9,
  0x1200070027: LogicalKeyboardKey.digit0,
  0x1200070028: LogicalKeyboardKey.enter,
  0x1200070029: LogicalKeyboardKey.escape,
  0x120007002a: LogicalKeyboardKey.backspace,
  0x120007002b: LogicalKeyboardKey.tab,
  0x120007002c: LogicalKeyboardKey.space,
  0x120007002d: LogicalKeyboardKey.minus,
  0x120007002e: LogicalKeyboardKey.equal,
  0x120007002f: LogicalKeyboardKey.bracketLeft,
  0x1200070030: LogicalKeyboardKey.bracketRight,
  0x1200070031: LogicalKeyboardKey.backslash,
  0x1200070033: LogicalKeyboardKey.semicolon,
  0x1200070034: LogicalKeyboardKey.quote,
  0x1200070035: LogicalKeyboardKey.backquote,
  0x1200070036: LogicalKeyboardKey.comma,
  0x1200070037: LogicalKeyboardKey.period,
  0x1200070038: LogicalKeyboardKey.slash,
  0x1200070039: LogicalKeyboardKey.capsLock,
  0x120007003a: LogicalKeyboardKey.f1,
  0x120007003b: LogicalKeyboardKey.f2,
  0x120007003c: LogicalKeyboardKey.f3,
  0x120007003d: LogicalKeyboardKey.f4,
  0x120007003e: LogicalKeyboardKey.f5,
  0x120007003f: LogicalKeyboardKey.f6,
  0x1200070040: LogicalKeyboardKey.f7,
  0x1200070041: LogicalKeyboardKey.f8,
  0x1200070042: LogicalKeyboardKey.f9,
  0x1200070043: LogicalKeyboardKey.f10,
  0x1200070044: LogicalKeyboardKey.f11,
  0x1200070045: LogicalKeyboardKey.f12,
  0x1200070046: LogicalKeyboardKey.printScreen,
  0x1200070047: LogicalKeyboardKey.scrollLock,
  0x1200070048: LogicalKeyboardKey.pause,
  0x1200070049: LogicalKeyboardKey.insert,
  0x120007004a: LogicalKeyboardKey.home,
  0x120007004b: LogicalKeyboardKey.pageUp,
  0x120007004c: LogicalKeyboardKey.delete,
  0x120007004d: LogicalKeyboardKey.end,
  0x120007004e: LogicalKeyboardKey.pageDown,
  0x120007004f: LogicalKeyboardKey.arrowRight,
  0x1200070050: LogicalKeyboardKey.arrowLeft,
  0x1200070051: LogicalKeyboardKey.arrowDown,
  0x1200070052: LogicalKeyboardKey.arrowUp,
  0x1200070053: LogicalKeyboardKey.numLock,
  0x1200070054: LogicalKeyboardKey.numpadDivide,
  0x1200070055: LogicalKeyboardKey.numpadMultiply,
  0x1200070056: LogicalKeyboardKey.numpadSubtract,
  0x1200070057: LogicalKeyboardKey.numpadAdd,
  0x1200070058: LogicalKeyboardKey.numpadEnter,
  0x1200070059: LogicalKeyboardKey.numpad1,
  0x120007005a: LogicalKeyboardKey.numpad2,
  0x120007005b: LogicalKeyboardKey.numpad3,
  0x120007005c: LogicalKeyboardKey.numpad4,
  0x120007005d: LogicalKeyboardKey.numpad5,
  0x120007005e: LogicalKeyboardKey.numpad6,
  0x120007005f: LogicalKeyboardKey.numpad7,
  0x1200070060: LogicalKeyboardKey.numpad8,
  0x1200070061: LogicalKeyboardKey.numpad9,
  0x1200070062: LogicalKeyboardKey.numpad0,
  0x1200070063: LogicalKeyboardKey.numpadDecimal,
  0x1200070064: LogicalKeyboardKey.intlBackslash,
  0x1200070065: LogicalKeyboardKey.contextMenu,
  0x1200070066: LogicalKeyboardKey.power,
  0x1200070067: LogicalKeyboardKey.numpadEqual,
  0x1200070068: LogicalKeyboardKey.f13,
  0x1200070069: LogicalKeyboardKey.f14,
  0x120007006a: LogicalKeyboardKey.f15,
  0x120007006b: LogicalKeyboardKey.f16,
  0x120007006c: LogicalKeyboardKey.f17,
  0x120007006d: LogicalKeyboardKey.f18,
  0x120007006e: LogicalKeyboardKey.f19,
  0x120007006f: LogicalKeyboardKey.f20,
  0x1200070070: LogicalKeyboardKey.f21,
  0x1200070071: LogicalKeyboardKey.f22,
  0x1200070072: LogicalKeyboardKey.f23,
  0x1200070073: LogicalKeyboardKey.f24,
  0x1200070074: LogicalKeyboardKey.open,
  0x1200070075: LogicalKeyboardKey.help,
  0x1200070077: LogicalKeyboardKey.select,
  0x1200070079: LogicalKeyboardKey.again,
  0x120007007a: LogicalKeyboardKey.undo,
  0x120007007b: LogicalKeyboardKey.cut,
  0x120007007c: LogicalKeyboardKey.copy,
  0x120007007d: LogicalKeyboardKey.paste,
  0x120007007e: LogicalKeyboardKey.find,
  0x120007007f: LogicalKeyboardKey.audioVolumeMute,
  0x1200070080: LogicalKeyboardKey.audioVolumeUp,
  0x1200070081: LogicalKeyboardKey.audioVolumeDown,
  0x1200070085: LogicalKeyboardKey.numpadComma,
  0x1200070087: LogicalKeyboardKey.intlRo,
  0x1200070088: LogicalKeyboardKey.kanaMode,
  0x1200070089: LogicalKeyboardKey.intlYen,
  0x120007008a: LogicalKeyboardKey.convert,
  0x120007008b: LogicalKeyboardKey.nonConvert,
  0x1200070090: LogicalKeyboardKey.lang1,
  0x1200070091: LogicalKeyboardKey.lang2,
  0x1200070092: LogicalKeyboardKey.lang3,
  0x1200070093: LogicalKeyboardKey.lang4,
  0x1200070094: LogicalKeyboardKey.lang5,
  0x120007009b: LogicalKeyboardKey.abort,
  0x12000700a3: LogicalKeyboardKey.props,
  0x12000700b6: LogicalKeyboardKey.numpadParenLeft,
  0x12000700b7: LogicalKeyboardKey.numpadParenRight,
  0x12000700e0: LogicalKeyboardKey.controlLeft,
  0x12000700e1: LogicalKeyboardKey.shiftLeft,
  0x12000700e2: LogicalKeyboardKey.altLeft,
  0x12000700e3: LogicalKeyboardKey.metaLeft,
  0x12000700e4: LogicalKeyboardKey.controlRight,
  0x12000700e5: LogicalKeyboardKey.shiftRight,
  0x12000700e6: LogicalKeyboardKey.altRight,
  0x12000700e7: LogicalKeyboardKey.metaRight,
  0x12000c0060: LogicalKeyboardKey.info,
  0x12000c0061: LogicalKeyboardKey.closedCaptionToggle,
  0x12000c006f: LogicalKeyboardKey.brightnessUp,
  0x12000c0070: LogicalKeyboardKey.brightnessDown,
  0x12000c0083: LogicalKeyboardKey.mediaLast,
  0x12000c008c: LogicalKeyboardKey.launchPhone,
  0x12000c0094: LogicalKeyboardKey.exit,
  0x12000c009c: LogicalKeyboardKey.channelUp,
  0x12000c009d: LogicalKeyboardKey.channelDown,
  0x12000c00b0: LogicalKeyboardKey.mediaPlay,
  0x12000c00b1: LogicalKeyboardKey.mediaPause,
  0x12000c00b2: LogicalKeyboardKey.mediaRecord,
  0x12000c00b3: LogicalKeyboardKey.mediaFastForward,
  0x12000c00b4: LogicalKeyboardKey.mediaRewind,
  0x12000c00b5: LogicalKeyboardKey.mediaTrackNext,
  0x12000c00b6: LogicalKeyboardKey.mediaTrackPrevious,
  0x12000c00b7: LogicalKeyboardKey.mediaStop,
  0x12000c00b8: LogicalKeyboardKey.eject,
  0x12000c00cd: LogicalKeyboardKey.mediaPlayPause,
  0x12000c00cf: LogicalKeyboardKey.speechInputToggle,
  0x12000c0184: LogicalKeyboardKey.launchWordProcessor,
  0x12000c0186: LogicalKeyboardKey.launchSpreadsheet,
  0x12000c018a: LogicalKeyboardKey.launchMail,
  0x12000c018d: LogicalKeyboardKey.launchContacts,
  0x12000c018e: LogicalKeyboardKey.launchCalendar,
  0x12000c019c: LogicalKeyboardKey.logOff,
  0x12000c019f: LogicalKeyboardKey.launchControlPanel,
  0x12000c01ab: LogicalKeyboardKey.spellCheck,
  0x12000c01b1: LogicalKeyboardKey.launchScreenSaver,
  0x12000c01cb: LogicalKeyboardKey.launchAssistant,
  0x12000c0201: LogicalKeyboardKey.newKey,
  0x12000c0203: LogicalKeyboardKey.close,
  0x12000c0207: LogicalKeyboardKey.save,
  0x12000c0208: LogicalKeyboardKey.print,
  0x12000c0221: LogicalKeyboardKey.browserSearch,
  0x12000c0223: LogicalKeyboardKey.browserHome,
  0x12000c0224: LogicalKeyboardKey.browserBack,
  0x12000c0225: LogicalKeyboardKey.browserForward,
  0x12000c0226: LogicalKeyboardKey.browserStop,
  0x12000c0227: LogicalKeyboardKey.browserRefresh,
  0x12000c022a: LogicalKeyboardKey.browserFavorites,
  0x12000c022d: LogicalKeyboardKey.zoomIn,
  0x12000c022e: LogicalKeyboardKey.zoomOut,
  0x12000c0232: LogicalKeyboardKey.zoomToggle,
  0x12000c0279: LogicalKeyboardKey.redo,
  0x12000c0289: LogicalKeyboardKey.mailReply,
  0x12000c028b: LogicalKeyboardKey.mailForward,
  0x12000c028c: LogicalKeyboardKey.mailSend,
};

/// Maps Fuchsia-specific USB HID Usage IDs to the matching
/// [PhysicalKeyboardKey].
const Map<int, PhysicalKeyboardKey> kFuchsiaToPhysicalKey = <int, PhysicalKeyboardKey>{
  0x00000010: PhysicalKeyboardKey.hyper,
  0x00000011: PhysicalKeyboardKey.superKey,
  0x00000012: PhysicalKeyboardKey.fn,
  0x00000013: PhysicalKeyboardKey.fnLock,
  0x00000014: PhysicalKeyboardKey.suspend,
  0x00000015: PhysicalKeyboardKey.resume,
  0x00000016: PhysicalKeyboardKey.turbo,
  0x00000017: PhysicalKeyboardKey.privacyScreenToggle,
  0x00000018: PhysicalKeyboardKey.microphoneMuteToggle,
  0x00010082: PhysicalKeyboardKey.sleep,
  0x00010083: PhysicalKeyboardKey.wakeUp,
  0x000100b5: PhysicalKeyboardKey.displayToggleIntExt,
  0x0005ff01: PhysicalKeyboardKey.gameButton1,
  0x0005ff02: PhysicalKeyboardKey.gameButton2,
  0x0005ff03: PhysicalKeyboardKey.gameButton3,
  0x0005ff04: PhysicalKeyboardKey.gameButton4,
  0x0005ff05: PhysicalKeyboardKey.gameButton5,
  0x0005ff06: PhysicalKeyboardKey.gameButton6,
  0x0005ff07: PhysicalKeyboardKey.gameButton7,
  0x0005ff08: PhysicalKeyboardKey.gameButton8,
  0x0005ff09: PhysicalKeyboardKey.gameButton9,
  0x0005ff0a: PhysicalKeyboardKey.gameButton10,
  0x0005ff0b: PhysicalKeyboardKey.gameButton11,
  0x0005ff0c: PhysicalKeyboardKey.gameButton12,
  0x0005ff0d: PhysicalKeyboardKey.gameButton13,
  0x0005ff0e: PhysicalKeyboardKey.gameButton14,
  0x0005ff0f: PhysicalKeyboardKey.gameButton15,
  0x0005ff10: PhysicalKeyboardKey.gameButton16,
  0x0005ff11: PhysicalKeyboardKey.gameButtonA,
  0x0005ff12: PhysicalKeyboardKey.gameButtonB,
  0x0005ff13: PhysicalKeyboardKey.gameButtonC,
  0x0005ff14: PhysicalKeyboardKey.gameButtonLeft1,
  0x0005ff15: PhysicalKeyboardKey.gameButtonLeft2,
  0x0005ff16: PhysicalKeyboardKey.gameButtonMode,
  0x0005ff17: PhysicalKeyboardKey.gameButtonRight1,
  0x0005ff18: PhysicalKeyboardKey.gameButtonRight2,
  0x0005ff19: PhysicalKeyboardKey.gameButtonSelect,
  0x0005ff1a: PhysicalKeyboardKey.gameButtonStart,
  0x0005ff1b: PhysicalKeyboardKey.gameButtonThumbLeft,
  0x0005ff1c: PhysicalKeyboardKey.gameButtonThumbRight,
  0x0005ff1d: PhysicalKeyboardKey.gameButtonX,
  0x0005ff1e: PhysicalKeyboardKey.gameButtonY,
  0x0005ff1f: PhysicalKeyboardKey.gameButtonZ,
  0x00070000: PhysicalKeyboardKey.usbReserved,
  0x00070001: PhysicalKeyboardKey.usbErrorRollOver,
  0x00070002: PhysicalKeyboardKey.usbPostFail,
  0x00070003: PhysicalKeyboardKey.usbErrorUndefined,
  0x00070004: PhysicalKeyboardKey.keyA,
  0x00070005: PhysicalKeyboardKey.keyB,
  0x00070006: PhysicalKeyboardKey.keyC,
  0x00070007: PhysicalKeyboardKey.keyD,
  0x00070008: PhysicalKeyboardKey.keyE,
  0x00070009: PhysicalKeyboardKey.keyF,
  0x0007000a: PhysicalKeyboardKey.keyG,
  0x0007000b: PhysicalKeyboardKey.keyH,
  0x0007000c: PhysicalKeyboardKey.keyI,
  0x0007000d: PhysicalKeyboardKey.keyJ,
  0x0007000e: PhysicalKeyboardKey.keyK,
  0x0007000f: PhysicalKeyboardKey.keyL,
  0x00070010: PhysicalKeyboardKey.keyM,
  0x00070011: PhysicalKeyboardKey.keyN,
  0x00070012: PhysicalKeyboardKey.keyO,
  0x00070013: PhysicalKeyboardKey.keyP,
  0x00070014: PhysicalKeyboardKey.keyQ,
  0x00070015: PhysicalKeyboardKey.keyR,
  0x00070016: PhysicalKeyboardKey.keyS,
  0x00070017: PhysicalKeyboardKey.keyT,
  0x00070018: PhysicalKeyboardKey.keyU,
  0x00070019: PhysicalKeyboardKey.keyV,
  0x0007001a: PhysicalKeyboardKey.keyW,
  0x0007001b: PhysicalKeyboardKey.keyX,
  0x0007001c: PhysicalKeyboardKey.keyY,
  0x0007001d: PhysicalKeyboardKey.keyZ,
  0x0007001e: PhysicalKeyboardKey.digit1,
  0x0007001f: PhysicalKeyboardKey.digit2,
  0x00070020: PhysicalKeyboardKey.digit3,
  0x00070021: PhysicalKeyboardKey.digit4,
  0x00070022: PhysicalKeyboardKey.digit5,
  0x00070023: PhysicalKeyboardKey.digit6,
  0x00070024: PhysicalKeyboardKey.digit7,
  0x00070025: PhysicalKeyboardKey.digit8,
  0x00070026: PhysicalKeyboardKey.digit9,
  0x00070027: PhysicalKeyboardKey.digit0,
  0x00070028: PhysicalKeyboardKey.enter,
  0x00070029: PhysicalKeyboardKey.escape,
  0x0007002a: PhysicalKeyboardKey.backspace,
  0x0007002b: PhysicalKeyboardKey.tab,
  0x0007002c: PhysicalKeyboardKey.space,
  0x0007002d: PhysicalKeyboardKey.minus,
  0x0007002e: PhysicalKeyboardKey.equal,
  0x0007002f: PhysicalKeyboardKey.bracketLeft,
  0x00070030: PhysicalKeyboardKey.bracketRight,
  0x00070031: PhysicalKeyboardKey.backslash,
  0x00070033: PhysicalKeyboardKey.semicolon,
  0x00070034: PhysicalKeyboardKey.quote,
  0x00070035: PhysicalKeyboardKey.backquote,
  0x00070036: PhysicalKeyboardKey.comma,
  0x00070037: PhysicalKeyboardKey.period,
  0x00070038: PhysicalKeyboardKey.slash,
  0x00070039: PhysicalKeyboardKey.capsLock,
  0x0007003a: PhysicalKeyboardKey.f1,
  0x0007003b: PhysicalKeyboardKey.f2,
  0x0007003c: PhysicalKeyboardKey.f3,
  0x0007003d: PhysicalKeyboardKey.f4,
  0x0007003e: PhysicalKeyboardKey.f5,
  0x0007003f: PhysicalKeyboardKey.f6,
  0x00070040: PhysicalKeyboardKey.f7,
  0x00070041: PhysicalKeyboardKey.f8,
  0x00070042: PhysicalKeyboardKey.f9,
  0x00070043: PhysicalKeyboardKey.f10,
  0x00070044: PhysicalKeyboardKey.f11,
  0x00070045: PhysicalKeyboardKey.f12,
  0x00070046: PhysicalKeyboardKey.printScreen,
  0x00070047: PhysicalKeyboardKey.scrollLock,
  0x00070048: PhysicalKeyboardKey.pause,
  0x00070049: PhysicalKeyboardKey.insert,
  0x0007004a: PhysicalKeyboardKey.home,
  0x0007004b: PhysicalKeyboardKey.pageUp,
  0x0007004c: PhysicalKeyboardKey.delete,
  0x0007004d: PhysicalKeyboardKey.end,
  0x0007004e: PhysicalKeyboardKey.pageDown,
  0x0007004f: PhysicalKeyboardKey.arrowRight,
  0x00070050: PhysicalKeyboardKey.arrowLeft,
  0x00070051: PhysicalKeyboardKey.arrowDown,
  0x00070052: PhysicalKeyboardKey.arrowUp,
  0x00070053: PhysicalKeyboardKey.numLock,
  0x00070054: PhysicalKeyboardKey.numpadDivide,
  0x00070055: PhysicalKeyboardKey.numpadMultiply,
  0x00070056: PhysicalKeyboardKey.numpadSubtract,
  0x00070057: PhysicalKeyboardKey.numpadAdd,
  0x00070058: PhysicalKeyboardKey.numpadEnter,
  0x00070059: PhysicalKeyboardKey.numpad1,
  0x0007005a: PhysicalKeyboardKey.numpad2,
  0x0007005b: PhysicalKeyboardKey.numpad3,
  0x0007005c: PhysicalKeyboardKey.numpad4,
  0x0007005d: PhysicalKeyboardKey.numpad5,
  0x0007005e: PhysicalKeyboardKey.numpad6,
  0x0007005f: PhysicalKeyboardKey.numpad7,
  0x00070060: PhysicalKeyboardKey.numpad8,
  0x00070061: PhysicalKeyboardKey.numpad9,
  0x00070062: PhysicalKeyboardKey.numpad0,
  0x00070063: PhysicalKeyboardKey.numpadDecimal,
  0x00070064: PhysicalKeyboardKey.intlBackslash,
  0x00070065: PhysicalKeyboardKey.contextMenu,
  0x00070066: PhysicalKeyboardKey.power,
  0x00070067: PhysicalKeyboardKey.numpadEqual,
  0x00070068: PhysicalKeyboardKey.f13,
  0x00070069: PhysicalKeyboardKey.f14,
  0x0007006a: PhysicalKeyboardKey.f15,
  0x0007006b: PhysicalKeyboardKey.f16,
  0x0007006c: PhysicalKeyboardKey.f17,
  0x0007006d: PhysicalKeyboardKey.f18,
  0x0007006e: PhysicalKeyboardKey.f19,
  0x0007006f: PhysicalKeyboardKey.f20,
  0x00070070: PhysicalKeyboardKey.f21,
  0x00070071: PhysicalKeyboardKey.f22,
  0x00070072: PhysicalKeyboardKey.f23,
  0x00070073: PhysicalKeyboardKey.f24,
  0x00070074: PhysicalKeyboardKey.open,
  0x00070075: PhysicalKeyboardKey.help,
  0x00070077: PhysicalKeyboardKey.select,
  0x00070079: PhysicalKeyboardKey.again,
  0x0007007a: PhysicalKeyboardKey.undo,
  0x0007007b: PhysicalKeyboardKey.cut,
  0x0007007c: PhysicalKeyboardKey.copy,
  0x0007007d: PhysicalKeyboardKey.paste,
  0x0007007e: PhysicalKeyboardKey.find,
  0x0007007f: PhysicalKeyboardKey.audioVolumeMute,
  0x00070080: PhysicalKeyboardKey.audioVolumeUp,
  0x00070081: PhysicalKeyboardKey.audioVolumeDown,
  0x00070085: PhysicalKeyboardKey.numpadComma,
  0x00070087: PhysicalKeyboardKey.intlRo,
  0x00070088: PhysicalKeyboardKey.kanaMode,
  0x00070089: PhysicalKeyboardKey.intlYen,
  0x0007008a: PhysicalKeyboardKey.convert,
  0x0007008b: PhysicalKeyboardKey.nonConvert,
  0x00070090: PhysicalKeyboardKey.lang1,
  0x00070091: PhysicalKeyboardKey.lang2,
  0x00070092: PhysicalKeyboardKey.lang3,
  0x00070093: PhysicalKeyboardKey.lang4,
  0x00070094: PhysicalKeyboardKey.lang5,
  0x0007009b: PhysicalKeyboardKey.abort,
  0x000700a3: PhysicalKeyboardKey.props,
  0x000700b6: PhysicalKeyboardKey.numpadParenLeft,
  0x000700b7: PhysicalKeyboardKey.numpadParenRight,
  0x000700bb: PhysicalKeyboardKey.numpadBackspace,
  0x000700d0: PhysicalKeyboardKey.numpadMemoryStore,
  0x000700d1: PhysicalKeyboardKey.numpadMemoryRecall,
  0x000700d2: PhysicalKeyboardKey.numpadMemoryClear,
  0x000700d3: PhysicalKeyboardKey.numpadMemoryAdd,
  0x000700d4: PhysicalKeyboardKey.numpadMemorySubtract,
  0x000700d7: PhysicalKeyboardKey.numpadSignChange,
  0x000700d8: PhysicalKeyboardKey.numpadClear,
  0x000700d9: PhysicalKeyboardKey.numpadClearEntry,
  0x000700e0: PhysicalKeyboardKey.controlLeft,
  0x000700e1: PhysicalKeyboardKey.shiftLeft,
  0x000700e2: PhysicalKeyboardKey.altLeft,
  0x000700e3: PhysicalKeyboardKey.metaLeft,
  0x000700e4: PhysicalKeyboardKey.controlRight,
  0x000700e5: PhysicalKeyboardKey.shiftRight,
  0x000700e6: PhysicalKeyboardKey.altRight,
  0x000700e7: PhysicalKeyboardKey.metaRight,
  0x000c0060: PhysicalKeyboardKey.info,
  0x000c0061: PhysicalKeyboardKey.closedCaptionToggle,
  0x000c006f: PhysicalKeyboardKey.brightnessUp,
  0x000c0070: PhysicalKeyboardKey.brightnessDown,
  0x000c0072: PhysicalKeyboardKey.brightnessToggle,
  0x000c0073: PhysicalKeyboardKey.brightnessMinimum,
  0x000c0074: PhysicalKeyboardKey.brightnessMaximum,
  0x000c0075: PhysicalKeyboardKey.brightnessAuto,
  0x000c0079: PhysicalKeyboardKey.kbdIllumUp,
  0x000c007a: PhysicalKeyboardKey.kbdIllumDown,
  0x000c0083: PhysicalKeyboardKey.mediaLast,
  0x000c008c: PhysicalKeyboardKey.launchPhone,
  0x000c008d: PhysicalKeyboardKey.programGuide,
  0x000c0094: PhysicalKeyboardKey.exit,
  0x000c009c: PhysicalKeyboardKey.channelUp,
  0x000c009d: PhysicalKeyboardKey.channelDown,
  0x000c00b0: PhysicalKeyboardKey.mediaPlay,
  0x000c00b1: PhysicalKeyboardKey.mediaPause,
  0x000c00b2: PhysicalKeyboardKey.mediaRecord,
  0x000c00b3: PhysicalKeyboardKey.mediaFastForward,
  0x000c00b4: PhysicalKeyboardKey.mediaRewind,
  0x000c00b5: PhysicalKeyboardKey.mediaTrackNext,
  0x000c00b6: PhysicalKeyboardKey.mediaTrackPrevious,
  0x000c00b7: PhysicalKeyboardKey.mediaStop,
  0x000c00b8: PhysicalKeyboardKey.eject,
  0x000c00cd: PhysicalKeyboardKey.mediaPlayPause,
  0x000c00cf: PhysicalKeyboardKey.speechInputToggle,
  0x000c00e5: PhysicalKeyboardKey.bassBoost,
  0x000c0183: PhysicalKeyboardKey.mediaSelect,
  0x000c0184: PhysicalKeyboardKey.launchWordProcessor,
  0x000c0186: PhysicalKeyboardKey.launchSpreadsheet,
  0x000c018a: PhysicalKeyboardKey.launchMail,
  0x000c018d: PhysicalKeyboardKey.launchContacts,
  0x000c018e: PhysicalKeyboardKey.launchCalendar,
  0x000c0192: PhysicalKeyboardKey.launchApp2,
  0x000c0194: PhysicalKeyboardKey.launchApp1,
  0x000c0196: PhysicalKeyboardKey.launchInternetBrowser,
  0x000c019c: PhysicalKeyboardKey.logOff,
  0x000c019e: PhysicalKeyboardKey.lockScreen,
  0x000c019f: PhysicalKeyboardKey.launchControlPanel,
  0x000c01a2: PhysicalKeyboardKey.selectTask,
  0x000c01a7: PhysicalKeyboardKey.launchDocuments,
  0x000c01ab: PhysicalKeyboardKey.spellCheck,
  0x000c01ae: PhysicalKeyboardKey.launchKeyboardLayout,
  0x000c01b1: PhysicalKeyboardKey.launchScreenSaver,
  0x000c01b7: PhysicalKeyboardKey.launchAudioBrowser,
  0x000c01cb: PhysicalKeyboardKey.launchAssistant,
  0x000c0201: PhysicalKeyboardKey.newKey,
  0x000c0203: PhysicalKeyboardKey.close,
  0x000c0207: PhysicalKeyboardKey.save,
  0x000c0208: PhysicalKeyboardKey.print,
  0x000c0221: PhysicalKeyboardKey.browserSearch,
  0x000c0223: PhysicalKeyboardKey.browserHome,
  0x000c0224: PhysicalKeyboardKey.browserBack,
  0x000c0225: PhysicalKeyboardKey.browserForward,
  0x000c0226: PhysicalKeyboardKey.browserStop,
  0x000c0227: PhysicalKeyboardKey.browserRefresh,
  0x000c022a: PhysicalKeyboardKey.browserFavorites,
  0x000c022d: PhysicalKeyboardKey.zoomIn,
  0x000c022e: PhysicalKeyboardKey.zoomOut,
  0x000c0232: PhysicalKeyboardKey.zoomToggle,
  0x000c0279: PhysicalKeyboardKey.redo,
  0x000c0289: PhysicalKeyboardKey.mailReply,
  0x000c028b: PhysicalKeyboardKey.mailForward,
  0x000c028c: PhysicalKeyboardKey.mailSend,
  0x000c029d: PhysicalKeyboardKey.keyboardLayoutSelect,
  0x000c029f: PhysicalKeyboardKey.showAllWindows,
};

/// Maps macOS-specific key code values representing [PhysicalKeyboardKey].
///
/// MacOS doesn't provide a scan code, but a virtual keycode to represent a physical key.
const Map<int, PhysicalKeyboardKey> kMacOsToPhysicalKey = <int, PhysicalKeyboardKey>{
  0x00000000: PhysicalKeyboardKey.keyA,
  0x00000001: PhysicalKeyboardKey.keyS,
  0x00000002: PhysicalKeyboardKey.keyD,
  0x00000003: PhysicalKeyboardKey.keyF,
  0x00000004: PhysicalKeyboardKey.keyH,
  0x00000005: PhysicalKeyboardKey.keyG,
  0x00000006: PhysicalKeyboardKey.keyZ,
  0x00000007: PhysicalKeyboardKey.keyX,
  0x00000008: PhysicalKeyboardKey.keyC,
  0x00000009: PhysicalKeyboardKey.keyV,
  0x0000000a: PhysicalKeyboardKey.intlBackslash,
  0x0000000b: PhysicalKeyboardKey.keyB,
  0x0000000c: PhysicalKeyboardKey.keyQ,
  0x0000000d: PhysicalKeyboardKey.keyW,
  0x0000000e: PhysicalKeyboardKey.keyE,
  0x0000000f: PhysicalKeyboardKey.keyR,
  0x00000010: PhysicalKeyboardKey.keyY,
  0x00000011: PhysicalKeyboardKey.keyT,
  0x00000012: PhysicalKeyboardKey.digit1,
  0x00000013: PhysicalKeyboardKey.digit2,
  0x00000014: PhysicalKeyboardKey.digit3,
  0x00000015: PhysicalKeyboardKey.digit4,
  0x00000016: PhysicalKeyboardKey.digit6,
  0x00000017: PhysicalKeyboardKey.digit5,
  0x00000018: PhysicalKeyboardKey.equal,
  0x00000019: PhysicalKeyboardKey.digit9,
  0x0000001a: PhysicalKeyboardKey.digit7,
  0x0000001b: PhysicalKeyboardKey.minus,
  0x0000001c: PhysicalKeyboardKey.digit8,
  0x0000001d: PhysicalKeyboardKey.digit0,
  0x0000001e: PhysicalKeyboardKey.bracketRight,
  0x0000001f: PhysicalKeyboardKey.keyO,
  0x00000020: PhysicalKeyboardKey.keyU,
  0x00000021: PhysicalKeyboardKey.bracketLeft,
  0x00000022: PhysicalKeyboardKey.keyI,
  0x00000023: PhysicalKeyboardKey.keyP,
  0x00000024: PhysicalKeyboardKey.enter,
  0x00000025: PhysicalKeyboardKey.keyL,
  0x00000026: PhysicalKeyboardKey.keyJ,
  0x00000027: PhysicalKeyboardKey.quote,
  0x00000028: PhysicalKeyboardKey.keyK,
  0x00000029: PhysicalKeyboardKey.semicolon,
  0x0000002a: PhysicalKeyboardKey.backslash,
  0x0000002b: PhysicalKeyboardKey.comma,
  0x0000002c: PhysicalKeyboardKey.slash,
  0x0000002d: PhysicalKeyboardKey.keyN,
  0x0000002e: PhysicalKeyboardKey.keyM,
  0x0000002f: PhysicalKeyboardKey.period,
  0x00000030: PhysicalKeyboardKey.tab,
  0x00000031: PhysicalKeyboardKey.space,
  0x00000032: PhysicalKeyboardKey.backquote,
  0x00000033: PhysicalKeyboardKey.backspace,
  0x00000035: PhysicalKeyboardKey.escape,
  0x00000036: PhysicalKeyboardKey.metaRight,
  0x00000037: PhysicalKeyboardKey.metaLeft,
  0x00000038: PhysicalKeyboardKey.shiftLeft,
  0x00000039: PhysicalKeyboardKey.capsLock,
  0x0000003a: PhysicalKeyboardKey.altLeft,
  0x0000003b: PhysicalKeyboardKey.controlLeft,
  0x0000003c: PhysicalKeyboardKey.shiftRight,
  0x0000003d: PhysicalKeyboardKey.altRight,
  0x0000003e: PhysicalKeyboardKey.controlRight,
  0x0000003f: PhysicalKeyboardKey.fn,
  0x00000040: PhysicalKeyboardKey.f17,
  0x00000041: PhysicalKeyboardKey.numpadDecimal,
  0x00000043: PhysicalKeyboardKey.numpadMultiply,
  0x00000045: PhysicalKeyboardKey.numpadAdd,
  0x00000047: PhysicalKeyboardKey.numLock,
  0x00000048: PhysicalKeyboardKey.audioVolumeUp,
  0x00000049: PhysicalKeyboardKey.audioVolumeDown,
  0x0000004a: PhysicalKeyboardKey.audioVolumeMute,
  0x0000004b: PhysicalKeyboardKey.numpadDivide,
  0x0000004c: PhysicalKeyboardKey.numpadEnter,
  0x0000004e: PhysicalKeyboardKey.numpadSubtract,
  0x0000004f: PhysicalKeyboardKey.f18,
  0x00000050: PhysicalKeyboardKey.f19,
  0x00000051: PhysicalKeyboardKey.numpadEqual,
  0x00000052: PhysicalKeyboardKey.numpad0,
  0x00000053: PhysicalKeyboardKey.numpad1,
  0x00000054: PhysicalKeyboardKey.numpad2,
  0x00000055: PhysicalKeyboardKey.numpad3,
  0x00000056: PhysicalKeyboardKey.numpad4,
  0x00000057: PhysicalKeyboardKey.numpad5,
  0x00000058: PhysicalKeyboardKey.numpad6,
  0x00000059: PhysicalKeyboardKey.numpad7,
  0x0000005a: PhysicalKeyboardKey.f20,
  0x0000005b: PhysicalKeyboardKey.numpad8,
  0x0000005c: PhysicalKeyboardKey.numpad9,
  0x0000005d: PhysicalKeyboardKey.intlYen,
  0x0000005e: PhysicalKeyboardKey.intlRo,
  0x0000005f: PhysicalKeyboardKey.numpadComma,
  0x00000060: PhysicalKeyboardKey.f5,
  0x00000061: PhysicalKeyboardKey.f6,
  0x00000062: PhysicalKeyboardKey.f7,
  0x00000063: PhysicalKeyboardKey.f3,
  0x00000064: PhysicalKeyboardKey.f8,
  0x00000065: PhysicalKeyboardKey.f9,
  0x00000066: PhysicalKeyboardKey.lang2,
  0x00000067: PhysicalKeyboardKey.f11,
  0x00000068: PhysicalKeyboardKey.lang1,
  0x00000069: PhysicalKeyboardKey.f13,
  0x0000006a: PhysicalKeyboardKey.f16,
  0x0000006b: PhysicalKeyboardKey.f14,
  0x0000006d: PhysicalKeyboardKey.f10,
  0x0000006e: PhysicalKeyboardKey.contextMenu,
  0x0000006f: PhysicalKeyboardKey.f12,
  0x00000071: PhysicalKeyboardKey.f15,
  0x00000072: PhysicalKeyboardKey.insert,
  0x00000073: PhysicalKeyboardKey.home,
  0x00000074: PhysicalKeyboardKey.pageUp,
  0x00000075: PhysicalKeyboardKey.delete,
  0x00000076: PhysicalKeyboardKey.f4,
  0x00000077: PhysicalKeyboardKey.end,
  0x00000078: PhysicalKeyboardKey.f2,
  0x00000079: PhysicalKeyboardKey.pageDown,
  0x0000007a: PhysicalKeyboardKey.f1,
  0x0000007b: PhysicalKeyboardKey.arrowLeft,
  0x0000007c: PhysicalKeyboardKey.arrowRight,
  0x0000007d: PhysicalKeyboardKey.arrowDown,
  0x0000007e: PhysicalKeyboardKey.arrowUp,
};

/// A map of macOS key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kMacOsNumPadMap = <int, LogicalKeyboardKey>{
  0x00000041: LogicalKeyboardKey.numpadDecimal,
  0x00000043: LogicalKeyboardKey.numpadMultiply,
  0x00000045: LogicalKeyboardKey.numpadAdd,
  0x0000004b: LogicalKeyboardKey.numpadDivide,
  0x0000004e: LogicalKeyboardKey.numpadSubtract,
  0x00000051: LogicalKeyboardKey.numpadEqual,
  0x00000052: LogicalKeyboardKey.numpad0,
  0x00000053: LogicalKeyboardKey.numpad1,
  0x00000054: LogicalKeyboardKey.numpad2,
  0x00000055: LogicalKeyboardKey.numpad3,
  0x00000056: LogicalKeyboardKey.numpad4,
  0x00000057: LogicalKeyboardKey.numpad5,
  0x00000058: LogicalKeyboardKey.numpad6,
  0x00000059: LogicalKeyboardKey.numpad7,
  0x0000005b: LogicalKeyboardKey.numpad8,
  0x0000005c: LogicalKeyboardKey.numpad9,
  0x0000005f: LogicalKeyboardKey.numpadComma,
};

/// A map of macOS key codes which are numbered function keys, so that they
/// can be excluded when asking "is the Fn modifier down?".
const Map<int, LogicalKeyboardKey> kMacOsFunctionKeyMap = <int, LogicalKeyboardKey>{
  0x00000040: LogicalKeyboardKey.f17,
  0x0000004f: LogicalKeyboardKey.f18,
  0x00000050: LogicalKeyboardKey.f19,
  0x0000005a: LogicalKeyboardKey.f20,
  0x00000060: LogicalKeyboardKey.f5,
  0x00000061: LogicalKeyboardKey.f6,
  0x00000062: LogicalKeyboardKey.f7,
  0x00000063: LogicalKeyboardKey.f3,
  0x00000064: LogicalKeyboardKey.f8,
  0x00000065: LogicalKeyboardKey.f9,
  0x00000067: LogicalKeyboardKey.f11,
  0x00000069: LogicalKeyboardKey.f13,
  0x0000006a: LogicalKeyboardKey.f16,
  0x0000006b: LogicalKeyboardKey.f14,
  0x0000006d: LogicalKeyboardKey.f10,
  0x0000006f: LogicalKeyboardKey.f12,
  0x00000071: LogicalKeyboardKey.f15,
  0x00000076: LogicalKeyboardKey.f4,
  0x00000078: LogicalKeyboardKey.f2,
  0x0000007a: LogicalKeyboardKey.f1,
};

/// A map of macOS key codes presenting [LogicalKeyboardKey].
///
/// Logical key codes are not available in macOS key events. Most of the logical keys
/// are derived from its `characterIgnoringModifiers`, but those keys that don't
/// have a character representation will be derived from their key codes using
/// this map.
const Map<int, LogicalKeyboardKey> kMacOsToLogicalKey = <int, LogicalKeyboardKey>{
  36: LogicalKeyboardKey.enter,
  48: LogicalKeyboardKey.tab,
  51: LogicalKeyboardKey.backspace,
  53: LogicalKeyboardKey.escape,
  54: LogicalKeyboardKey.metaRight,
  55: LogicalKeyboardKey.metaLeft,
  56: LogicalKeyboardKey.shiftLeft,
  57: LogicalKeyboardKey.capsLock,
  58: LogicalKeyboardKey.altLeft,
  59: LogicalKeyboardKey.controlLeft,
  60: LogicalKeyboardKey.shiftRight,
  61: LogicalKeyboardKey.altRight,
  62: LogicalKeyboardKey.controlRight,
  63: LogicalKeyboardKey.fn,
  64: LogicalKeyboardKey.f17,
  65: LogicalKeyboardKey.numpadDecimal,
  67: LogicalKeyboardKey.numpadMultiply,
  69: LogicalKeyboardKey.numpadAdd,
  71: LogicalKeyboardKey.numLock,
  72: LogicalKeyboardKey.audioVolumeUp,
  73: LogicalKeyboardKey.audioVolumeDown,
  74: LogicalKeyboardKey.audioVolumeMute,
  75: LogicalKeyboardKey.numpadDivide,
  76: LogicalKeyboardKey.numpadEnter,
  78: LogicalKeyboardKey.numpadSubtract,
  79: LogicalKeyboardKey.f18,
  80: LogicalKeyboardKey.f19,
  81: LogicalKeyboardKey.numpadEqual,
  82: LogicalKeyboardKey.numpad0,
  83: LogicalKeyboardKey.numpad1,
  84: LogicalKeyboardKey.numpad2,
  85: LogicalKeyboardKey.numpad3,
  86: LogicalKeyboardKey.numpad4,
  87: LogicalKeyboardKey.numpad5,
  88: LogicalKeyboardKey.numpad6,
  89: LogicalKeyboardKey.numpad7,
  90: LogicalKeyboardKey.f20,
  91: LogicalKeyboardKey.numpad8,
  92: LogicalKeyboardKey.numpad9,
  93: LogicalKeyboardKey.intlYen,
  94: LogicalKeyboardKey.intlRo,
  95: LogicalKeyboardKey.numpadComma,
  96: LogicalKeyboardKey.f5,
  97: LogicalKeyboardKey.f6,
  98: LogicalKeyboardKey.f7,
  99: LogicalKeyboardKey.f3,
  100: LogicalKeyboardKey.f8,
  101: LogicalKeyboardKey.f9,
  102: LogicalKeyboardKey.lang2,
  103: LogicalKeyboardKey.f11,
  104: LogicalKeyboardKey.lang1,
  105: LogicalKeyboardKey.f13,
  106: LogicalKeyboardKey.f16,
  107: LogicalKeyboardKey.f14,
  109: LogicalKeyboardKey.f10,
  110: LogicalKeyboardKey.contextMenu,
  111: LogicalKeyboardKey.f12,
  113: LogicalKeyboardKey.f15,
  114: LogicalKeyboardKey.insert,
  115: LogicalKeyboardKey.home,
  116: LogicalKeyboardKey.pageUp,
  117: LogicalKeyboardKey.delete,
  118: LogicalKeyboardKey.f4,
  119: LogicalKeyboardKey.end,
  120: LogicalKeyboardKey.f2,
  121: LogicalKeyboardKey.pageDown,
  122: LogicalKeyboardKey.f1,
  123: LogicalKeyboardKey.arrowLeft,
  124: LogicalKeyboardKey.arrowRight,
  125: LogicalKeyboardKey.arrowDown,
  126: LogicalKeyboardKey.arrowUp,
};

/// Maps iOS-specific key code values representing [PhysicalKeyboardKey].
///
/// iOS doesn't provide a scan code, but a virtual keycode to represent a physical key.
const Map<int, PhysicalKeyboardKey> kIosToPhysicalKey = <int, PhysicalKeyboardKey>{
  0x00000000: PhysicalKeyboardKey.usbReserved,
  0x00000001: PhysicalKeyboardKey.usbErrorRollOver,
  0x00000002: PhysicalKeyboardKey.usbPostFail,
  0x00000003: PhysicalKeyboardKey.usbErrorUndefined,
  0x00000004: PhysicalKeyboardKey.keyA,
  0x00000005: PhysicalKeyboardKey.keyB,
  0x00000006: PhysicalKeyboardKey.keyC,
  0x00000007: PhysicalKeyboardKey.keyD,
  0x00000008: PhysicalKeyboardKey.keyE,
  0x00000009: PhysicalKeyboardKey.keyF,
  0x0000000a: PhysicalKeyboardKey.keyG,
  0x0000000b: PhysicalKeyboardKey.keyH,
  0x0000000c: PhysicalKeyboardKey.keyI,
  0x0000000d: PhysicalKeyboardKey.keyJ,
  0x0000000e: PhysicalKeyboardKey.keyK,
  0x0000000f: PhysicalKeyboardKey.keyL,
  0x00000010: PhysicalKeyboardKey.keyM,
  0x00000011: PhysicalKeyboardKey.keyN,
  0x00000012: PhysicalKeyboardKey.keyO,
  0x00000013: PhysicalKeyboardKey.keyP,
  0x00000014: PhysicalKeyboardKey.keyQ,
  0x00000015: PhysicalKeyboardKey.keyR,
  0x00000016: PhysicalKeyboardKey.keyS,
  0x00000017: PhysicalKeyboardKey.keyT,
  0x00000018: PhysicalKeyboardKey.keyU,
  0x00000019: PhysicalKeyboardKey.keyV,
  0x0000001a: PhysicalKeyboardKey.keyW,
  0x0000001b: PhysicalKeyboardKey.keyX,
  0x0000001c: PhysicalKeyboardKey.keyY,
  0x0000001d: PhysicalKeyboardKey.keyZ,
  0x0000001e: PhysicalKeyboardKey.digit1,
  0x0000001f: PhysicalKeyboardKey.digit2,
  0x00000020: PhysicalKeyboardKey.digit3,
  0x00000021: PhysicalKeyboardKey.digit4,
  0x00000022: PhysicalKeyboardKey.digit5,
  0x00000023: PhysicalKeyboardKey.digit6,
  0x00000024: PhysicalKeyboardKey.digit7,
  0x00000025: PhysicalKeyboardKey.digit8,
  0x00000026: PhysicalKeyboardKey.digit9,
  0x00000027: PhysicalKeyboardKey.digit0,
  0x00000028: PhysicalKeyboardKey.enter,
  0x00000029: PhysicalKeyboardKey.escape,
  0x0000002a: PhysicalKeyboardKey.backspace,
  0x0000002b: PhysicalKeyboardKey.tab,
  0x0000002c: PhysicalKeyboardKey.space,
  0x0000002d: PhysicalKeyboardKey.minus,
  0x0000002e: PhysicalKeyboardKey.equal,
  0x0000002f: PhysicalKeyboardKey.bracketLeft,
  0x00000030: PhysicalKeyboardKey.bracketRight,
  0x00000031: PhysicalKeyboardKey.backslash,
  0x00000033: PhysicalKeyboardKey.semicolon,
  0x00000034: PhysicalKeyboardKey.quote,
  0x00000035: PhysicalKeyboardKey.backquote,
  0x00000036: PhysicalKeyboardKey.comma,
  0x00000037: PhysicalKeyboardKey.period,
  0x00000038: PhysicalKeyboardKey.slash,
  0x00000039: PhysicalKeyboardKey.capsLock,
  0x0000003a: PhysicalKeyboardKey.f1,
  0x0000003b: PhysicalKeyboardKey.f2,
  0x0000003c: PhysicalKeyboardKey.f3,
  0x0000003d: PhysicalKeyboardKey.f4,
  0x0000003e: PhysicalKeyboardKey.f5,
  0x0000003f: PhysicalKeyboardKey.f6,
  0x00000040: PhysicalKeyboardKey.f7,
  0x00000041: PhysicalKeyboardKey.f8,
  0x00000042: PhysicalKeyboardKey.f9,
  0x00000043: PhysicalKeyboardKey.f10,
  0x00000044: PhysicalKeyboardKey.f11,
  0x00000045: PhysicalKeyboardKey.f12,
  0x00000046: PhysicalKeyboardKey.printScreen,
  0x00000047: PhysicalKeyboardKey.scrollLock,
  0x00000048: PhysicalKeyboardKey.pause,
  0x00000049: PhysicalKeyboardKey.insert,
  0x0000004a: PhysicalKeyboardKey.home,
  0x0000004b: PhysicalKeyboardKey.pageUp,
  0x0000004c: PhysicalKeyboardKey.delete,
  0x0000004d: PhysicalKeyboardKey.end,
  0x0000004e: PhysicalKeyboardKey.pageDown,
  0x0000004f: PhysicalKeyboardKey.arrowRight,
  0x00000050: PhysicalKeyboardKey.arrowLeft,
  0x00000051: PhysicalKeyboardKey.arrowDown,
  0x00000052: PhysicalKeyboardKey.arrowUp,
  0x00000053: PhysicalKeyboardKey.numLock,
  0x00000054: PhysicalKeyboardKey.numpadDivide,
  0x00000055: PhysicalKeyboardKey.numpadMultiply,
  0x00000056: PhysicalKeyboardKey.numpadSubtract,
  0x00000057: PhysicalKeyboardKey.numpadAdd,
  0x00000058: PhysicalKeyboardKey.numpadEnter,
  0x00000059: PhysicalKeyboardKey.numpad1,
  0x0000005a: PhysicalKeyboardKey.numpad2,
  0x0000005b: PhysicalKeyboardKey.numpad3,
  0x0000005c: PhysicalKeyboardKey.numpad4,
  0x0000005d: PhysicalKeyboardKey.numpad5,
  0x0000005e: PhysicalKeyboardKey.numpad6,
  0x0000005f: PhysicalKeyboardKey.numpad7,
  0x00000060: PhysicalKeyboardKey.numpad8,
  0x00000061: PhysicalKeyboardKey.numpad9,
  0x00000062: PhysicalKeyboardKey.numpad0,
  0x00000063: PhysicalKeyboardKey.numpadDecimal,
  0x00000064: PhysicalKeyboardKey.intlBackslash,
  0x00000065: PhysicalKeyboardKey.contextMenu,
  0x00000066: PhysicalKeyboardKey.power,
  0x00000067: PhysicalKeyboardKey.numpadEqual,
  0x00000068: PhysicalKeyboardKey.f13,
  0x00000069: PhysicalKeyboardKey.f14,
  0x0000006a: PhysicalKeyboardKey.f15,
  0x0000006b: PhysicalKeyboardKey.f16,
  0x0000006c: PhysicalKeyboardKey.f17,
  0x0000006d: PhysicalKeyboardKey.f18,
  0x0000006e: PhysicalKeyboardKey.f19,
  0x0000006f: PhysicalKeyboardKey.f20,
  0x00000070: PhysicalKeyboardKey.f21,
  0x00000071: PhysicalKeyboardKey.f22,
  0x00000072: PhysicalKeyboardKey.f23,
  0x00000073: PhysicalKeyboardKey.f24,
  0x00000074: PhysicalKeyboardKey.open,
  0x00000075: PhysicalKeyboardKey.help,
  0x00000077: PhysicalKeyboardKey.select,
  0x00000079: PhysicalKeyboardKey.again,
  0x0000007a: PhysicalKeyboardKey.undo,
  0x0000007b: PhysicalKeyboardKey.cut,
  0x0000007c: PhysicalKeyboardKey.copy,
  0x0000007d: PhysicalKeyboardKey.paste,
  0x0000007e: PhysicalKeyboardKey.find,
  0x0000007f: PhysicalKeyboardKey.audioVolumeMute,
  0x00000080: PhysicalKeyboardKey.audioVolumeUp,
  0x00000081: PhysicalKeyboardKey.audioVolumeDown,
  0x00000085: PhysicalKeyboardKey.numpadComma,
  0x00000087: PhysicalKeyboardKey.intlRo,
  0x00000088: PhysicalKeyboardKey.kanaMode,
  0x00000089: PhysicalKeyboardKey.intlYen,
  0x0000008a: PhysicalKeyboardKey.convert,
  0x0000008b: PhysicalKeyboardKey.nonConvert,
  0x00000090: PhysicalKeyboardKey.lang1,
  0x00000091: PhysicalKeyboardKey.lang2,
  0x00000092: PhysicalKeyboardKey.lang3,
  0x00000093: PhysicalKeyboardKey.lang4,
  0x00000094: PhysicalKeyboardKey.lang5,
  0x0000009b: PhysicalKeyboardKey.abort,
  0x000000a3: PhysicalKeyboardKey.props,
  0x000000b6: PhysicalKeyboardKey.numpadParenLeft,
  0x000000b7: PhysicalKeyboardKey.numpadParenRight,
  0x000000bb: PhysicalKeyboardKey.numpadBackspace,
  0x000000d0: PhysicalKeyboardKey.numpadMemoryStore,
  0x000000d1: PhysicalKeyboardKey.numpadMemoryRecall,
  0x000000d2: PhysicalKeyboardKey.numpadMemoryClear,
  0x000000d3: PhysicalKeyboardKey.numpadMemoryAdd,
  0x000000d4: PhysicalKeyboardKey.numpadMemorySubtract,
  0x000000d7: PhysicalKeyboardKey.numpadSignChange,
  0x000000d8: PhysicalKeyboardKey.numpadClear,
  0x000000d9: PhysicalKeyboardKey.numpadClearEntry,
  0x000000e0: PhysicalKeyboardKey.controlLeft,
  0x000000e1: PhysicalKeyboardKey.shiftLeft,
  0x000000e2: PhysicalKeyboardKey.altLeft,
  0x000000e3: PhysicalKeyboardKey.metaLeft,
  0x000000e4: PhysicalKeyboardKey.controlRight,
  0x000000e5: PhysicalKeyboardKey.shiftRight,
  0x000000e6: PhysicalKeyboardKey.altRight,
  0x000000e7: PhysicalKeyboardKey.metaRight,
};

/// Maps iOS specific string values of nonvisible keys to logical keys
///
/// Some unprintable keys on iOS has literal names on their key label, such as
/// "UIKeyInputEscape". See:
/// https://developer.apple.com/documentation/uikit/uikeycommand/input_strings_for_special_keys?language=objc
const Map<String, LogicalKeyboardKey> kIosSpecialLogicalMap = <String, LogicalKeyboardKey>{
  'UIKeyInputEscape': LogicalKeyboardKey.escape,
  'UIKeyInputF1': LogicalKeyboardKey.f1,
  'UIKeyInputF2': LogicalKeyboardKey.f2,
  'UIKeyInputF3': LogicalKeyboardKey.f3,
  'UIKeyInputF4': LogicalKeyboardKey.f4,
  'UIKeyInputF5': LogicalKeyboardKey.f5,
  'UIKeyInputF6': LogicalKeyboardKey.f6,
  'UIKeyInputF7': LogicalKeyboardKey.f7,
  'UIKeyInputF8': LogicalKeyboardKey.f8,
  'UIKeyInputF9': LogicalKeyboardKey.f9,
  'UIKeyInputF10': LogicalKeyboardKey.f10,
  'UIKeyInputF11': LogicalKeyboardKey.f11,
  'UIKeyInputF12': LogicalKeyboardKey.f12,
  'UIKeyInputUpArrow': LogicalKeyboardKey.arrowUp,
  'UIKeyInputDownArrow': LogicalKeyboardKey.arrowDown,
  'UIKeyInputLeftArrow': LogicalKeyboardKey.arrowLeft,
  'UIKeyInputRightArrow': LogicalKeyboardKey.arrowRight,
  'UIKeyInputHome': LogicalKeyboardKey.home,
  'UIKeyInputEnd': LogicalKeyboardKey.enter,
  'UIKeyInputPageUp': LogicalKeyboardKey.pageUp,
  'UIKeyInputPageDown': LogicalKeyboardKey.pageDown,
};

/// A map of iOS key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kIosNumPadMap = <int, LogicalKeyboardKey>{
  0x00000054: LogicalKeyboardKey.numpadDivide,
  0x00000055: LogicalKeyboardKey.numpadMultiply,
  0x00000056: LogicalKeyboardKey.numpadSubtract,
  0x00000057: LogicalKeyboardKey.numpadAdd,
  0x00000059: LogicalKeyboardKey.numpad1,
  0x0000005a: LogicalKeyboardKey.numpad2,
  0x0000005b: LogicalKeyboardKey.numpad3,
  0x0000005c: LogicalKeyboardKey.numpad4,
  0x0000005d: LogicalKeyboardKey.numpad5,
  0x0000005e: LogicalKeyboardKey.numpad6,
  0x0000005f: LogicalKeyboardKey.numpad7,
  0x00000060: LogicalKeyboardKey.numpad8,
  0x00000061: LogicalKeyboardKey.numpad9,
  0x00000062: LogicalKeyboardKey.numpad0,
  0x00000063: LogicalKeyboardKey.numpadDecimal,
  0x00000067: LogicalKeyboardKey.numpadEqual,
  0x00000085: LogicalKeyboardKey.numpadComma,
  0x000000b6: LogicalKeyboardKey.numpadParenLeft,
  0x000000b7: LogicalKeyboardKey.numpadParenRight,
};

/// A map of iOS key codes presenting [LogicalKeyboardKey].
///
/// Logical key codes are not available in iOS key events. Most of the logical keys
/// are derived from its `characterIgnoringModifiers`, but those keys that don't
/// have a character representation will be derived from their key codes using
/// this map.
const Map<int, LogicalKeyboardKey> kIosToLogicalKey = <int, LogicalKeyboardKey>{
  40: LogicalKeyboardKey.enter,
  41: LogicalKeyboardKey.escape,
  42: LogicalKeyboardKey.backspace,
  43: LogicalKeyboardKey.tab,
  57: LogicalKeyboardKey.capsLock,
  58: LogicalKeyboardKey.f1,
  59: LogicalKeyboardKey.f2,
  60: LogicalKeyboardKey.f3,
  61: LogicalKeyboardKey.f4,
  62: LogicalKeyboardKey.f5,
  63: LogicalKeyboardKey.f6,
  64: LogicalKeyboardKey.f7,
  65: LogicalKeyboardKey.f8,
  66: LogicalKeyboardKey.f9,
  67: LogicalKeyboardKey.f10,
  68: LogicalKeyboardKey.f11,
  69: LogicalKeyboardKey.f12,
  73: LogicalKeyboardKey.insert,
  74: LogicalKeyboardKey.home,
  75: LogicalKeyboardKey.pageUp,
  76: LogicalKeyboardKey.delete,
  77: LogicalKeyboardKey.end,
  78: LogicalKeyboardKey.pageDown,
  79: LogicalKeyboardKey.arrowRight,
  80: LogicalKeyboardKey.arrowLeft,
  81: LogicalKeyboardKey.arrowDown,
  82: LogicalKeyboardKey.arrowUp,
  83: LogicalKeyboardKey.numLock,
  84: LogicalKeyboardKey.numpadDivide,
  85: LogicalKeyboardKey.numpadMultiply,
  86: LogicalKeyboardKey.numpadSubtract,
  87: LogicalKeyboardKey.numpadAdd,
  88: LogicalKeyboardKey.numpadEnter,
  89: LogicalKeyboardKey.numpad1,
  90: LogicalKeyboardKey.numpad2,
  91: LogicalKeyboardKey.numpad3,
  92: LogicalKeyboardKey.numpad4,
  93: LogicalKeyboardKey.numpad5,
  94: LogicalKeyboardKey.numpad6,
  95: LogicalKeyboardKey.numpad7,
  96: LogicalKeyboardKey.numpad8,
  97: LogicalKeyboardKey.numpad9,
  98: LogicalKeyboardKey.numpad0,
  99: LogicalKeyboardKey.numpadDecimal,
  101: LogicalKeyboardKey.contextMenu,
  103: LogicalKeyboardKey.numpadEqual,
  104: LogicalKeyboardKey.f13,
  105: LogicalKeyboardKey.f14,
  106: LogicalKeyboardKey.f15,
  107: LogicalKeyboardKey.f16,
  108: LogicalKeyboardKey.f17,
  109: LogicalKeyboardKey.f18,
  110: LogicalKeyboardKey.f19,
  111: LogicalKeyboardKey.f20,
  127: LogicalKeyboardKey.audioVolumeMute,
  128: LogicalKeyboardKey.audioVolumeUp,
  129: LogicalKeyboardKey.audioVolumeDown,
  133: LogicalKeyboardKey.numpadComma,
  135: LogicalKeyboardKey.intlRo,
  137: LogicalKeyboardKey.intlYen,
  144: LogicalKeyboardKey.lang1,
  145: LogicalKeyboardKey.lang2,
  146: LogicalKeyboardKey.lang3,
  147: LogicalKeyboardKey.lang4,
  148: LogicalKeyboardKey.lang5,
  224: LogicalKeyboardKey.controlLeft,
  225: LogicalKeyboardKey.shiftLeft,
  226: LogicalKeyboardKey.altLeft,
  227: LogicalKeyboardKey.metaLeft,
  228: LogicalKeyboardKey.controlRight,
  229: LogicalKeyboardKey.shiftRight,
  230: LogicalKeyboardKey.altRight,
  231: LogicalKeyboardKey.metaRight,
};

/// Maps GLFW-specific key codes to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kGlfwToLogicalKey = <int, LogicalKeyboardKey>{
  32: LogicalKeyboardKey.space,
  39: LogicalKeyboardKey.quote,
  44: LogicalKeyboardKey.comma,
  45: LogicalKeyboardKey.minus,
  46: LogicalKeyboardKey.period,
  47: LogicalKeyboardKey.slash,
  48: LogicalKeyboardKey.digit0,
  49: LogicalKeyboardKey.digit1,
  50: LogicalKeyboardKey.digit2,
  51: LogicalKeyboardKey.digit3,
  52: LogicalKeyboardKey.digit4,
  53: LogicalKeyboardKey.digit5,
  54: LogicalKeyboardKey.digit6,
  55: LogicalKeyboardKey.digit7,
  56: LogicalKeyboardKey.digit8,
  57: LogicalKeyboardKey.digit9,
  59: LogicalKeyboardKey.semicolon,
  61: LogicalKeyboardKey.equal,
  65: LogicalKeyboardKey.keyA,
  66: LogicalKeyboardKey.keyB,
  67: LogicalKeyboardKey.keyC,
  68: LogicalKeyboardKey.keyD,
  69: LogicalKeyboardKey.keyE,
  70: LogicalKeyboardKey.keyF,
  71: LogicalKeyboardKey.keyG,
  72: LogicalKeyboardKey.keyH,
  73: LogicalKeyboardKey.keyI,
  74: LogicalKeyboardKey.keyJ,
  75: LogicalKeyboardKey.keyK,
  76: LogicalKeyboardKey.keyL,
  77: LogicalKeyboardKey.keyM,
  78: LogicalKeyboardKey.keyN,
  79: LogicalKeyboardKey.keyO,
  80: LogicalKeyboardKey.keyP,
  81: LogicalKeyboardKey.keyQ,
  82: LogicalKeyboardKey.keyR,
  83: LogicalKeyboardKey.keyS,
  84: LogicalKeyboardKey.keyT,
  85: LogicalKeyboardKey.keyU,
  86: LogicalKeyboardKey.keyV,
  87: LogicalKeyboardKey.keyW,
  88: LogicalKeyboardKey.keyX,
  89: LogicalKeyboardKey.keyY,
  90: LogicalKeyboardKey.keyZ,
  91: LogicalKeyboardKey.bracketLeft,
  92: LogicalKeyboardKey.backslash,
  93: LogicalKeyboardKey.bracketRight,
  96: LogicalKeyboardKey.backquote,
  256: LogicalKeyboardKey.escape,
  257: LogicalKeyboardKey.enter,
  258: LogicalKeyboardKey.tab,
  259: LogicalKeyboardKey.backspace,
  260: LogicalKeyboardKey.insert,
  261: LogicalKeyboardKey.delete,
  262: LogicalKeyboardKey.arrowRight,
  263: LogicalKeyboardKey.arrowLeft,
  264: LogicalKeyboardKey.arrowDown,
  265: LogicalKeyboardKey.arrowUp,
  266: LogicalKeyboardKey.pageUp,
  267: LogicalKeyboardKey.pageDown,
  268: LogicalKeyboardKey.home,
  269: LogicalKeyboardKey.end,
  280: LogicalKeyboardKey.capsLock,
  282: LogicalKeyboardKey.numLock,
  283: LogicalKeyboardKey.printScreen,
  284: LogicalKeyboardKey.pause,
  290: LogicalKeyboardKey.f1,
  291: LogicalKeyboardKey.f2,
  292: LogicalKeyboardKey.f3,
  293: LogicalKeyboardKey.f4,
  294: LogicalKeyboardKey.f5,
  295: LogicalKeyboardKey.f6,
  296: LogicalKeyboardKey.f7,
  297: LogicalKeyboardKey.f8,
  298: LogicalKeyboardKey.f9,
  299: LogicalKeyboardKey.f10,
  300: LogicalKeyboardKey.f11,
  301: LogicalKeyboardKey.f12,
  302: LogicalKeyboardKey.f13,
  303: LogicalKeyboardKey.f14,
  304: LogicalKeyboardKey.f15,
  305: LogicalKeyboardKey.f16,
  306: LogicalKeyboardKey.f17,
  307: LogicalKeyboardKey.f18,
  308: LogicalKeyboardKey.f19,
  309: LogicalKeyboardKey.f20,
  310: LogicalKeyboardKey.f21,
  311: LogicalKeyboardKey.f22,
  312: LogicalKeyboardKey.f23,
  320: LogicalKeyboardKey.numpad0,
  321: LogicalKeyboardKey.numpad1,
  322: LogicalKeyboardKey.numpad2,
  323: LogicalKeyboardKey.numpad3,
  324: LogicalKeyboardKey.numpad4,
  325: LogicalKeyboardKey.numpad5,
  326: LogicalKeyboardKey.numpad6,
  327: LogicalKeyboardKey.numpad7,
  328: LogicalKeyboardKey.numpad8,
  329: LogicalKeyboardKey.numpad9,
  330: LogicalKeyboardKey.numpadDecimal,
  331: LogicalKeyboardKey.numpadDivide,
  332: LogicalKeyboardKey.numpadMultiply,
  334: LogicalKeyboardKey.numpadAdd,
  335: LogicalKeyboardKey.numpadEnter,
  336: LogicalKeyboardKey.numpadEqual,
  340: LogicalKeyboardKey.shiftLeft,
  341: LogicalKeyboardKey.controlLeft,
  342: LogicalKeyboardKey.altLeft,
  343: LogicalKeyboardKey.metaLeft,
  344: LogicalKeyboardKey.shiftRight,
  345: LogicalKeyboardKey.controlRight,
  346: LogicalKeyboardKey.altRight,
  347: LogicalKeyboardKey.metaRight,
  348: LogicalKeyboardKey.contextMenu,
};

/// A map of GLFW key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kGlfwNumpadMap = <int, LogicalKeyboardKey>{
  320: LogicalKeyboardKey.numpad0,
  321: LogicalKeyboardKey.numpad1,
  322: LogicalKeyboardKey.numpad2,
  323: LogicalKeyboardKey.numpad3,
  324: LogicalKeyboardKey.numpad4,
  325: LogicalKeyboardKey.numpad5,
  326: LogicalKeyboardKey.numpad6,
  327: LogicalKeyboardKey.numpad7,
  328: LogicalKeyboardKey.numpad8,
  329: LogicalKeyboardKey.numpad9,
  330: LogicalKeyboardKey.numpadDecimal,
  331: LogicalKeyboardKey.numpadDivide,
  332: LogicalKeyboardKey.numpadMultiply,
  334: LogicalKeyboardKey.numpadAdd,
  336: LogicalKeyboardKey.numpadEqual,
};

/// Maps GTK-specific key codes to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kGtkToLogicalKey = <int, LogicalKeyboardKey>{
  165: LogicalKeyboardKey.intlYen,
  64774: LogicalKeyboardKey.eraseEof,
  64782: LogicalKeyboardKey.attn,
  64789: LogicalKeyboardKey.copy,
  64790: LogicalKeyboardKey.mediaPlay,
  64795: LogicalKeyboardKey.exSel,
  64797: LogicalKeyboardKey.printScreen,
  64798: LogicalKeyboardKey.enter,
  65027: LogicalKeyboardKey.altGraph,
  65032: LogicalKeyboardKey.groupNext,
  65034: LogicalKeyboardKey.groupPrevious,
  65036: LogicalKeyboardKey.groupFirst,
  65038: LogicalKeyboardKey.groupLast,
  65056: LogicalKeyboardKey.tab,
  65076: LogicalKeyboardKey.enter,
  65288: LogicalKeyboardKey.backspace,
  65289: LogicalKeyboardKey.tab,
  65291: LogicalKeyboardKey.clear,
  65293: LogicalKeyboardKey.enter,
  65299: LogicalKeyboardKey.pause,
  65300: LogicalKeyboardKey.scrollLock,
  65307: LogicalKeyboardKey.escape,
  65313: LogicalKeyboardKey.kanjiMode,
  65316: LogicalKeyboardKey.romaji,
  65317: LogicalKeyboardKey.hiragana,
  65318: LogicalKeyboardKey.katakana,
  65319: LogicalKeyboardKey.hiraganaKatakana,
  65320: LogicalKeyboardKey.zenkaku,
  65321: LogicalKeyboardKey.hankaku,
  65322: LogicalKeyboardKey.zenkakuHankaku,
  65327: LogicalKeyboardKey.eisu,
  65329: LogicalKeyboardKey.hangulMode,
  65332: LogicalKeyboardKey.hanjaMode,
  65335: LogicalKeyboardKey.codeInput,
  65340: LogicalKeyboardKey.singleCandidate,
  65342: LogicalKeyboardKey.previousCandidate,
  65360: LogicalKeyboardKey.home,
  65361: LogicalKeyboardKey.arrowLeft,
  65362: LogicalKeyboardKey.arrowUp,
  65363: LogicalKeyboardKey.arrowRight,
  65364: LogicalKeyboardKey.arrowDown,
  65365: LogicalKeyboardKey.pageUp,
  65366: LogicalKeyboardKey.pageDown,
  65367: LogicalKeyboardKey.end,
  65376: LogicalKeyboardKey.select,
  65377: LogicalKeyboardKey.print,
  65378: LogicalKeyboardKey.execute,
  65379: LogicalKeyboardKey.insert,
  65381: LogicalKeyboardKey.undo,
  65382: LogicalKeyboardKey.redo,
  65383: LogicalKeyboardKey.contextMenu,
  65384: LogicalKeyboardKey.find,
  65385: LogicalKeyboardKey.cancel,
  65386: LogicalKeyboardKey.help,
  65406: LogicalKeyboardKey.modeChange,
  65407: LogicalKeyboardKey.numLock,
  65408: LogicalKeyboardKey.space,
  65417: LogicalKeyboardKey.tab,
  65421: LogicalKeyboardKey.numpadEnter,
  65425: LogicalKeyboardKey.f1,
  65426: LogicalKeyboardKey.f2,
  65427: LogicalKeyboardKey.f3,
  65428: LogicalKeyboardKey.f4,
  65429: LogicalKeyboardKey.numpad7,
  65430: LogicalKeyboardKey.numpad4,
  65431: LogicalKeyboardKey.numpad8,
  65432: LogicalKeyboardKey.numpad6,
  65433: LogicalKeyboardKey.numpad2,
  65434: LogicalKeyboardKey.numpad9,
  65435: LogicalKeyboardKey.numpad3,
  65436: LogicalKeyboardKey.numpad1,
  65438: LogicalKeyboardKey.numpad0,
  65439: LogicalKeyboardKey.numpadDecimal,
  65450: LogicalKeyboardKey.numpadMultiply,
  65451: LogicalKeyboardKey.numpadAdd,
  65453: LogicalKeyboardKey.numpadSubtract,
  65454: LogicalKeyboardKey.period,
  65455: LogicalKeyboardKey.numpadDivide,
  65456: LogicalKeyboardKey.numpad0,
  65457: LogicalKeyboardKey.numpad1,
  65458: LogicalKeyboardKey.numpad2,
  65459: LogicalKeyboardKey.numpad3,
  65460: LogicalKeyboardKey.numpad4,
  65461: LogicalKeyboardKey.numpad5,
  65462: LogicalKeyboardKey.numpad6,
  65463: LogicalKeyboardKey.numpad7,
  65464: LogicalKeyboardKey.numpad8,
  65465: LogicalKeyboardKey.numpad9,
  65469: LogicalKeyboardKey.numpadEqual,
  65470: LogicalKeyboardKey.f1,
  65471: LogicalKeyboardKey.f2,
  65472: LogicalKeyboardKey.f3,
  65473: LogicalKeyboardKey.f4,
  65474: LogicalKeyboardKey.f5,
  65475: LogicalKeyboardKey.f6,
  65476: LogicalKeyboardKey.f7,
  65477: LogicalKeyboardKey.f8,
  65478: LogicalKeyboardKey.f9,
  65479: LogicalKeyboardKey.f10,
  65480: LogicalKeyboardKey.f11,
  65481: LogicalKeyboardKey.f12,
  65482: LogicalKeyboardKey.f13,
  65483: LogicalKeyboardKey.f14,
  65484: LogicalKeyboardKey.f15,
  65485: LogicalKeyboardKey.f16,
  65486: LogicalKeyboardKey.f17,
  65487: LogicalKeyboardKey.f18,
  65488: LogicalKeyboardKey.f19,
  65489: LogicalKeyboardKey.f20,
  65490: LogicalKeyboardKey.f21,
  65491: LogicalKeyboardKey.f22,
  65492: LogicalKeyboardKey.f23,
  65493: LogicalKeyboardKey.f24,
  65505: LogicalKeyboardKey.shiftLeft,
  65506: LogicalKeyboardKey.shiftRight,
  65507: LogicalKeyboardKey.controlLeft,
  65508: LogicalKeyboardKey.controlRight,
  65509: LogicalKeyboardKey.capsLock,
  65511: LogicalKeyboardKey.metaLeft,
  65512: LogicalKeyboardKey.metaRight,
  65513: LogicalKeyboardKey.altLeft,
  65514: LogicalKeyboardKey.altRight,
  65515: LogicalKeyboardKey.superKey,
  65516: LogicalKeyboardKey.superKey,
  65517: LogicalKeyboardKey.hyper,
  65518: LogicalKeyboardKey.hyper,
  65535: LogicalKeyboardKey.delete,
  269025026: LogicalKeyboardKey.brightnessUp,
  269025027: LogicalKeyboardKey.brightnessDown,
  269025040: LogicalKeyboardKey.standby,
  269025041: LogicalKeyboardKey.audioVolumeDown,
  269025042: LogicalKeyboardKey.audioVolumeMute,
  269025043: LogicalKeyboardKey.audioVolumeUp,
  269025044: LogicalKeyboardKey.mediaPlay,
  269025045: LogicalKeyboardKey.mediaStop,
  269025046: LogicalKeyboardKey.mediaTrackPrevious,
  269025047: LogicalKeyboardKey.mediaTrackNext,
  269025048: LogicalKeyboardKey.browserHome,
  269025049: LogicalKeyboardKey.launchMail,
  269025051: LogicalKeyboardKey.browserSearch,
  269025052: LogicalKeyboardKey.mediaRecord,
  269025056: LogicalKeyboardKey.launchCalendar,
  269025062: LogicalKeyboardKey.browserBack,
  269025063: LogicalKeyboardKey.browserForward,
  269025064: LogicalKeyboardKey.browserStop,
  269025065: LogicalKeyboardKey.browserRefresh,
  269025066: LogicalKeyboardKey.powerOff,
  269025067: LogicalKeyboardKey.wakeUp,
  269025068: LogicalKeyboardKey.eject,
  269025069: LogicalKeyboardKey.launchScreenSaver,
  269025071: LogicalKeyboardKey.sleep,
  269025072: LogicalKeyboardKey.browserFavorites,
  269025073: LogicalKeyboardKey.mediaPause,
  269025086: LogicalKeyboardKey.mediaRewind,
  269025110: LogicalKeyboardKey.close,
  269025111: LogicalKeyboardKey.copy,
  269025112: LogicalKeyboardKey.cut,
  269025121: LogicalKeyboardKey.logOff,
  269025128: LogicalKeyboardKey.newKey,
  269025131: LogicalKeyboardKey.open,
  269025133: LogicalKeyboardKey.paste,
  269025134: LogicalKeyboardKey.launchPhone,
  269025138: LogicalKeyboardKey.mailReply,
  269025143: LogicalKeyboardKey.save,
  269025147: LogicalKeyboardKey.mailSend,
  269025148: LogicalKeyboardKey.spellCheck,
  269025163: LogicalKeyboardKey.zoomIn,
  269025164: LogicalKeyboardKey.zoomOut,
  269025168: LogicalKeyboardKey.mailForward,
  269025175: LogicalKeyboardKey.mediaFastForward,
  269025191: LogicalKeyboardKey.suspend,
};

/// A map of GTK key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kGtkNumpadMap = <int, LogicalKeyboardKey>{
  65429: LogicalKeyboardKey.numpad7,
  65430: LogicalKeyboardKey.numpad4,
  65431: LogicalKeyboardKey.numpad8,
  65432: LogicalKeyboardKey.numpad6,
  65433: LogicalKeyboardKey.numpad2,
  65434: LogicalKeyboardKey.numpad9,
  65435: LogicalKeyboardKey.numpad3,
  65436: LogicalKeyboardKey.numpad1,
  65438: LogicalKeyboardKey.numpad0,
  65439: LogicalKeyboardKey.numpadDecimal,
  65450: LogicalKeyboardKey.numpadMultiply,
  65451: LogicalKeyboardKey.numpadAdd,
  65453: LogicalKeyboardKey.numpadSubtract,
  65455: LogicalKeyboardKey.numpadDivide,
  65456: LogicalKeyboardKey.numpad0,
  65457: LogicalKeyboardKey.numpad1,
  65458: LogicalKeyboardKey.numpad2,
  65459: LogicalKeyboardKey.numpad3,
  65460: LogicalKeyboardKey.numpad4,
  65461: LogicalKeyboardKey.numpad5,
  65462: LogicalKeyboardKey.numpad6,
  65463: LogicalKeyboardKey.numpad7,
  65464: LogicalKeyboardKey.numpad8,
  65465: LogicalKeyboardKey.numpad9,
  65469: LogicalKeyboardKey.numpadEqual,
};

/// Maps XKB specific key code values representing [PhysicalKeyboardKey].
const Map<int, PhysicalKeyboardKey> kLinuxToPhysicalKey = <int, PhysicalKeyboardKey>{
  0x00000009: PhysicalKeyboardKey.escape,
  0x0000000a: PhysicalKeyboardKey.digit1,
  0x0000000b: PhysicalKeyboardKey.digit2,
  0x0000000c: PhysicalKeyboardKey.digit3,
  0x0000000d: PhysicalKeyboardKey.digit4,
  0x0000000e: PhysicalKeyboardKey.digit5,
  0x0000000f: PhysicalKeyboardKey.digit6,
  0x00000010: PhysicalKeyboardKey.digit7,
  0x00000011: PhysicalKeyboardKey.digit8,
  0x00000012: PhysicalKeyboardKey.digit9,
  0x00000013: PhysicalKeyboardKey.digit0,
  0x00000014: PhysicalKeyboardKey.minus,
  0x00000015: PhysicalKeyboardKey.equal,
  0x00000016: PhysicalKeyboardKey.backspace,
  0x00000017: PhysicalKeyboardKey.tab,
  0x00000018: PhysicalKeyboardKey.keyQ,
  0x00000019: PhysicalKeyboardKey.keyW,
  0x0000001a: PhysicalKeyboardKey.keyE,
  0x0000001b: PhysicalKeyboardKey.keyR,
  0x0000001c: PhysicalKeyboardKey.keyT,
  0x0000001d: PhysicalKeyboardKey.keyY,
  0x0000001e: PhysicalKeyboardKey.keyU,
  0x0000001f: PhysicalKeyboardKey.keyI,
  0x00000020: PhysicalKeyboardKey.keyO,
  0x00000021: PhysicalKeyboardKey.keyP,
  0x00000022: PhysicalKeyboardKey.bracketLeft,
  0x00000023: PhysicalKeyboardKey.bracketRight,
  0x00000024: PhysicalKeyboardKey.enter,
  0x00000025: PhysicalKeyboardKey.controlLeft,
  0x00000026: PhysicalKeyboardKey.keyA,
  0x00000027: PhysicalKeyboardKey.keyS,
  0x00000028: PhysicalKeyboardKey.keyD,
  0x00000029: PhysicalKeyboardKey.keyF,
  0x0000002a: PhysicalKeyboardKey.keyG,
  0x0000002b: PhysicalKeyboardKey.keyH,
  0x0000002c: PhysicalKeyboardKey.keyJ,
  0x0000002d: PhysicalKeyboardKey.keyK,
  0x0000002e: PhysicalKeyboardKey.keyL,
  0x0000002f: PhysicalKeyboardKey.semicolon,
  0x00000030: PhysicalKeyboardKey.quote,
  0x00000031: PhysicalKeyboardKey.backquote,
  0x00000032: PhysicalKeyboardKey.shiftLeft,
  0x00000033: PhysicalKeyboardKey.backslash,
  0x00000034: PhysicalKeyboardKey.keyZ,
  0x00000035: PhysicalKeyboardKey.keyX,
  0x00000036: PhysicalKeyboardKey.keyC,
  0x00000037: PhysicalKeyboardKey.keyV,
  0x00000038: PhysicalKeyboardKey.keyB,
  0x00000039: PhysicalKeyboardKey.keyN,
  0x0000003a: PhysicalKeyboardKey.keyM,
  0x0000003b: PhysicalKeyboardKey.comma,
  0x0000003c: PhysicalKeyboardKey.period,
  0x0000003d: PhysicalKeyboardKey.slash,
  0x0000003e: PhysicalKeyboardKey.shiftRight,
  0x0000003f: PhysicalKeyboardKey.numpadMultiply,
  0x00000040: PhysicalKeyboardKey.altLeft,
  0x00000041: PhysicalKeyboardKey.space,
  0x00000042: PhysicalKeyboardKey.capsLock,
  0x00000043: PhysicalKeyboardKey.f1,
  0x00000044: PhysicalKeyboardKey.f2,
  0x00000045: PhysicalKeyboardKey.f3,
  0x00000046: PhysicalKeyboardKey.f4,
  0x00000047: PhysicalKeyboardKey.f5,
  0x00000048: PhysicalKeyboardKey.f6,
  0x00000049: PhysicalKeyboardKey.f7,
  0x0000004a: PhysicalKeyboardKey.f8,
  0x0000004b: PhysicalKeyboardKey.f9,
  0x0000004c: PhysicalKeyboardKey.f10,
  0x0000004d: PhysicalKeyboardKey.numLock,
  0x0000004e: PhysicalKeyboardKey.scrollLock,
  0x0000004f: PhysicalKeyboardKey.numpad7,
  0x00000050: PhysicalKeyboardKey.numpad8,
  0x00000051: PhysicalKeyboardKey.numpad9,
  0x00000052: PhysicalKeyboardKey.numpadSubtract,
  0x00000053: PhysicalKeyboardKey.numpad4,
  0x00000054: PhysicalKeyboardKey.numpad5,
  0x00000055: PhysicalKeyboardKey.numpad6,
  0x00000056: PhysicalKeyboardKey.numpadAdd,
  0x00000057: PhysicalKeyboardKey.numpad1,
  0x00000058: PhysicalKeyboardKey.numpad2,
  0x00000059: PhysicalKeyboardKey.numpad3,
  0x0000005a: PhysicalKeyboardKey.numpad0,
  0x0000005b: PhysicalKeyboardKey.numpadDecimal,
  0x0000005d: PhysicalKeyboardKey.lang5,
  0x0000005e: PhysicalKeyboardKey.intlBackslash,
  0x0000005f: PhysicalKeyboardKey.f11,
  0x00000060: PhysicalKeyboardKey.f12,
  0x00000061: PhysicalKeyboardKey.intlRo,
  0x00000062: PhysicalKeyboardKey.lang3,
  0x00000063: PhysicalKeyboardKey.lang4,
  0x00000064: PhysicalKeyboardKey.convert,
  0x00000065: PhysicalKeyboardKey.kanaMode,
  0x00000066: PhysicalKeyboardKey.nonConvert,
  0x00000068: PhysicalKeyboardKey.numpadEnter,
  0x00000069: PhysicalKeyboardKey.controlRight,
  0x0000006a: PhysicalKeyboardKey.numpadDivide,
  0x0000006b: PhysicalKeyboardKey.printScreen,
  0x0000006c: PhysicalKeyboardKey.altRight,
  0x0000006e: PhysicalKeyboardKey.home,
  0x0000006f: PhysicalKeyboardKey.arrowUp,
  0x00000070: PhysicalKeyboardKey.pageUp,
  0x00000071: PhysicalKeyboardKey.arrowLeft,
  0x00000072: PhysicalKeyboardKey.arrowRight,
  0x00000073: PhysicalKeyboardKey.end,
  0x00000074: PhysicalKeyboardKey.arrowDown,
  0x00000075: PhysicalKeyboardKey.pageDown,
  0x00000076: PhysicalKeyboardKey.insert,
  0x00000077: PhysicalKeyboardKey.delete,
  0x00000079: PhysicalKeyboardKey.audioVolumeMute,
  0x0000007a: PhysicalKeyboardKey.audioVolumeDown,
  0x0000007b: PhysicalKeyboardKey.audioVolumeUp,
  0x0000007c: PhysicalKeyboardKey.power,
  0x0000007d: PhysicalKeyboardKey.numpadEqual,
  0x0000007e: PhysicalKeyboardKey.numpadSignChange,
  0x0000007f: PhysicalKeyboardKey.pause,
  0x00000080: PhysicalKeyboardKey.showAllWindows,
  0x00000081: PhysicalKeyboardKey.numpadComma,
  0x00000082: PhysicalKeyboardKey.lang1,
  0x00000083: PhysicalKeyboardKey.lang2,
  0x00000084: PhysicalKeyboardKey.intlYen,
  0x00000085: PhysicalKeyboardKey.metaLeft,
  0x00000086: PhysicalKeyboardKey.metaRight,
  0x00000087: PhysicalKeyboardKey.contextMenu,
  0x00000088: PhysicalKeyboardKey.browserStop,
  0x00000089: PhysicalKeyboardKey.again,
  0x0000008b: PhysicalKeyboardKey.undo,
  0x0000008c: PhysicalKeyboardKey.select,
  0x0000008d: PhysicalKeyboardKey.copy,
  0x0000008e: PhysicalKeyboardKey.open,
  0x0000008f: PhysicalKeyboardKey.paste,
  0x00000090: PhysicalKeyboardKey.find,
  0x00000091: PhysicalKeyboardKey.cut,
  0x00000092: PhysicalKeyboardKey.help,
  0x00000094: PhysicalKeyboardKey.launchApp2,
  0x00000096: PhysicalKeyboardKey.sleep,
  0x00000097: PhysicalKeyboardKey.wakeUp,
  0x00000098: PhysicalKeyboardKey.launchApp1,
  0x0000009e: PhysicalKeyboardKey.launchInternetBrowser,
  0x000000a0: PhysicalKeyboardKey.lockScreen,
  0x000000a3: PhysicalKeyboardKey.launchMail,
  0x000000a4: PhysicalKeyboardKey.browserFavorites,
  0x000000a6: PhysicalKeyboardKey.browserBack,
  0x000000a7: PhysicalKeyboardKey.browserForward,
  0x000000a9: PhysicalKeyboardKey.eject,
  0x000000ab: PhysicalKeyboardKey.mediaTrackNext,
  0x000000ac: PhysicalKeyboardKey.mediaPlayPause,
  0x000000ad: PhysicalKeyboardKey.mediaTrackPrevious,
  0x000000ae: PhysicalKeyboardKey.mediaStop,
  0x000000af: PhysicalKeyboardKey.mediaRecord,
  0x000000b0: PhysicalKeyboardKey.mediaRewind,
  0x000000b1: PhysicalKeyboardKey.launchPhone,
  0x000000b3: PhysicalKeyboardKey.mediaSelect,
  0x000000b4: PhysicalKeyboardKey.browserHome,
  0x000000b5: PhysicalKeyboardKey.browserRefresh,
  0x000000b6: PhysicalKeyboardKey.exit,
  0x000000bb: PhysicalKeyboardKey.numpadParenLeft,
  0x000000bc: PhysicalKeyboardKey.numpadParenRight,
  0x000000bd: PhysicalKeyboardKey.newKey,
  0x000000be: PhysicalKeyboardKey.redo,
  0x000000bf: PhysicalKeyboardKey.f13,
  0x000000c0: PhysicalKeyboardKey.f14,
  0x000000c1: PhysicalKeyboardKey.f15,
  0x000000c2: PhysicalKeyboardKey.f16,
  0x000000c3: PhysicalKeyboardKey.f17,
  0x000000c4: PhysicalKeyboardKey.f18,
  0x000000c5: PhysicalKeyboardKey.f19,
  0x000000c6: PhysicalKeyboardKey.f20,
  0x000000c7: PhysicalKeyboardKey.f21,
  0x000000c8: PhysicalKeyboardKey.f22,
  0x000000c9: PhysicalKeyboardKey.f23,
  0x000000ca: PhysicalKeyboardKey.f24,
  0x000000d1: PhysicalKeyboardKey.mediaPause,
  0x000000d6: PhysicalKeyboardKey.close,
  0x000000d7: PhysicalKeyboardKey.mediaPlay,
  0x000000d8: PhysicalKeyboardKey.mediaFastForward,
  0x000000d9: PhysicalKeyboardKey.bassBoost,
  0x000000da: PhysicalKeyboardKey.print,
  0x000000e1: PhysicalKeyboardKey.browserSearch,
  0x000000e8: PhysicalKeyboardKey.brightnessDown,
  0x000000e9: PhysicalKeyboardKey.brightnessUp,
  0x000000eb: PhysicalKeyboardKey.displayToggleIntExt,
  0x000000ed: PhysicalKeyboardKey.kbdIllumDown,
  0x000000ee: PhysicalKeyboardKey.kbdIllumUp,
  0x000000ef: PhysicalKeyboardKey.mailSend,
  0x000000f0: PhysicalKeyboardKey.mailReply,
  0x000000f1: PhysicalKeyboardKey.mailForward,
  0x000000f2: PhysicalKeyboardKey.save,
  0x000000f3: PhysicalKeyboardKey.launchDocuments,
  0x000000fc: PhysicalKeyboardKey.brightnessAuto,
  0x00000100: PhysicalKeyboardKey.microphoneMuteToggle,
  0x0000016e: PhysicalKeyboardKey.info,
  0x00000172: PhysicalKeyboardKey.programGuide,
  0x0000017a: PhysicalKeyboardKey.closedCaptionToggle,
  0x0000017c: PhysicalKeyboardKey.zoomToggle,
  0x0000017e: PhysicalKeyboardKey.launchKeyboardLayout,
  0x00000190: PhysicalKeyboardKey.launchAudioBrowser,
  0x00000195: PhysicalKeyboardKey.launchCalendar,
  0x0000019d: PhysicalKeyboardKey.mediaLast,
  0x000001a2: PhysicalKeyboardKey.channelUp,
  0x000001a3: PhysicalKeyboardKey.channelDown,
  0x000001aa: PhysicalKeyboardKey.zoomIn,
  0x000001ab: PhysicalKeyboardKey.zoomOut,
  0x000001ad: PhysicalKeyboardKey.launchWordProcessor,
  0x000001af: PhysicalKeyboardKey.launchSpreadsheet,
  0x000001b5: PhysicalKeyboardKey.launchContacts,
  0x000001b7: PhysicalKeyboardKey.brightnessToggle,
  0x000001b8: PhysicalKeyboardKey.spellCheck,
  0x000001b9: PhysicalKeyboardKey.logOff,
  0x0000024b: PhysicalKeyboardKey.launchControlPanel,
  0x0000024c: PhysicalKeyboardKey.selectTask,
  0x0000024d: PhysicalKeyboardKey.launchScreenSaver,
  0x0000024e: PhysicalKeyboardKey.speechInputToggle,
  0x0000024f: PhysicalKeyboardKey.launchAssistant,
  0x00000250: PhysicalKeyboardKey.keyboardLayoutSelect,
  0x00000258: PhysicalKeyboardKey.brightnessMinimum,
  0x00000259: PhysicalKeyboardKey.brightnessMaximum,
  0x00000281: PhysicalKeyboardKey.privacyScreenToggle,
};

/// Maps Web KeyboardEvent codes to the matching [LogicalKeyboardKey].
const Map<String, LogicalKeyboardKey> kWebToLogicalKey = <String, LogicalKeyboardKey>{
  'AVRInput': LogicalKeyboardKey.avrInput,
  'AVRPower': LogicalKeyboardKey.avrPower,
  'Accel': LogicalKeyboardKey.accel,
  'Accept': LogicalKeyboardKey.accept,
  'Again': LogicalKeyboardKey.again,
  'AllCandidates': LogicalKeyboardKey.allCandidates,
  'Alphanumeric': LogicalKeyboardKey.alphanumeric,
  'AltGraph': LogicalKeyboardKey.altGraph,
  'AppSwitch': LogicalKeyboardKey.appSwitch,
  'ArrowDown': LogicalKeyboardKey.arrowDown,
  'ArrowLeft': LogicalKeyboardKey.arrowLeft,
  'ArrowRight': LogicalKeyboardKey.arrowRight,
  'ArrowUp': LogicalKeyboardKey.arrowUp,
  'Attn': LogicalKeyboardKey.attn,
  'AudioBalanceLeft': LogicalKeyboardKey.audioBalanceLeft,
  'AudioBalanceRight': LogicalKeyboardKey.audioBalanceRight,
  'AudioBassBoostDown': LogicalKeyboardKey.audioBassBoostDown,
  'AudioBassBoostToggle': LogicalKeyboardKey.audioBassBoostToggle,
  'AudioBassBoostUp': LogicalKeyboardKey.audioBassBoostUp,
  'AudioFaderFront': LogicalKeyboardKey.audioFaderFront,
  'AudioFaderRear': LogicalKeyboardKey.audioFaderRear,
  'AudioSurroundModeNext': LogicalKeyboardKey.audioSurroundModeNext,
  'AudioTrebleDown': LogicalKeyboardKey.audioTrebleDown,
  'AudioTrebleUp': LogicalKeyboardKey.audioTrebleUp,
  'AudioVolumeDown': LogicalKeyboardKey.audioVolumeDown,
  'AudioVolumeMute': LogicalKeyboardKey.audioVolumeMute,
  'AudioVolumeUp': LogicalKeyboardKey.audioVolumeUp,
  'Backspace': LogicalKeyboardKey.backspace,
  'BrightnessDown': LogicalKeyboardKey.brightnessDown,
  'BrightnessUp': LogicalKeyboardKey.brightnessUp,
  'BrowserBack': LogicalKeyboardKey.browserBack,
  'BrowserFavorites': LogicalKeyboardKey.browserFavorites,
  'BrowserForward': LogicalKeyboardKey.browserForward,
  'BrowserHome': LogicalKeyboardKey.browserHome,
  'BrowserRefresh': LogicalKeyboardKey.browserRefresh,
  'BrowserSearch': LogicalKeyboardKey.browserSearch,
  'BrowserStop': LogicalKeyboardKey.browserStop,
  'Call': LogicalKeyboardKey.call,
  'Camera': LogicalKeyboardKey.camera,
  'CameraFocus': LogicalKeyboardKey.cameraFocus,
  'Cancel': LogicalKeyboardKey.cancel,
  'CapsLock': LogicalKeyboardKey.capsLock,
  'ChannelDown': LogicalKeyboardKey.channelDown,
  'ChannelUp': LogicalKeyboardKey.channelUp,
  'Clear': LogicalKeyboardKey.clear,
  'Close': LogicalKeyboardKey.close,
  'ClosedCaptionToggle': LogicalKeyboardKey.closedCaptionToggle,
  'CodeInput': LogicalKeyboardKey.codeInput,
  'ColorF0Red': LogicalKeyboardKey.colorF0Red,
  'ColorF1Green': LogicalKeyboardKey.colorF1Green,
  'ColorF2Yellow': LogicalKeyboardKey.colorF2Yellow,
  'ColorF3Blue': LogicalKeyboardKey.colorF3Blue,
  'ColorF4Grey': LogicalKeyboardKey.colorF4Grey,
  'ColorF5Brown': LogicalKeyboardKey.colorF5Brown,
  'Compose': LogicalKeyboardKey.compose,
  'ContextMenu': LogicalKeyboardKey.contextMenu,
  'Convert': LogicalKeyboardKey.convert,
  'Copy': LogicalKeyboardKey.copy,
  'CrSel': LogicalKeyboardKey.crSel,
  'Cut': LogicalKeyboardKey.cut,
  'DVR': LogicalKeyboardKey.dvr,
  'Delete': LogicalKeyboardKey.delete,
  'Dimmer': LogicalKeyboardKey.dimmer,
  'DisplaySwap': LogicalKeyboardKey.displaySwap,
  'Eisu': LogicalKeyboardKey.eisu,
  'Eject': LogicalKeyboardKey.eject,
  'End': LogicalKeyboardKey.end,
  'EndCall': LogicalKeyboardKey.endCall,
  'Enter': LogicalKeyboardKey.enter,
  'EraseEof': LogicalKeyboardKey.eraseEof,
  'Esc': LogicalKeyboardKey.escape,
  'Escape': LogicalKeyboardKey.escape,
  'ExSel': LogicalKeyboardKey.exSel,
  'Execute': LogicalKeyboardKey.execute,
  'Exit': LogicalKeyboardKey.exit,
  'F1': LogicalKeyboardKey.f1,
  'F10': LogicalKeyboardKey.f10,
  'F11': LogicalKeyboardKey.f11,
  'F12': LogicalKeyboardKey.f12,
  'F13': LogicalKeyboardKey.f13,
  'F14': LogicalKeyboardKey.f14,
  'F15': LogicalKeyboardKey.f15,
  'F16': LogicalKeyboardKey.f16,
  'F17': LogicalKeyboardKey.f17,
  'F18': LogicalKeyboardKey.f18,
  'F19': LogicalKeyboardKey.f19,
  'F2': LogicalKeyboardKey.f2,
  'F20': LogicalKeyboardKey.f20,
  'F21': LogicalKeyboardKey.f21,
  'F22': LogicalKeyboardKey.f22,
  'F23': LogicalKeyboardKey.f23,
  'F24': LogicalKeyboardKey.f24,
  'F3': LogicalKeyboardKey.f3,
  'F4': LogicalKeyboardKey.f4,
  'F5': LogicalKeyboardKey.f5,
  'F6': LogicalKeyboardKey.f6,
  'F7': LogicalKeyboardKey.f7,
  'F8': LogicalKeyboardKey.f8,
  'F9': LogicalKeyboardKey.f9,
  'FavoriteClear0': LogicalKeyboardKey.favoriteClear0,
  'FavoriteClear1': LogicalKeyboardKey.favoriteClear1,
  'FavoriteClear2': LogicalKeyboardKey.favoriteClear2,
  'FavoriteClear3': LogicalKeyboardKey.favoriteClear3,
  'FavoriteRecall0': LogicalKeyboardKey.favoriteRecall0,
  'FavoriteRecall1': LogicalKeyboardKey.favoriteRecall1,
  'FavoriteRecall2': LogicalKeyboardKey.favoriteRecall2,
  'FavoriteRecall3': LogicalKeyboardKey.favoriteRecall3,
  'FavoriteStore0': LogicalKeyboardKey.favoriteStore0,
  'FavoriteStore1': LogicalKeyboardKey.favoriteStore1,
  'FavoriteStore2': LogicalKeyboardKey.favoriteStore2,
  'FavoriteStore3': LogicalKeyboardKey.favoriteStore3,
  'FinalMode': LogicalKeyboardKey.finalMode,
  'Find': LogicalKeyboardKey.find,
  'Fn': LogicalKeyboardKey.fn,
  'FnLock': LogicalKeyboardKey.fnLock,
  'GoBack': LogicalKeyboardKey.goBack,
  'GoHome': LogicalKeyboardKey.goHome,
  'GroupFirst': LogicalKeyboardKey.groupFirst,
  'GroupLast': LogicalKeyboardKey.groupLast,
  'GroupNext': LogicalKeyboardKey.groupNext,
  'GroupPrevious': LogicalKeyboardKey.groupPrevious,
  'Guide': LogicalKeyboardKey.guide,
  'GuideNextDay': LogicalKeyboardKey.guideNextDay,
  'GuidePreviousDay': LogicalKeyboardKey.guidePreviousDay,
  'HangulMode': LogicalKeyboardKey.hangulMode,
  'HanjaMode': LogicalKeyboardKey.hanjaMode,
  'Hankaku': LogicalKeyboardKey.hankaku,
  'HeadsetHook': LogicalKeyboardKey.headsetHook,
  'Help': LogicalKeyboardKey.help,
  'Hibernate': LogicalKeyboardKey.hibernate,
  'Hiragana': LogicalKeyboardKey.hiragana,
  'HiraganaKatakana': LogicalKeyboardKey.hiraganaKatakana,
  'Home': LogicalKeyboardKey.home,
  'Hyper': LogicalKeyboardKey.hyper,
  'Info': LogicalKeyboardKey.info,
  'Insert': LogicalKeyboardKey.insert,
  'InstantReplay': LogicalKeyboardKey.instantReplay,
  'JunjaMode': LogicalKeyboardKey.junjaMode,
  'KanaMode': LogicalKeyboardKey.kanaMode,
  'KanjiMode': LogicalKeyboardKey.kanjiMode,
  'Katakana': LogicalKeyboardKey.katakana,
  'Key11': LogicalKeyboardKey.key11,
  'Key12': LogicalKeyboardKey.key12,
  'LastNumberRedial': LogicalKeyboardKey.lastNumberRedial,
  'LaunchApplication1': LogicalKeyboardKey.launchApplication1,
  'LaunchApplication2': LogicalKeyboardKey.launchApplication2,
  'LaunchAssistant': LogicalKeyboardKey.launchAssistant,
  'LaunchCalendar': LogicalKeyboardKey.launchCalendar,
  'LaunchContacts': LogicalKeyboardKey.launchContacts,
  'LaunchControlPanel': LogicalKeyboardKey.launchControlPanel,
  'LaunchMail': LogicalKeyboardKey.launchMail,
  'LaunchMediaPlayer': LogicalKeyboardKey.launchMediaPlayer,
  'LaunchMusicPlayer': LogicalKeyboardKey.launchMusicPlayer,
  'LaunchPhone': LogicalKeyboardKey.launchPhone,
  'LaunchScreenSaver': LogicalKeyboardKey.launchScreenSaver,
  'LaunchSpreadsheet': LogicalKeyboardKey.launchSpreadsheet,
  'LaunchWebBrowser': LogicalKeyboardKey.launchWebBrowser,
  'LaunchWebCam': LogicalKeyboardKey.launchWebCam,
  'LaunchWordProcessor': LogicalKeyboardKey.launchWordProcessor,
  'Link': LogicalKeyboardKey.link,
  'ListProgram': LogicalKeyboardKey.listProgram,
  'LiveContent': LogicalKeyboardKey.liveContent,
  'Lock': LogicalKeyboardKey.lock,
  'LogOff': LogicalKeyboardKey.logOff,
  'MailForward': LogicalKeyboardKey.mailForward,
  'MailReply': LogicalKeyboardKey.mailReply,
  'MailSend': LogicalKeyboardKey.mailSend,
  'MannerMode': LogicalKeyboardKey.mannerMode,
  'MediaApps': LogicalKeyboardKey.mediaApps,
  'MediaAudioTrack': LogicalKeyboardKey.mediaAudioTrack,
  'MediaClose': LogicalKeyboardKey.mediaClose,
  'MediaFastForward': LogicalKeyboardKey.mediaFastForward,
  'MediaLast': LogicalKeyboardKey.mediaLast,
  'MediaPause': LogicalKeyboardKey.mediaPause,
  'MediaPlay': LogicalKeyboardKey.mediaPlay,
  'MediaPlayPause': LogicalKeyboardKey.mediaPlayPause,
  'MediaRecord': LogicalKeyboardKey.mediaRecord,
  'MediaRewind': LogicalKeyboardKey.mediaRewind,
  'MediaSkip': LogicalKeyboardKey.mediaSkip,
  'MediaSkipBackward': LogicalKeyboardKey.mediaSkipBackward,
  'MediaSkipForward': LogicalKeyboardKey.mediaSkipForward,
  'MediaStepBackward': LogicalKeyboardKey.mediaStepBackward,
  'MediaStepForward': LogicalKeyboardKey.mediaStepForward,
  'MediaStop': LogicalKeyboardKey.mediaStop,
  'MediaTopMenu': LogicalKeyboardKey.mediaTopMenu,
  'MediaTrackNext': LogicalKeyboardKey.mediaTrackNext,
  'MediaTrackPrevious': LogicalKeyboardKey.mediaTrackPrevious,
  'MicrophoneToggle': LogicalKeyboardKey.microphoneToggle,
  'MicrophoneVolumeDown': LogicalKeyboardKey.microphoneVolumeDown,
  'MicrophoneVolumeMute': LogicalKeyboardKey.microphoneVolumeMute,
  'MicrophoneVolumeUp': LogicalKeyboardKey.microphoneVolumeUp,
  'ModeChange': LogicalKeyboardKey.modeChange,
  'NavigateIn': LogicalKeyboardKey.navigateIn,
  'NavigateNext': LogicalKeyboardKey.navigateNext,
  'NavigateOut': LogicalKeyboardKey.navigateOut,
  'NavigatePrevious': LogicalKeyboardKey.navigatePrevious,
  'New': LogicalKeyboardKey.newKey,
  'NextCandidate': LogicalKeyboardKey.nextCandidate,
  'NextFavoriteChannel': LogicalKeyboardKey.nextFavoriteChannel,
  'NextUserProfile': LogicalKeyboardKey.nextUserProfile,
  'NonConvert': LogicalKeyboardKey.nonConvert,
  'Notification': LogicalKeyboardKey.notification,
  'NumLock': LogicalKeyboardKey.numLock,
  'OnDemand': LogicalKeyboardKey.onDemand,
  'Open': LogicalKeyboardKey.open,
  'PageDown': LogicalKeyboardKey.pageDown,
  'PageUp': LogicalKeyboardKey.pageUp,
  'Pairing': LogicalKeyboardKey.pairing,
  'Paste': LogicalKeyboardKey.paste,
  'Pause': LogicalKeyboardKey.pause,
  'PinPDown': LogicalKeyboardKey.pInPDown,
  'PinPMove': LogicalKeyboardKey.pInPMove,
  'PinPToggle': LogicalKeyboardKey.pInPToggle,
  'PinPUp': LogicalKeyboardKey.pInPUp,
  'Play': LogicalKeyboardKey.play,
  'PlaySpeedDown': LogicalKeyboardKey.playSpeedDown,
  'PlaySpeedReset': LogicalKeyboardKey.playSpeedReset,
  'PlaySpeedUp': LogicalKeyboardKey.playSpeedUp,
  'Power': LogicalKeyboardKey.power,
  'PowerOff': LogicalKeyboardKey.powerOff,
  'PreviousCandidate': LogicalKeyboardKey.previousCandidate,
  'Print': LogicalKeyboardKey.print,
  'PrintScreen': LogicalKeyboardKey.printScreen,
  'Process': LogicalKeyboardKey.process,
  'Props': LogicalKeyboardKey.props,
  'RandomToggle': LogicalKeyboardKey.randomToggle,
  'RcLowBattery': LogicalKeyboardKey.rcLowBattery,
  'RecordSpeedNext': LogicalKeyboardKey.recordSpeedNext,
  'Redo': LogicalKeyboardKey.redo,
  'RfBypass': LogicalKeyboardKey.rfBypass,
  'Romaji': LogicalKeyboardKey.romaji,
  'STBInput': LogicalKeyboardKey.stbInput,
  'STBPower': LogicalKeyboardKey.stbPower,
  'Save': LogicalKeyboardKey.save,
  'ScanChannelsToggle': LogicalKeyboardKey.scanChannelsToggle,
  'ScreenModeNext': LogicalKeyboardKey.screenModeNext,
  'ScrollLock': LogicalKeyboardKey.scrollLock,
  'Select': LogicalKeyboardKey.select,
  'Settings': LogicalKeyboardKey.settings,
  'ShiftLevel5': LogicalKeyboardKey.shiftLevel5,
  'SingleCandidate': LogicalKeyboardKey.singleCandidate,
  'Soft1': LogicalKeyboardKey.soft1,
  'Soft2': LogicalKeyboardKey.soft2,
  'Soft3': LogicalKeyboardKey.soft3,
  'Soft4': LogicalKeyboardKey.soft4,
  'Soft5': LogicalKeyboardKey.soft5,
  'Soft6': LogicalKeyboardKey.soft6,
  'Soft7': LogicalKeyboardKey.soft7,
  'Soft8': LogicalKeyboardKey.soft8,
  'SpeechCorrectionList': LogicalKeyboardKey.speechCorrectionList,
  'SpeechInputToggle': LogicalKeyboardKey.speechInputToggle,
  'SpellCheck': LogicalKeyboardKey.spellCheck,
  'SplitScreenToggle': LogicalKeyboardKey.splitScreenToggle,
  'Standby': LogicalKeyboardKey.standby,
  'Subtitle': LogicalKeyboardKey.subtitle,
  'Super': LogicalKeyboardKey.superKey,
  'Symbol': LogicalKeyboardKey.symbol,
  'SymbolLock': LogicalKeyboardKey.symbolLock,
  'TV': LogicalKeyboardKey.tv,
  'TV3DMode': LogicalKeyboardKey.tv3DMode,
  'TVAntennaCable': LogicalKeyboardKey.tvAntennaCable,
  'TVAudioDescription': LogicalKeyboardKey.tvAudioDescription,
  'TVAudioDescriptionMixDown': LogicalKeyboardKey.tvAudioDescriptionMixDown,
  'TVAudioDescriptionMixUp': LogicalKeyboardKey.tvAudioDescriptionMixUp,
  'TVContentsMenu': LogicalKeyboardKey.tvContentsMenu,
  'TVDataService': LogicalKeyboardKey.tvDataService,
  'TVInput': LogicalKeyboardKey.tvInput,
  'TVInputComponent1': LogicalKeyboardKey.tvInputComponent1,
  'TVInputComponent2': LogicalKeyboardKey.tvInputComponent2,
  'TVInputComposite1': LogicalKeyboardKey.tvInputComposite1,
  'TVInputComposite2': LogicalKeyboardKey.tvInputComposite2,
  'TVInputHDMI1': LogicalKeyboardKey.tvInputHDMI1,
  'TVInputHDMI2': LogicalKeyboardKey.tvInputHDMI2,
  'TVInputHDMI3': LogicalKeyboardKey.tvInputHDMI3,
  'TVInputHDMI4': LogicalKeyboardKey.tvInputHDMI4,
  'TVInputVGA1': LogicalKeyboardKey.tvInputVGA1,
  'TVMediaContext': LogicalKeyboardKey.tvMediaContext,
  'TVNetwork': LogicalKeyboardKey.tvNetwork,
  'TVNumberEntry': LogicalKeyboardKey.tvNumberEntry,
  'TVPower': LogicalKeyboardKey.tvPower,
  'TVRadioService': LogicalKeyboardKey.tvRadioService,
  'TVSatellite': LogicalKeyboardKey.tvSatellite,
  'TVSatelliteBS': LogicalKeyboardKey.tvSatelliteBS,
  'TVSatelliteCS': LogicalKeyboardKey.tvSatelliteCS,
  'TVSatelliteToggle': LogicalKeyboardKey.tvSatelliteToggle,
  'TVTerrestrialAnalog': LogicalKeyboardKey.tvTerrestrialAnalog,
  'TVTerrestrialDigital': LogicalKeyboardKey.tvTerrestrialDigital,
  'TVTimer': LogicalKeyboardKey.tvTimer,
  'Tab': LogicalKeyboardKey.tab,
  'Teletext': LogicalKeyboardKey.teletext,
  'Undo': LogicalKeyboardKey.undo,
  'Unidentified': LogicalKeyboardKey.unidentified,
  'VideoModeNext': LogicalKeyboardKey.videoModeNext,
  'VoiceDial': LogicalKeyboardKey.voiceDial,
  'WakeUp': LogicalKeyboardKey.wakeUp,
  'Wink': LogicalKeyboardKey.wink,
  'Zenkaku': LogicalKeyboardKey.zenkaku,
  'ZenkakuHankaku': LogicalKeyboardKey.zenkakuHankaku,
  'ZoomIn': LogicalKeyboardKey.zoomIn,
  'ZoomOut': LogicalKeyboardKey.zoomOut,
  'ZoomToggle': LogicalKeyboardKey.zoomToggle,
};

/// Maps Web KeyboardEvent codes to the matching [PhysicalKeyboardKey].
const Map<String, PhysicalKeyboardKey> kWebToPhysicalKey = <String, PhysicalKeyboardKey>{
  'Abort': PhysicalKeyboardKey.abort,
  'Again': PhysicalKeyboardKey.again,
  'AltLeft': PhysicalKeyboardKey.altLeft,
  'AltRight': PhysicalKeyboardKey.altRight,
  'ArrowDown': PhysicalKeyboardKey.arrowDown,
  'ArrowLeft': PhysicalKeyboardKey.arrowLeft,
  'ArrowRight': PhysicalKeyboardKey.arrowRight,
  'ArrowUp': PhysicalKeyboardKey.arrowUp,
  'AudioVolumeDown': PhysicalKeyboardKey.audioVolumeDown,
  'AudioVolumeMute': PhysicalKeyboardKey.audioVolumeMute,
  'AudioVolumeUp': PhysicalKeyboardKey.audioVolumeUp,
  'Backquote': PhysicalKeyboardKey.backquote,
  'Backslash': PhysicalKeyboardKey.backslash,
  'Backspace': PhysicalKeyboardKey.backspace,
  'BracketLeft': PhysicalKeyboardKey.bracketLeft,
  'BracketRight': PhysicalKeyboardKey.bracketRight,
  'BrightnessDown': PhysicalKeyboardKey.brightnessDown,
  'BrightnessUp': PhysicalKeyboardKey.brightnessUp,
  'BrowserBack': PhysicalKeyboardKey.browserBack,
  'BrowserFavorites': PhysicalKeyboardKey.browserFavorites,
  'BrowserForward': PhysicalKeyboardKey.browserForward,
  'BrowserHome': PhysicalKeyboardKey.browserHome,
  'BrowserRefresh': PhysicalKeyboardKey.browserRefresh,
  'BrowserSearch': PhysicalKeyboardKey.browserSearch,
  'BrowserStop': PhysicalKeyboardKey.browserStop,
  'CapsLock': PhysicalKeyboardKey.capsLock,
  'Comma': PhysicalKeyboardKey.comma,
  'ContextMenu': PhysicalKeyboardKey.contextMenu,
  'ControlLeft': PhysicalKeyboardKey.controlLeft,
  'ControlRight': PhysicalKeyboardKey.controlRight,
  'Convert': PhysicalKeyboardKey.convert,
  'Copy': PhysicalKeyboardKey.copy,
  'Cut': PhysicalKeyboardKey.cut,
  'Delete': PhysicalKeyboardKey.delete,
  'Digit0': PhysicalKeyboardKey.digit0,
  'Digit1': PhysicalKeyboardKey.digit1,
  'Digit2': PhysicalKeyboardKey.digit2,
  'Digit3': PhysicalKeyboardKey.digit3,
  'Digit4': PhysicalKeyboardKey.digit4,
  'Digit5': PhysicalKeyboardKey.digit5,
  'Digit6': PhysicalKeyboardKey.digit6,
  'Digit7': PhysicalKeyboardKey.digit7,
  'Digit8': PhysicalKeyboardKey.digit8,
  'Digit9': PhysicalKeyboardKey.digit9,
  'DisplayToggleIntExt': PhysicalKeyboardKey.displayToggleIntExt,
  'Eject': PhysicalKeyboardKey.eject,
  'End': PhysicalKeyboardKey.end,
  'Enter': PhysicalKeyboardKey.enter,
  'Equal': PhysicalKeyboardKey.equal,
  'Escape': PhysicalKeyboardKey.escape,
  'Esc': PhysicalKeyboardKey.escape,
  'F1': PhysicalKeyboardKey.f1,
  'F10': PhysicalKeyboardKey.f10,
  'F11': PhysicalKeyboardKey.f11,
  'F12': PhysicalKeyboardKey.f12,
  'F13': PhysicalKeyboardKey.f13,
  'F14': PhysicalKeyboardKey.f14,
  'F15': PhysicalKeyboardKey.f15,
  'F16': PhysicalKeyboardKey.f16,
  'F17': PhysicalKeyboardKey.f17,
  'F18': PhysicalKeyboardKey.f18,
  'F19': PhysicalKeyboardKey.f19,
  'F2': PhysicalKeyboardKey.f2,
  'F20': PhysicalKeyboardKey.f20,
  'F21': PhysicalKeyboardKey.f21,
  'F22': PhysicalKeyboardKey.f22,
  'F23': PhysicalKeyboardKey.f23,
  'F24': PhysicalKeyboardKey.f24,
  'F3': PhysicalKeyboardKey.f3,
  'F4': PhysicalKeyboardKey.f4,
  'F5': PhysicalKeyboardKey.f5,
  'F6': PhysicalKeyboardKey.f6,
  'F7': PhysicalKeyboardKey.f7,
  'F8': PhysicalKeyboardKey.f8,
  'F9': PhysicalKeyboardKey.f9,
  'Find': PhysicalKeyboardKey.find,
  'Fn': PhysicalKeyboardKey.fn,
  'FnLock': PhysicalKeyboardKey.fnLock,
  'GameButton1': PhysicalKeyboardKey.gameButton1,
  'GameButton10': PhysicalKeyboardKey.gameButton10,
  'GameButton11': PhysicalKeyboardKey.gameButton11,
  'GameButton12': PhysicalKeyboardKey.gameButton12,
  'GameButton13': PhysicalKeyboardKey.gameButton13,
  'GameButton14': PhysicalKeyboardKey.gameButton14,
  'GameButton15': PhysicalKeyboardKey.gameButton15,
  'GameButton16': PhysicalKeyboardKey.gameButton16,
  'GameButton2': PhysicalKeyboardKey.gameButton2,
  'GameButton3': PhysicalKeyboardKey.gameButton3,
  'GameButton4': PhysicalKeyboardKey.gameButton4,
  'GameButton5': PhysicalKeyboardKey.gameButton5,
  'GameButton6': PhysicalKeyboardKey.gameButton6,
  'GameButton7': PhysicalKeyboardKey.gameButton7,
  'GameButton8': PhysicalKeyboardKey.gameButton8,
  'GameButton9': PhysicalKeyboardKey.gameButton9,
  'GameButtonA': PhysicalKeyboardKey.gameButtonA,
  'GameButtonB': PhysicalKeyboardKey.gameButtonB,
  'GameButtonC': PhysicalKeyboardKey.gameButtonC,
  'GameButtonLeft1': PhysicalKeyboardKey.gameButtonLeft1,
  'GameButtonLeft2': PhysicalKeyboardKey.gameButtonLeft2,
  'GameButtonMode': PhysicalKeyboardKey.gameButtonMode,
  'GameButtonRight1': PhysicalKeyboardKey.gameButtonRight1,
  'GameButtonRight2': PhysicalKeyboardKey.gameButtonRight2,
  'GameButtonSelect': PhysicalKeyboardKey.gameButtonSelect,
  'GameButtonStart': PhysicalKeyboardKey.gameButtonStart,
  'GameButtonThumbLeft': PhysicalKeyboardKey.gameButtonThumbLeft,
  'GameButtonThumbRight': PhysicalKeyboardKey.gameButtonThumbRight,
  'GameButtonX': PhysicalKeyboardKey.gameButtonX,
  'GameButtonY': PhysicalKeyboardKey.gameButtonY,
  'GameButtonZ': PhysicalKeyboardKey.gameButtonZ,
  'Help': PhysicalKeyboardKey.help,
  'Home': PhysicalKeyboardKey.home,
  'Hyper': PhysicalKeyboardKey.hyper,
  'Insert': PhysicalKeyboardKey.insert,
  'IntlBackslash': PhysicalKeyboardKey.intlBackslash,
  'IntlRo': PhysicalKeyboardKey.intlRo,
  'IntlYen': PhysicalKeyboardKey.intlYen,
  'KanaMode': PhysicalKeyboardKey.kanaMode,
  'KeyA': PhysicalKeyboardKey.keyA,
  'KeyB': PhysicalKeyboardKey.keyB,
  'KeyC': PhysicalKeyboardKey.keyC,
  'KeyD': PhysicalKeyboardKey.keyD,
  'KeyE': PhysicalKeyboardKey.keyE,
  'KeyF': PhysicalKeyboardKey.keyF,
  'KeyG': PhysicalKeyboardKey.keyG,
  'KeyH': PhysicalKeyboardKey.keyH,
  'KeyI': PhysicalKeyboardKey.keyI,
  'KeyJ': PhysicalKeyboardKey.keyJ,
  'KeyK': PhysicalKeyboardKey.keyK,
  'KeyL': PhysicalKeyboardKey.keyL,
  'KeyM': PhysicalKeyboardKey.keyM,
  'KeyN': PhysicalKeyboardKey.keyN,
  'KeyO': PhysicalKeyboardKey.keyO,
  'KeyP': PhysicalKeyboardKey.keyP,
  'KeyQ': PhysicalKeyboardKey.keyQ,
  'KeyR': PhysicalKeyboardKey.keyR,
  'KeyS': PhysicalKeyboardKey.keyS,
  'KeyT': PhysicalKeyboardKey.keyT,
  'KeyU': PhysicalKeyboardKey.keyU,
  'KeyV': PhysicalKeyboardKey.keyV,
  'KeyW': PhysicalKeyboardKey.keyW,
  'KeyX': PhysicalKeyboardKey.keyX,
  'KeyY': PhysicalKeyboardKey.keyY,
  'KeyZ': PhysicalKeyboardKey.keyZ,
  'KeyboardLayoutSelect': PhysicalKeyboardKey.keyboardLayoutSelect,
  'Lang1': PhysicalKeyboardKey.lang1,
  'Lang2': PhysicalKeyboardKey.lang2,
  'Lang3': PhysicalKeyboardKey.lang3,
  'Lang4': PhysicalKeyboardKey.lang4,
  'Lang5': PhysicalKeyboardKey.lang5,
  'LaunchApp1': PhysicalKeyboardKey.launchApp1,
  'LaunchApp2': PhysicalKeyboardKey.launchApp2,
  'LaunchAssistant': PhysicalKeyboardKey.launchAssistant,
  'LaunchControlPanel': PhysicalKeyboardKey.launchControlPanel,
  'LaunchMail': PhysicalKeyboardKey.launchMail,
  'LaunchScreenSaver': PhysicalKeyboardKey.launchScreenSaver,
  'MailForward': PhysicalKeyboardKey.mailForward,
  'MailReply': PhysicalKeyboardKey.mailReply,
  'MailSend': PhysicalKeyboardKey.mailSend,
  'MediaFastForward': PhysicalKeyboardKey.mediaFastForward,
  'MediaPause': PhysicalKeyboardKey.mediaPause,
  'MediaPlay': PhysicalKeyboardKey.mediaPlay,
  'MediaPlayPause': PhysicalKeyboardKey.mediaPlayPause,
  'MediaRecord': PhysicalKeyboardKey.mediaRecord,
  'MediaRewind': PhysicalKeyboardKey.mediaRewind,
  'MediaSelect': PhysicalKeyboardKey.mediaSelect,
  'MediaStop': PhysicalKeyboardKey.mediaStop,
  'MediaTrackNext': PhysicalKeyboardKey.mediaTrackNext,
  'MediaTrackPrevious': PhysicalKeyboardKey.mediaTrackPrevious,
  'MetaLeft': PhysicalKeyboardKey.metaLeft,
  'MetaRight': PhysicalKeyboardKey.metaRight,
  'MicrophoneMuteToggle': PhysicalKeyboardKey.microphoneMuteToggle,
  'Minus': PhysicalKeyboardKey.minus,
  'NonConvert': PhysicalKeyboardKey.nonConvert,
  'NumLock': PhysicalKeyboardKey.numLock,
  'Numpad0': PhysicalKeyboardKey.numpad0,
  'Numpad1': PhysicalKeyboardKey.numpad1,
  'Numpad2': PhysicalKeyboardKey.numpad2,
  'Numpad3': PhysicalKeyboardKey.numpad3,
  'Numpad4': PhysicalKeyboardKey.numpad4,
  'Numpad5': PhysicalKeyboardKey.numpad5,
  'Numpad6': PhysicalKeyboardKey.numpad6,
  'Numpad7': PhysicalKeyboardKey.numpad7,
  'Numpad8': PhysicalKeyboardKey.numpad8,
  'Numpad9': PhysicalKeyboardKey.numpad9,
  'NumpadAdd': PhysicalKeyboardKey.numpadAdd,
  'NumpadBackspace': PhysicalKeyboardKey.numpadBackspace,
  'NumpadClear': PhysicalKeyboardKey.numpadClear,
  'NumpadClearEntry': PhysicalKeyboardKey.numpadClearEntry,
  'NumpadComma': PhysicalKeyboardKey.numpadComma,
  'NumpadDecimal': PhysicalKeyboardKey.numpadDecimal,
  'NumpadDivide': PhysicalKeyboardKey.numpadDivide,
  'NumpadEnter': PhysicalKeyboardKey.numpadEnter,
  'NumpadEqual': PhysicalKeyboardKey.numpadEqual,
  'NumpadMemoryAdd': PhysicalKeyboardKey.numpadMemoryAdd,
  'NumpadMemoryClear': PhysicalKeyboardKey.numpadMemoryClear,
  'NumpadMemoryRecall': PhysicalKeyboardKey.numpadMemoryRecall,
  'NumpadMemoryStore': PhysicalKeyboardKey.numpadMemoryStore,
  'NumpadMemorySubtract': PhysicalKeyboardKey.numpadMemorySubtract,
  'NumpadMultiply': PhysicalKeyboardKey.numpadMultiply,
  'NumpadParenLeft': PhysicalKeyboardKey.numpadParenLeft,
  'NumpadParenRight': PhysicalKeyboardKey.numpadParenRight,
  'NumpadSubtract': PhysicalKeyboardKey.numpadSubtract,
  'Open': PhysicalKeyboardKey.open,
  'PageDown': PhysicalKeyboardKey.pageDown,
  'PageUp': PhysicalKeyboardKey.pageUp,
  'Paste': PhysicalKeyboardKey.paste,
  'Pause': PhysicalKeyboardKey.pause,
  'Period': PhysicalKeyboardKey.period,
  'Power': PhysicalKeyboardKey.power,
  'PrintScreen': PhysicalKeyboardKey.printScreen,
  'PrivacyScreenToggle': PhysicalKeyboardKey.privacyScreenToggle,
  'Props': PhysicalKeyboardKey.props,
  'Quote': PhysicalKeyboardKey.quote,
  'Resume': PhysicalKeyboardKey.resume,
  'ScrollLock': PhysicalKeyboardKey.scrollLock,
  'Select': PhysicalKeyboardKey.select,
  'SelectTask': PhysicalKeyboardKey.selectTask,
  'Semicolon': PhysicalKeyboardKey.semicolon,
  'ShiftLeft': PhysicalKeyboardKey.shiftLeft,
  'ShiftRight': PhysicalKeyboardKey.shiftRight,
  'ShowAllWindows': PhysicalKeyboardKey.showAllWindows,
  'Slash': PhysicalKeyboardKey.slash,
  'Sleep': PhysicalKeyboardKey.sleep,
  'Space': PhysicalKeyboardKey.space,
  'Super': PhysicalKeyboardKey.superKey,
  'Suspend': PhysicalKeyboardKey.suspend,
  'Tab': PhysicalKeyboardKey.tab,
  'Turbo': PhysicalKeyboardKey.turbo,
  'Undo': PhysicalKeyboardKey.undo,
  'WakeUp': PhysicalKeyboardKey.wakeUp,
  'ZoomToggle': PhysicalKeyboardKey.zoomToggle,
};

/// A map of Web KeyboardEvent codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<String, LogicalKeyboardKey> kWebNumPadMap = <String, LogicalKeyboardKey>{
  'Numpad0': LogicalKeyboardKey.numpad0,
  'Numpad1': LogicalKeyboardKey.numpad1,
  'Numpad2': LogicalKeyboardKey.numpad2,
  'Numpad3': LogicalKeyboardKey.numpad3,
  'Numpad4': LogicalKeyboardKey.numpad4,
  'Numpad5': LogicalKeyboardKey.numpad5,
  'Numpad6': LogicalKeyboardKey.numpad6,
  'Numpad7': LogicalKeyboardKey.numpad7,
  'Numpad8': LogicalKeyboardKey.numpad8,
  'Numpad9': LogicalKeyboardKey.numpad9,
  'NumpadAdd': LogicalKeyboardKey.numpadAdd,
  'NumpadComma': LogicalKeyboardKey.numpadComma,
  'NumpadDecimal': LogicalKeyboardKey.numpadDecimal,
  'NumpadDivide': LogicalKeyboardKey.numpadDivide,
  'NumpadEqual': LogicalKeyboardKey.numpadEqual,
  'NumpadMultiply': LogicalKeyboardKey.numpadMultiply,
  'NumpadParenLeft': LogicalKeyboardKey.numpadParenLeft,
  'NumpadParenRight': LogicalKeyboardKey.numpadParenRight,
  'NumpadSubtract': LogicalKeyboardKey.numpadSubtract,
};

/// A map of Web KeyboardEvent keys which needs to be decided based on location,
/// typically for numpad keys and modifier keys. Used to provide different key
/// objects for keys like KEY_EQUALS and NUMPAD_EQUALS.
const Map<String, List<LogicalKeyboardKey?>> kWebLocationMap = <String, List<LogicalKeyboardKey?>>{
  '*': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.asterisk,
    null,
    null,
    LogicalKeyboardKey.numpadMultiply,
  ],
  '+': <LogicalKeyboardKey?>[LogicalKeyboardKey.add, null, null, LogicalKeyboardKey.numpadAdd],
  '-': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.minus,
    null,
    null,
    LogicalKeyboardKey.numpadSubtract,
  ],
  '.': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.period,
    null,
    null,
    LogicalKeyboardKey.numpadDecimal,
  ],
  '/': <LogicalKeyboardKey?>[LogicalKeyboardKey.slash, null, null, LogicalKeyboardKey.numpadDivide],
  '0': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit0, null, null, LogicalKeyboardKey.numpad0],
  '1': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit1, null, null, LogicalKeyboardKey.numpad1],
  '2': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit2, null, null, LogicalKeyboardKey.numpad2],
  '3': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit3, null, null, LogicalKeyboardKey.numpad3],
  '4': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit4, null, null, LogicalKeyboardKey.numpad4],
  '5': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit5, null, null, LogicalKeyboardKey.numpad5],
  '6': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit6, null, null, LogicalKeyboardKey.numpad6],
  '7': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit7, null, null, LogicalKeyboardKey.numpad7],
  '8': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit8, null, null, LogicalKeyboardKey.numpad8],
  '9': <LogicalKeyboardKey?>[LogicalKeyboardKey.digit9, null, null, LogicalKeyboardKey.numpad9],
  'Alt': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.altLeft,
    LogicalKeyboardKey.altLeft,
    LogicalKeyboardKey.altRight,
    null,
  ],
  'AltGraph': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.altGraph,
    null,
    LogicalKeyboardKey.altGraph,
    null,
  ],
  'ArrowDown': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.arrowDown,
    null,
    null,
    LogicalKeyboardKey.numpad2,
  ],
  'ArrowLeft': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.arrowLeft,
    null,
    null,
    LogicalKeyboardKey.numpad4,
  ],
  'ArrowRight': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.arrowRight,
    null,
    null,
    LogicalKeyboardKey.numpad6,
  ],
  'ArrowUp': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.arrowUp,
    null,
    null,
    LogicalKeyboardKey.numpad8,
  ],
  'Clear': <LogicalKeyboardKey?>[LogicalKeyboardKey.clear, null, null, LogicalKeyboardKey.numpad5],
  'Control': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.controlLeft,
    LogicalKeyboardKey.controlLeft,
    LogicalKeyboardKey.controlRight,
    null,
  ],
  'Delete': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.delete,
    null,
    null,
    LogicalKeyboardKey.numpadDecimal,
  ],
  'End': <LogicalKeyboardKey?>[LogicalKeyboardKey.end, null, null, LogicalKeyboardKey.numpad1],
  'Enter': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.enter,
    null,
    null,
    LogicalKeyboardKey.numpadEnter,
  ],
  'Home': <LogicalKeyboardKey?>[LogicalKeyboardKey.home, null, null, LogicalKeyboardKey.numpad7],
  'Insert': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.insert,
    null,
    null,
    LogicalKeyboardKey.numpad0,
  ],
  'Meta': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.metaLeft,
    LogicalKeyboardKey.metaLeft,
    LogicalKeyboardKey.metaRight,
    null,
  ],
  'PageDown': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.pageDown,
    null,
    null,
    LogicalKeyboardKey.numpad3,
  ],
  'PageUp': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.pageUp,
    null,
    null,
    LogicalKeyboardKey.numpad9,
  ],
  'Shift': <LogicalKeyboardKey?>[
    LogicalKeyboardKey.shiftLeft,
    LogicalKeyboardKey.shiftLeft,
    LogicalKeyboardKey.shiftRight,
    null,
  ],
};

/// Maps Windows KeyboardEvent codes to the matching [LogicalKeyboardKey].
const Map<int, LogicalKeyboardKey> kWindowsToLogicalKey = <int, LogicalKeyboardKey>{
  3: LogicalKeyboardKey.cancel,
  8: LogicalKeyboardKey.backspace,
  9: LogicalKeyboardKey.tab,
  12: LogicalKeyboardKey.clear,
  13: LogicalKeyboardKey.enter,
  16: LogicalKeyboardKey.shiftLeft,
  17: LogicalKeyboardKey.controlLeft,
  19: LogicalKeyboardKey.pause,
  20: LogicalKeyboardKey.capsLock,
  21: LogicalKeyboardKey.lang1,
  23: LogicalKeyboardKey.junjaMode,
  24: LogicalKeyboardKey.finalMode,
  25: LogicalKeyboardKey.kanjiMode,
  27: LogicalKeyboardKey.escape,
  28: LogicalKeyboardKey.convert,
  30: LogicalKeyboardKey.accept,
  31: LogicalKeyboardKey.modeChange,
  32: LogicalKeyboardKey.space,
  33: LogicalKeyboardKey.pageUp,
  34: LogicalKeyboardKey.pageDown,
  35: LogicalKeyboardKey.end,
  36: LogicalKeyboardKey.home,
  37: LogicalKeyboardKey.arrowLeft,
  38: LogicalKeyboardKey.arrowUp,
  39: LogicalKeyboardKey.arrowRight,
  40: LogicalKeyboardKey.arrowDown,
  41: LogicalKeyboardKey.select,
  42: LogicalKeyboardKey.print,
  43: LogicalKeyboardKey.execute,
  44: LogicalKeyboardKey.printScreen,
  45: LogicalKeyboardKey.insert,
  46: LogicalKeyboardKey.delete,
  47: LogicalKeyboardKey.help,
  48: LogicalKeyboardKey.digit0,
  49: LogicalKeyboardKey.digit1,
  50: LogicalKeyboardKey.digit2,
  51: LogicalKeyboardKey.digit3,
  52: LogicalKeyboardKey.digit4,
  53: LogicalKeyboardKey.digit5,
  54: LogicalKeyboardKey.digit6,
  55: LogicalKeyboardKey.digit7,
  56: LogicalKeyboardKey.digit8,
  57: LogicalKeyboardKey.digit9,
  65: LogicalKeyboardKey.keyA,
  66: LogicalKeyboardKey.keyB,
  67: LogicalKeyboardKey.keyC,
  68: LogicalKeyboardKey.keyD,
  69: LogicalKeyboardKey.keyE,
  70: LogicalKeyboardKey.keyF,
  71: LogicalKeyboardKey.keyG,
  72: LogicalKeyboardKey.keyH,
  73: LogicalKeyboardKey.keyI,
  74: LogicalKeyboardKey.keyJ,
  75: LogicalKeyboardKey.keyK,
  76: LogicalKeyboardKey.keyL,
  77: LogicalKeyboardKey.keyM,
  78: LogicalKeyboardKey.keyN,
  79: LogicalKeyboardKey.keyO,
  80: LogicalKeyboardKey.keyP,
  81: LogicalKeyboardKey.keyQ,
  82: LogicalKeyboardKey.keyR,
  83: LogicalKeyboardKey.keyS,
  84: LogicalKeyboardKey.keyT,
  85: LogicalKeyboardKey.keyU,
  86: LogicalKeyboardKey.keyV,
  87: LogicalKeyboardKey.keyW,
  88: LogicalKeyboardKey.keyX,
  89: LogicalKeyboardKey.keyY,
  90: LogicalKeyboardKey.keyZ,
  91: LogicalKeyboardKey.metaLeft,
  92: LogicalKeyboardKey.metaRight,
  93: LogicalKeyboardKey.contextMenu,
  95: LogicalKeyboardKey.sleep,
  96: LogicalKeyboardKey.numpad0,
  97: LogicalKeyboardKey.numpad1,
  98: LogicalKeyboardKey.numpad2,
  99: LogicalKeyboardKey.numpad3,
  100: LogicalKeyboardKey.numpad4,
  101: LogicalKeyboardKey.numpad5,
  102: LogicalKeyboardKey.numpad6,
  103: LogicalKeyboardKey.numpad7,
  104: LogicalKeyboardKey.numpad8,
  105: LogicalKeyboardKey.numpad9,
  106: LogicalKeyboardKey.numpadMultiply,
  107: LogicalKeyboardKey.numpadAdd,
  108: LogicalKeyboardKey.numpadComma,
  109: LogicalKeyboardKey.numpadSubtract,
  110: LogicalKeyboardKey.numpadDecimal,
  111: LogicalKeyboardKey.numpadDivide,
  112: LogicalKeyboardKey.f1,
  113: LogicalKeyboardKey.f2,
  114: LogicalKeyboardKey.f3,
  115: LogicalKeyboardKey.f4,
  116: LogicalKeyboardKey.f5,
  117: LogicalKeyboardKey.f6,
  118: LogicalKeyboardKey.f7,
  119: LogicalKeyboardKey.f8,
  120: LogicalKeyboardKey.f9,
  121: LogicalKeyboardKey.f10,
  122: LogicalKeyboardKey.f11,
  123: LogicalKeyboardKey.f12,
  124: LogicalKeyboardKey.f13,
  125: LogicalKeyboardKey.f14,
  126: LogicalKeyboardKey.f15,
  127: LogicalKeyboardKey.f16,
  128: LogicalKeyboardKey.f17,
  129: LogicalKeyboardKey.f18,
  130: LogicalKeyboardKey.f19,
  131: LogicalKeyboardKey.f20,
  132: LogicalKeyboardKey.f21,
  133: LogicalKeyboardKey.f22,
  134: LogicalKeyboardKey.f23,
  135: LogicalKeyboardKey.f24,
  144: LogicalKeyboardKey.numLock,
  145: LogicalKeyboardKey.scrollLock,
  146: LogicalKeyboardKey.numpadEqual,
  160: LogicalKeyboardKey.shiftLeft,
  161: LogicalKeyboardKey.shiftRight,
  162: LogicalKeyboardKey.controlLeft,
  163: LogicalKeyboardKey.controlRight,
  164: LogicalKeyboardKey.altLeft,
  165: LogicalKeyboardKey.altRight,
  166: LogicalKeyboardKey.browserBack,
  167: LogicalKeyboardKey.browserForward,
  168: LogicalKeyboardKey.browserRefresh,
  169: LogicalKeyboardKey.browserStop,
  170: LogicalKeyboardKey.browserSearch,
  171: LogicalKeyboardKey.browserFavorites,
  172: LogicalKeyboardKey.browserHome,
  173: LogicalKeyboardKey.audioVolumeMute,
  174: LogicalKeyboardKey.audioVolumeDown,
  175: LogicalKeyboardKey.audioVolumeUp,
  178: LogicalKeyboardKey.mediaStop,
  179: LogicalKeyboardKey.mediaPlayPause,
  180: LogicalKeyboardKey.launchMail,
  186: LogicalKeyboardKey.semicolon,
  187: LogicalKeyboardKey.equal,
  188: LogicalKeyboardKey.comma,
  189: LogicalKeyboardKey.minus,
  190: LogicalKeyboardKey.period,
  191: LogicalKeyboardKey.slash,
  192: LogicalKeyboardKey.backquote,
  195: LogicalKeyboardKey.gameButton8,
  196: LogicalKeyboardKey.gameButton9,
  197: LogicalKeyboardKey.gameButton10,
  198: LogicalKeyboardKey.gameButton11,
  199: LogicalKeyboardKey.gameButton12,
  200: LogicalKeyboardKey.gameButton13,
  201: LogicalKeyboardKey.gameButton14,
  202: LogicalKeyboardKey.gameButton15,
  203: LogicalKeyboardKey.gameButton16,
  219: LogicalKeyboardKey.bracketLeft,
  220: LogicalKeyboardKey.backslash,
  221: LogicalKeyboardKey.bracketRight,
  222: LogicalKeyboardKey.quote,
  246: LogicalKeyboardKey.attn,
  250: LogicalKeyboardKey.play,
};

/// Maps Windows KeyboardEvent codes to the matching [PhysicalKeyboardKey].
const Map<int, PhysicalKeyboardKey> kWindowsToPhysicalKey = <int, PhysicalKeyboardKey>{
  1: PhysicalKeyboardKey.escape,
  2: PhysicalKeyboardKey.digit1,
  3: PhysicalKeyboardKey.digit2,
  4: PhysicalKeyboardKey.digit3,
  5: PhysicalKeyboardKey.digit4,
  6: PhysicalKeyboardKey.digit5,
  7: PhysicalKeyboardKey.digit6,
  8: PhysicalKeyboardKey.digit7,
  9: PhysicalKeyboardKey.digit8,
  10: PhysicalKeyboardKey.digit9,
  11: PhysicalKeyboardKey.digit0,
  12: PhysicalKeyboardKey.minus,
  13: PhysicalKeyboardKey.equal,
  14: PhysicalKeyboardKey.backspace,
  15: PhysicalKeyboardKey.tab,
  16: PhysicalKeyboardKey.keyQ,
  17: PhysicalKeyboardKey.keyW,
  18: PhysicalKeyboardKey.keyE,
  19: PhysicalKeyboardKey.keyR,
  20: PhysicalKeyboardKey.keyT,
  21: PhysicalKeyboardKey.keyY,
  22: PhysicalKeyboardKey.keyU,
  23: PhysicalKeyboardKey.keyI,
  24: PhysicalKeyboardKey.keyO,
  25: PhysicalKeyboardKey.keyP,
  26: PhysicalKeyboardKey.bracketLeft,
  27: PhysicalKeyboardKey.bracketRight,
  28: PhysicalKeyboardKey.enter,
  29: PhysicalKeyboardKey.controlLeft,
  30: PhysicalKeyboardKey.keyA,
  31: PhysicalKeyboardKey.keyS,
  32: PhysicalKeyboardKey.keyD,
  33: PhysicalKeyboardKey.keyF,
  34: PhysicalKeyboardKey.keyG,
  35: PhysicalKeyboardKey.keyH,
  36: PhysicalKeyboardKey.keyJ,
  37: PhysicalKeyboardKey.keyK,
  38: PhysicalKeyboardKey.keyL,
  39: PhysicalKeyboardKey.semicolon,
  40: PhysicalKeyboardKey.quote,
  41: PhysicalKeyboardKey.backquote,
  42: PhysicalKeyboardKey.shiftLeft,
  43: PhysicalKeyboardKey.backslash,
  44: PhysicalKeyboardKey.keyZ,
  45: PhysicalKeyboardKey.keyX,
  46: PhysicalKeyboardKey.keyC,
  47: PhysicalKeyboardKey.keyV,
  48: PhysicalKeyboardKey.keyB,
  49: PhysicalKeyboardKey.keyN,
  50: PhysicalKeyboardKey.keyM,
  51: PhysicalKeyboardKey.comma,
  52: PhysicalKeyboardKey.period,
  53: PhysicalKeyboardKey.slash,
  54: PhysicalKeyboardKey.shiftRight,
  55: PhysicalKeyboardKey.numpadMultiply,
  56: PhysicalKeyboardKey.altLeft,
  57: PhysicalKeyboardKey.space,
  58: PhysicalKeyboardKey.capsLock,
  59: PhysicalKeyboardKey.f1,
  60: PhysicalKeyboardKey.f2,
  61: PhysicalKeyboardKey.f3,
  62: PhysicalKeyboardKey.f4,
  63: PhysicalKeyboardKey.f5,
  64: PhysicalKeyboardKey.f6,
  65: PhysicalKeyboardKey.f7,
  66: PhysicalKeyboardKey.f8,
  67: PhysicalKeyboardKey.f9,
  68: PhysicalKeyboardKey.f10,
  69: PhysicalKeyboardKey.pause,
  70: PhysicalKeyboardKey.scrollLock,
  71: PhysicalKeyboardKey.numpad7,
  72: PhysicalKeyboardKey.numpad8,
  73: PhysicalKeyboardKey.numpad9,
  74: PhysicalKeyboardKey.numpadSubtract,
  75: PhysicalKeyboardKey.numpad4,
  76: PhysicalKeyboardKey.numpad5,
  77: PhysicalKeyboardKey.numpad6,
  78: PhysicalKeyboardKey.numpadAdd,
  79: PhysicalKeyboardKey.numpad1,
  80: PhysicalKeyboardKey.numpad2,
  81: PhysicalKeyboardKey.numpad3,
  82: PhysicalKeyboardKey.numpad0,
  83: PhysicalKeyboardKey.numpadDecimal,
  86: PhysicalKeyboardKey.intlBackslash,
  87: PhysicalKeyboardKey.f11,
  88: PhysicalKeyboardKey.f12,
  89: PhysicalKeyboardKey.numpadEqual,
  100: PhysicalKeyboardKey.f13,
  101: PhysicalKeyboardKey.f14,
  102: PhysicalKeyboardKey.f15,
  103: PhysicalKeyboardKey.f16,
  104: PhysicalKeyboardKey.f17,
  105: PhysicalKeyboardKey.f18,
  106: PhysicalKeyboardKey.f19,
  107: PhysicalKeyboardKey.f20,
  108: PhysicalKeyboardKey.f21,
  109: PhysicalKeyboardKey.f22,
  110: PhysicalKeyboardKey.f23,
  112: PhysicalKeyboardKey.kanaMode,
  113: PhysicalKeyboardKey.lang2,
  114: PhysicalKeyboardKey.lang1,
  115: PhysicalKeyboardKey.intlRo,
  118: PhysicalKeyboardKey.f24,
  119: PhysicalKeyboardKey.lang4,
  120: PhysicalKeyboardKey.lang3,
  121: PhysicalKeyboardKey.convert,
  123: PhysicalKeyboardKey.nonConvert,
  125: PhysicalKeyboardKey.intlYen,
  126: PhysicalKeyboardKey.numpadComma,
  252: PhysicalKeyboardKey.usbPostFail,
  255: PhysicalKeyboardKey.usbErrorRollOver,
  57352: PhysicalKeyboardKey.undo,
  57354: PhysicalKeyboardKey.paste,
  57360: PhysicalKeyboardKey.mediaTrackPrevious,
  57367: PhysicalKeyboardKey.cut,
  57368: PhysicalKeyboardKey.copy,
  57369: PhysicalKeyboardKey.mediaTrackNext,
  57372: PhysicalKeyboardKey.numpadEnter,
  57373: PhysicalKeyboardKey.controlRight,
  57376: PhysicalKeyboardKey.audioVolumeMute,
  57377: PhysicalKeyboardKey.launchApp2,
  57378: PhysicalKeyboardKey.mediaPlayPause,
  57380: PhysicalKeyboardKey.mediaStop,
  57388: PhysicalKeyboardKey.eject,
  57390: PhysicalKeyboardKey.audioVolumeDown,
  57392: PhysicalKeyboardKey.audioVolumeUp,
  57394: PhysicalKeyboardKey.browserHome,
  57397: PhysicalKeyboardKey.numpadDivide,
  57399: PhysicalKeyboardKey.printScreen,
  57400: PhysicalKeyboardKey.altRight,
  57403: PhysicalKeyboardKey.help,
  57413: PhysicalKeyboardKey.numLock,
  57415: PhysicalKeyboardKey.home,
  57416: PhysicalKeyboardKey.arrowUp,
  57417: PhysicalKeyboardKey.pageUp,
  57419: PhysicalKeyboardKey.arrowLeft,
  57421: PhysicalKeyboardKey.arrowRight,
  57423: PhysicalKeyboardKey.end,
  57424: PhysicalKeyboardKey.arrowDown,
  57425: PhysicalKeyboardKey.pageDown,
  57426: PhysicalKeyboardKey.insert,
  57427: PhysicalKeyboardKey.delete,
  57435: PhysicalKeyboardKey.metaLeft,
  57436: PhysicalKeyboardKey.metaRight,
  57437: PhysicalKeyboardKey.contextMenu,
  57438: PhysicalKeyboardKey.power,
  57439: PhysicalKeyboardKey.sleep,
  57443: PhysicalKeyboardKey.wakeUp,
  57445: PhysicalKeyboardKey.browserSearch,
  57446: PhysicalKeyboardKey.browserFavorites,
  57447: PhysicalKeyboardKey.browserRefresh,
  57448: PhysicalKeyboardKey.browserStop,
  57449: PhysicalKeyboardKey.browserForward,
  57450: PhysicalKeyboardKey.browserBack,
  57451: PhysicalKeyboardKey.launchApp1,
  57452: PhysicalKeyboardKey.launchMail,
  57453: PhysicalKeyboardKey.mediaSelect,
};

/// A map of Windows KeyboardEvent codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const Map<int, LogicalKeyboardKey> kWindowsNumPadMap = <int, LogicalKeyboardKey>{
  96: LogicalKeyboardKey.numpad0,
  97: LogicalKeyboardKey.numpad1,
  98: LogicalKeyboardKey.numpad2,
  99: LogicalKeyboardKey.numpad3,
  100: LogicalKeyboardKey.numpad4,
  101: LogicalKeyboardKey.numpad5,
  102: LogicalKeyboardKey.numpad6,
  103: LogicalKeyboardKey.numpad7,
  104: LogicalKeyboardKey.numpad8,
  105: LogicalKeyboardKey.numpad9,
  106: LogicalKeyboardKey.numpadMultiply,
  107: LogicalKeyboardKey.numpadAdd,
  108: LogicalKeyboardKey.numpadComma,
  109: LogicalKeyboardKey.numpadSubtract,
  110: LogicalKeyboardKey.numpadDecimal,
  111: LogicalKeyboardKey.numpadDivide,
  146: LogicalKeyboardKey.numpadEqual,
};
