// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

const int kMaxCodePoint = 0x10ffff;

const int kPrefixDigit0 = 48;
const int kPrefixRadix = 10;

const int kFontIndexDigit0 = 65 + 32; // 'a'..'z'
const int kFontIndexRadix = 26;

const int kRangeSizeDigit0 = 65 + 32; // 'a'..'z'
const int kRangeSizeRadix = 26;

const int kRangeValueDigit0 = 65; // 'A'..'Z'
const int kRangeValueRadix = 26;
