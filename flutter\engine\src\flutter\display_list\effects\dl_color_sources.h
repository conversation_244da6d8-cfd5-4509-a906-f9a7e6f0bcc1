// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_DISPLAY_LIST_EFFECTS_DL_COLOR_SOURCES_H_
#define FLUTTER_DISPLAY_LIST_EFFECTS_DL_COLOR_SOURCES_H_

#include "flutter/display_list/effects/color_sources/dl_conical_gradient_color_source.h"
#include "flutter/display_list/effects/color_sources/dl_image_color_source.h"
#include "flutter/display_list/effects/color_sources/dl_linear_gradient_color_source.h"
#include "flutter/display_list/effects/color_sources/dl_radial_gradient_color_source.h"
#include "flutter/display_list/effects/color_sources/dl_runtime_effect_color_source.h"
#include "flutter/display_list/effects/color_sources/dl_sweep_gradient_color_source.h"

#endif  // FLUTTER_DISPLAY_LIST_EFFECTS_DL_COLOR_SOURCES_H_
