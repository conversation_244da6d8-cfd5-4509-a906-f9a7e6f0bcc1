{"builds": [{"drone_dimensions": ["device_type=none", "os=Linux", "kvm=1", "cores=8"], "gclient_variables": {"use_rbe": true}, "gn": ["--android", "--android-cpu=x64", "--no-lto", "--rbe", "--no-goma", "--target-dir", "ci/android_emulator_debug_x64"], "dependencies": [{"dependency": "goldctl", "version": "git_revision:720a542f6fe4f92922c3b8f0fdcc4d2ac6bb83cd"}], "name": "ci/android_emulator_debug_x64", "description": "Build for debug mode x64 Android emulator tests.", "ninja": {"config": "ci/android_emulator_debug_x64", "targets": ["flutter/impeller/renderer/backend/vulkan:vulkan_android_unittests", "flutter/impeller/renderer/backend/vulkan:vulkan_android_apk_unittests", "flutter/impeller/toolkit/android:unittests", "flutter/shell/platform/android:flutter_shell_native_unittests"]}, "tests": [{"language": "python3", "name": "Android Unit Tests", "test_dependencies": [{"dependency": "android_virtual_device", "version": "android_35_google_apis_x64.textpb"}, {"dependency": "avd_cipd_version", "version": "build_id:8733065022087935185"}], "contexts": ["android_virtual_device"], "script": "flutter/testing/run_tests.py", "parameters": ["--android-variant", "ci/android_emulator_debug_x64", "--type", "android"]}, {"language": "dart", "name": "skia_gold_client/e2e_test", "script": "flutter/testing/skia_gold_client/tool/e2e_test.dart", "max_attempts": 1}]}, {"drone_dimensions": ["device_type=none", "os=Linux", "kvm=1", "cores=8"], "gclient_variables": {"use_rbe": true}, "gn": ["--android", "--android-cpu=x86", "--no-lto", "--rbe", "--no-goma", "--target-dir", "ci/android_emulator_debug_x86"], "dependencies": [{"dependency": "goldctl", "version": "git_revision:720a542f6fe4f92922c3b8f0fdcc4d2ac6bb83cd"}], "name": "ci/android_emulator_debug_x86", "description": "Build for debug mode x86 Android emulator tests.", "ninja": {"config": "ci/android_emulator_debug_x86", "targets": ["flutter/impeller/renderer/backend/vulkan:vulkan_android_unittests", "flutter/impeller/renderer/backend/vulkan:vulkan_android_apk_unittests", "flutter/impeller/toolkit/android:unittests", "flutter/shell/platform/android:flutter_shell_native_unittests"]}, "tests": [{"language": "python3", "name": "Android Unit Tests (API 28)", "test_dependencies": [{"dependency": "android_virtual_device", "version": "android_28_google_apis_x86.textpb"}, {"dependency": "avd_cipd_version", "version": "build_id:8733065022087935185"}], "contexts": ["android_virtual_device"], "script": "flutter/testing/run_tests.py", "parameters": ["--android-variant", "ci/android_emulator_debug_x86", "--type", "android"]}]}]}