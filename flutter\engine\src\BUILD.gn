# Copyright 2014 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

group("default") {
  testonly = true
  if (target_os == "wasm") {
    deps = [ "//flutter/wasm" ]
  } else {
    deps = [ "//flutter" ]
  }
}

group("dist") {
  testonly = true

  if (target_os == "wasm") {
    deps = [ "//flutter/wasm" ]
  } else {
    deps = [ "//flutter:dist" ]
  }
}
