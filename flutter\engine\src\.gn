# This file is used by the experimental meta-buildsystem in src/tools/gn to
# find the root of the source tree and to set startup options.

# Use vpython3 from depot_tools for exec_script() calls.
# See `gn help dotfile` for details.
script_executable = "vpython3"

# The location of the build configuration file.
buildconfig = "//build/config/BUILDCONFIG.gn"

# The secondary source root is a parallel directory tree where
# GN build files are placed when they can not be placed directly
# in the source tree, e.g. for third party source trees.
secondary_source = "//flutter/build/secondary/"

# The set of targets known to pass 'gn check'. When all targets pass, remove
# this.
check_targets = [
  "//flutter/common/*",
  "//flutter/display_list/*",
  "//flutter/flow/*",
  "//flutter/fml/*",
  "//flutter/lib/*",
  "//flutter/impeller/*",
  "//flutter/runtime/*",
  "//flutter/shell/*",
]
