// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "flutter/shell/platform/windows/testing/mock_window_binding_handler.h"

namespace flutter {
namespace testing {

MockWindowBindingHandler::MockWindowBindingHandler() : WindowBindingHandler(){};

MockWindowBindingHandler::~MockWindowBindingHandler() = default;

}  // namespace testing
}  // namespace flutter
