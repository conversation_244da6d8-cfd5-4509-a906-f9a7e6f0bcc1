{"logs": [{"outputFile": "com.example.ecocura_flutter.app-mergeDebugResources-47:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f660676bf4ab7e115492941cf8444d98\\transformed\\browser-1.4.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,157,260,374", "endColumns": "101,102,113,100", "endOffsets": "152,255,369,470"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6085,6285,6388,6502", "endColumns": "101,102,113,100", "endOffsets": "6182,6383,6497,6598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3848899b7e93201e983882e2ba4294f0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2798,2907", "endColumns": "108,121", "endOffsets": "2902,3024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7976d4e64729cb9c47971e21b0850b04\\transformed\\jetified-play-services-base-18.1.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,572,678,827,950,1058,1155,1326,1433,1593,1717,1874,2025,2089,2152", "endColumns": "101,155,120,105,148,122,107,96,170,106,159,123,156,150,63,62,81", "endOffsets": "294,450,571,677,826,949,1057,1154,1325,1432,1592,1716,1873,2024,2088,2151,2233"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3760,3866,4026,4151,4261,4414,4541,4653,4897,5072,5183,5347,5475,5636,5791,5859,5926", "endColumns": "105,159,124,109,152,126,111,100,174,110,163,127,160,154,67,66,85", "endOffsets": "3861,4021,4146,4256,4409,4536,4648,4749,5067,5178,5342,5470,5631,5786,5854,5921,6007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,809,886,977,1069,1164,1258,1353,1446,1541,1638,1729,1820,1903,2007,2119,2218,2324,2435,2537,2700,2798", "endColumns": "106,101,108,85,104,116,77,76,90,91,94,93,94,92,94,96,90,90,82,103,111,98,105,110,101,162,97,81", "endOffsets": "207,309,418,504,609,726,804,881,972,1064,1159,1253,1348,1441,1536,1633,1724,1815,1898,2002,2114,2213,2319,2430,2532,2695,2793,2875"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,809,886,977,1069,1164,1258,1353,1446,1541,1638,1729,1820,1903,2007,2119,2218,2324,2435,2537,2700,6828", "endColumns": "106,101,108,85,104,116,77,76,90,91,94,93,94,92,94,96,90,90,82,103,111,98,105,110,101,162,97,81", "endOffsets": "207,309,418,504,609,726,804,881,972,1064,1159,1253,1348,1441,1536,1633,1724,1815,1898,2002,2114,2213,2319,2430,2532,2695,2793,6905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4754", "endColumns": "142", "endOffsets": "4892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,276,354,501,670,754", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "173,271,349,496,665,749,830"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6012,6187,6603,6681,7011,7180,7264", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "6080,6280,6676,6823,7175,7259,7340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3029,3127,3229,3330,3429,3534,3641,6910", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3122,3224,3325,3424,3529,3636,3755,7006"}}]}]}