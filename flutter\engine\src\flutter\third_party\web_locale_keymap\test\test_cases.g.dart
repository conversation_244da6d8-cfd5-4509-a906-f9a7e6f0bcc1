//---------------------------------------------------------------------------------------------
//  Copyright (c) 2022 Google LLC
//  Licensed under the MIT License. See License.txt in the project root for license information.
//--------------------------------------------------------------------------------------------*/

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
//
// This file is auto generated by flutter/engine:flutter/tools/gen_web_keyboard_keymap based on
// https://github.com/microsoft/vscode/tree/@@@COMMIT_ID@@@/src/vs/workbench/services/keybinding/browser/keyboardLayouts
//
// Edit the following files instead:
//
//  - Script: lib/main.dart
//  - Templates: data/*.tmpl
//
// See flutter/engine:flutter/tools/gen_web_keyboard_keymap/README.md for more information.

import 'package:test/test.dart';
import 'package:web_locale_keymap/web_locale_keymap.dart';
import 'testing.dart';

void testWin(LocaleKeymap mapping) {
  group('cz', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'{', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'&', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'Đ', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'[', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r']', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'ł', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'Ł', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'}', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'\', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'đ', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'@', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'|', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'#', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'z', r'Z', r'', r''], 'z');
    verifyEntry(mapping, 'KeyZ', <String>[r'y', r'Y', r'', r''], 'y');
  });

  group('de', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'@', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'z', r'Z', r'', r''], 'z');
    verifyEntry(mapping, 'KeyZ', <String>[r'y', r'Y', r'', r''], 'y');
  });

  group('de-swiss', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'z', r'Z', r'', r''], 'z');
    verifyEntry(mapping, 'KeyZ', <String>[r'y', r'Y', r'', r''], 'y');
  });

  group('dk', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('en', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('en-belgian', () {
    verifyEntry(mapping, 'KeyA', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'z', r'Z', r'', r''], 'z');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'Semicolon', <String>[r'm', r'M', r'', r''], 'm');
  });

  group('en-in', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'ā', r'Ā'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'ḍ', r'Ḍ'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'ē', r'Ē'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'ṅ', r'Ṅ'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'ḥ', r'Ḥ'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'ī', r'Ī'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'l̥', r'L̥'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'ṁ', r'Ṁ'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'ṇ', r'Ṇ'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ō', r'Ō'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'æ', r'Æ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'r̥', r'R̥'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ś', r'Ś'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'ṭ', r'Ṭ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'ū', r'Ū'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'ṣ', r'Ṣ'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'ñ', r'Ñ'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('en-intl', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'á', r'Á'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'©', r'¢'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'ð', r'Ð'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'é', r'É'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'í', r'Í'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'ø', r'Ø'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'ñ', r'Ñ'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ó', r'Ó'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'ö', r'Ö'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'ä', r'Ä'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'§'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'þ', r'Þ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'ú', r'Ú'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'å', r'Å'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'ü', r'Ü'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'æ', r'Æ'], 'z');
  });

  group('en-uk', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'á', r'Á'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'é', r'É'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'í', r'Í'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ó', r'Ó'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'ú', r'Ú'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('es', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('es-latin', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'@', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('fr', () {
    verifyEntry(mapping, 'KeyA', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'z', r'Z', r'', r''], 'z');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'Semicolon', <String>[r'm', r'M', r'', r''], 'm');
  });

  group('hu', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'ä', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'{', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'&', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'Đ', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'Ä', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'[', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r']', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Í', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'í', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'ł', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'Ł', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'<', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'}', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'\', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'đ', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'€', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'@', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'|', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'#', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'z', r'Z', r'', r''], 'z');
    verifyEntry(mapping, 'KeyZ', <String>[r'y', r'Y', r'>', r''], 'y');
  });

  group('it', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('no', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('pl', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'ą', r'Ą'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ć', r'Ć'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'ę', r'Ę'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'ł', r'Ł'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'ń', r'Ń'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ó', r'Ó'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ś', r'Ś'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'€', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'ź', r'Ź'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'ż', r'Ż'], 'z');
  });

  group('pt', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('pt-br', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'₢', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'°', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'/', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'?', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('ru', () {
    verifyEntry(mapping, 'KeyA', <String>[r'ф', r'Ф', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'и', r'И', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'с', r'С', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'в', r'В', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'у', r'У', r'', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'а', r'А', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'п', r'П', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'р', r'Р', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'ш', r'Ш', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'о', r'О', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'л', r'Л', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'д', r'Д', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'ь', r'Ь', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'т', r'Т', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'щ', r'Щ', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'з', r'З', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'й', r'Й', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'к', r'К', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r'ы', r'Ы', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r'е', r'Е', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'г', r'Г', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'м', r'М', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'ц', r'Ц', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'ч', r'Ч', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'н', r'Н', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'я', r'Я', r'', r''], 'z');
  });

  group('sv', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });

  group('thai', () {
    verifyEntry(mapping, 'KeyA', <String>[r'ฟ', r'ฤ', r'', r''], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'ิ', r'ฺ', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'แ', r'ฉ', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'ก', r'ฏ', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'ำ', r'ฎ', r'', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'ด', r'โ', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'เ', r'ฌ', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'้', r'็', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'ร', r'ณ', r'', r''], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'่', r'๋', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'า', r'ษ', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'ส', r'ศ', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'ท', r'?', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'ื', r'์', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'น', r'ฯ', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'ย', r'ญ', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'ๆ', r'๐', r'', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'พ', r'ฑ', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r'ห', r'ฆ', r'', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r'ะ', r'ธ', r'', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'ี', r'๊', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'อ', r'ฮ', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'ไ', r'"', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'ป', r')', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'ั', r'ํ', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'ผ', r'(', r'', r''], 'z');
  });

  group('tr', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'æ', r'Æ'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'', r''], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'', r''], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r''], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r''], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'', r''], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'ı', r'I', r'i', r'İ'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'', r''], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'', r''], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'', r''], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'', r''], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'', r''], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'', r''], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'@', r''], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'', r''], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'₺', r''], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'', r''], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'', r''], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'', r''], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'', r''], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'', r''], 'z');
  });
}

void testLinux(LocaleKeymap mapping) {
  group('de', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'æ', r'Æ'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'“', r'‘'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'¢', r'©'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'ð', r'Ð'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r'€'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'đ', r'ª'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'ŋ', r'Ŋ'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'ħ', r'Ħ'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'→', r'ı'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'̣', r'̇'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'ĸ', r'&'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'ł', r'Ł'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'º'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'”', r'’'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'þ', r'Þ'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'@', r'Ω'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'¶', r'®'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ſ', r'ẞ'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'ŧ', r'Ŧ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'↓', r'↑'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'„', r'‚'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'ł', r'Ł'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'«', r'‹'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'z', r'Z', r'←', r'¥'], 'z');
    verifyEntry(mapping, 'KeyZ', <String>[r'y', r'Y', r'»', r'›'], 'y');
  });

  group('en', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'a', r'A'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'b', r'B'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'c', r'C'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'd', r'D'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'e', r'E'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'f', r'F'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'g', r'G'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'h', r'H'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'i', r'I'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'j', r'J'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'k', r'K'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'l', r'L'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'm', r'M'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'n', r'N'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'o', r'O'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'p', r'P'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'q', r'Q'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'r', r'R'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r's', r'S'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r't', r'T'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'u', r'U'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'v', r'V'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'w', r'W'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'x', r'X'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'y', r'Y'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'z', r'Z'], 'z');
  });

  group('es', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'æ', r'Æ'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'”', r'’'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'¢', r'©'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'ð', r'Ð'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r'¢'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'đ', r'ª'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'ŋ', r'Ŋ'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'ħ', r'Ħ'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'→', r'ı'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'̉', r'̛'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'ĸ', r'&'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'ł', r'Ł'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'º'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'n', r'N'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'þ', r'Þ'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'@', r'Ω'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'¶', r'®'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'§'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'ŧ', r'Ŧ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'↓', r'↑'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'“', r'‘'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'ł', r'Ł'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'»', r'>'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'←', r'¥'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'«', r'<'], 'z');
  });

  group('fr', () {
    verifyEntry(mapping, 'KeyA', <String>[r'q', r'Q', r'@', r'Ω'], 'q');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'”', r'’'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'¢', r'©'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'ð', r'Ð'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r'¢'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'đ', r'ª'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'ŋ', r'Ŋ'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'ħ', r'Ħ'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'→', r'ı'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'̉', r'̛'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'ĸ', r'&'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'ł', r'Ł'], 'l');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'n', r'N'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'þ', r'Þ'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'a', r'A', r'æ', r'Æ'], 'a');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'¶', r'®'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'§'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'ŧ', r'Ŧ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'↓', r'↑'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'“', r'‘'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'z', r'Z', r'«', r'<'], 'z');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'»', r'>'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'←', r'¥'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'w', r'W', r'ł', r'Ł'], 'w');
    verifyEntry(mapping, 'Semicolon', <String>[r'm', r'M', r'µ', r'º'], 'm');
  });

  group('ru', () {
    verifyEntry(mapping, 'KeyA', <String>[r'ф', r'Ф', r'ф', r'Ф'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'и', r'И', r'и', r'И'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'с', r'С', r'с', r'С'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'в', r'В', r'в', r'В'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'у', r'У', r'у', r'У'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'а', r'А', r'а', r'А'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'п', r'П', r'п', r'П'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'р', r'Р', r'р', r'Р'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'ш', r'Ш', r'ш', r'Ш'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'о', r'О', r'о', r'О'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'л', r'Л', r'л', r'Л'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'д', r'Д', r'д', r'Д'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'ь', r'Ь', r'ь', r'Ь'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'т', r'Т', r'т', r'Т'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'щ', r'Щ', r'щ', r'Щ'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'з', r'З', r'з', r'З'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'й', r'Й', r'й', r'Й'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'к', r'К', r'к', r'К'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r'ы', r'Ы', r'ы', r'Ы'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r'е', r'Е', r'е', r'Е'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'г', r'Г', r'г', r'Г'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'м', r'М', r'м', r'М'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'ц', r'Ц', r'ц', r'Ц'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'ч', r'Ч', r'ч', r'Ч'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'н', r'Н', r'н', r'Н'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'я', r'Я', r'я', r'Я'], 'z');
  });
}

void testDarwin(LocaleKeymap mapping) {
  group('de', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'∫', r'‹'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ç', r'Ç'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'™'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r'‰'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'Ï'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'Ì'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'ª', r'Ó'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'⁄', r'Û'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'º', r'ı'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'∆', r'ˆ'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'@', r'ﬂ'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'˘'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'›'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'«', r'»'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'¸'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'‚', r'Í'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'˝'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'Á'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'∑', r'„'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'≈', r'Ù'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'z', r'Z', r'Ω', r'ˇ'], 'z');
    verifyEntry(mapping, 'KeyZ', <String>[r'y', r'Y', r'¥', r'‡'], 'y');
  });

  group('dvorak', () {
    verifyEntry(mapping, 'Comma', <String>[r'w', r'W', r'∑', r'„'], 'w');
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'x', r'X', r'≈', r'˛'], 'x');
    verifyEntry(mapping, 'KeyC', <String>[r'j', r'J', r'∆', r'Ô'], 'j');
    verifyEntry(mapping, 'KeyD', <String>[r'e', r'E', r'Dead', r'´'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'u', r'U', r'Dead', r'¨'], 'u');
    verifyEntry(mapping, 'KeyG', <String>[r'i', r'I', r'Dead', r'ˆ'], 'i');
    verifyEntry(mapping, 'KeyH', <String>[r'd', r'D', r'∂', r'Î'], 'd');
    verifyEntry(mapping, 'KeyI', <String>[r'c', r'C', r'ç', r'Ç'], 'c');
    verifyEntry(mapping, 'KeyJ', <String>[r'h', r'H', r'˙', r'Ó'], 'h');
    verifyEntry(mapping, 'KeyK', <String>[r't', r'T', r'†', r'ˇ'], 't');
    verifyEntry(mapping, 'KeyL', <String>[r'n', r'N', r'Dead', r'˜'], 'n');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'Â'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'b', r'B', r'∫', r'ı'], 'b');
    verifyEntry(mapping, 'KeyO', <String>[r'r', r'R', r'®', r'‰'], 'r');
    verifyEntry(mapping, 'KeyP', <String>[r'l', r'L', r'¬', r'Ò'], 'l');
    verifyEntry(mapping, 'KeyR', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyS', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyT', <String>[r'y', r'Y', r'¥', r'Á'], 'y');
    verifyEntry(mapping, 'KeyU', <String>[r'g', r'G', r'©', r'˝'], 'g');
    verifyEntry(mapping, 'KeyV', <String>[r'k', r'K', r'˚', r''], 'k');
    verifyEntry(mapping, 'KeyX', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyY', <String>[r'f', r'F', r'ƒ', r'Ï'], 'f');
    verifyEntry(mapping, 'Period', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'Semicolon', <String>[r's', r'S', r'ß', r'Í'], 's');
    verifyEntry(mapping, 'Slash', <String>[r'z', r'Z', r'Ω', r'¸'], 'z');
  });

  group('en', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'∫', r'ı'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ç', r'Ç'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'Î'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'Dead', r'´'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'Ï'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'˝'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'˙', r'Ó'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Dead', r'ˆ'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'∆', r'Ô'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'˚', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'¬', r'Ò'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'Â'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'˜'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'‰'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'Í'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'ˇ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'¨'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'∑', r'„'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'≈', r'˛'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'¥', r'Á'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'Ω', r'¸'], 'z');
  });

  group('en-ext', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'Dead', r'̄'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'Dead', r'̆'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'Dead', r'̧'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'ð', r'Ð'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'Dead', r'́'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'Dead'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'Dead', r'̱'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Dead', r'̛'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'Dead', r'̋'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'Dead', r'̊'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'Dead', r'̵'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'Dead', r'̨'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'̃'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'Dead', r'̦'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'‰'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'þ', r'Þ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'̈'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'Dead', r'̌'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'Dead', r'̇'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'Dead', r'̣'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'¥', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'Dead', r'̉'], 'z');
  });

  group('en-intl', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'∫', r'ı'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ç', r'Ç'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'Î'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'Dead', r'´'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'Ï'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'˝'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'˙', r'Ó'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Dead', r'ˆ'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'∆', r'Ô'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'˚', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'¬', r'Ò'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'Â'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'˜'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'‰'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'Í'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'ˇ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'¨'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'∑', r'„'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'≈', r'˛'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'¥', r'Á'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'Ω', r'¸'], 'z');
  });

  group('en-uk', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'∫', r'ı'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ç', r'Ç'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'Î'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'Dead', r'‰'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'Ï'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'Ì'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'˙', r'Ó'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Dead', r'È'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'∆', r'Ô'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'˚', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'¬', r'Ò'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'˜'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'ˆ'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'Â'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'Í'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'Ê'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'Ë'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'∑', r'„'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'≈', r'Ù'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'¥', r'Á'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'Ω', r'Û'], 'z');
  });

  group('es', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'ß', r''], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'©', r' '], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'∆'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r'€'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'ﬁ'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'', r'ﬂ'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'™', r' '], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r' ', r' '], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'¶', r'¯'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'§', r'ˇ'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r' ', r'˘'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'˚'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r' ', r'˙'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r' '], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'∫', r' '], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'‡'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r' ', r' '], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'æ', r'Æ'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'∑', r'›'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'¥', r' '], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'Ω', r'‹'], 'z');
  });

  group('fr', () {
    verifyEntry(mapping, 'KeyA', <String>[r'q', r'Q', r'‡', r'Ω'], 'q');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'ß', r'∫'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'©', r'¢'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'∆'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'ê', r'Ê'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'·'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'ﬁ', r'ﬂ'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'Ì', r'Î'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'î', r'ï'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'Ï', r'Í'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'È', r'Ë'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'¬', r'|'], 'l');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'ı'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'œ', r'Œ'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'a', r'A', r'æ', r'Æ'], 'a');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'‚'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'Ò', r'∑'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'™'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'º', r'ª'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'◊', r'√'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'z', r'Z', r'Â', r'Å'], 'z');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'≈', r'⁄'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'Ú', r'Ÿ'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'w', r'W', r'‹', r'›'], 'w');
    verifyEntry(mapping, 'Semicolon', <String>[r'm', r'M', r'µ', r'Ó'], 'm');
  });

  group('it', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'∫', r'Í'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'©', r'Á'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'˘'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'€', r'È'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'˙'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'∞', r'˚'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'∆', r'¸'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'œ', r'Œ'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'ª', r'˝'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'º', r'˛'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'¬', r'ˇ'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'Ú'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'Ó'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'„', r'‚'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'Ì'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'¯'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'™', r'Ò'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'Ù'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'É'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'Ω', r'À'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'†', r'‡'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'æ', r'Æ'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'∑', r' '], 'z');
  });

  group('jp', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'∫', r'ı'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ç', r'Ç'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'Î'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'Dead', r'´'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'Ï'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'˝'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'˙', r'Ó'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Dead', r'ˆ'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'∆', r'Ô'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'˚', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'¬', r'Ò'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'Â'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'˜'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'‰'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'Í'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'ˇ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'¨'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'∑', r'„'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'≈', r'˛'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'¥', r'Á'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'Ω', r'¸'], 'z');
  });

  group('jp-roman', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'Dead', r'̄'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'Dead', r'̆'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'Dead', r'̧'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'ð', r'Ð'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'Dead', r'́'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r''], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'Dead'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'Dead', r'̱'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Dead', r'̛'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'Dead', r'̋'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'Dead', r'̊'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'Dead', r'̵'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'Dead', r'̨'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'̃'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'Dead', r'̦'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'‰'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r''], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'þ', r'Þ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'̈'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'Dead', r'̌'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'Dead', r'̇'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'Dead', r'̣'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'¥', r''], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'Dead', r'̉'], 'z');
  });

  group('ko', () {
    verifyEntry(mapping, 'KeyA', <String>[r'ㅁ', r'ㅁ', r'a', r'A'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'ㅠ', r'ㅠ', r'b', r'B'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'ㅊ', r'ㅊ', r'c', r'C'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'ㅇ', r'ㅇ', r'd', r'D'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'ㄷ', r'ㄸ', r'e', r'E'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'ㄹ', r'ㄹ', r'f', r'F'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'ㅎ', r'ㅎ', r'g', r'G'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'ㅗ', r'ㅗ', r'h', r'H'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'ㅑ', r'ㅑ', r'i', r'I'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'ㅓ', r'ㅓ', r'j', r'J'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'ㅏ', r'ㅏ', r'k', r'K'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'ㅣ', r'ㅣ', r'l', r'L'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'ㅡ', r'ㅡ', r'm', r'M'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'ㅜ', r'ㅜ', r'n', r'N'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'ㅐ', r'ㅒ', r'o', r'O'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'ㅔ', r'ㅖ', r'p', r'P'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'ㅂ', r'ㅃ', r'q', r'Q'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'ㄱ', r'ㄲ', r'r', r'R'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r'ㄴ', r'ㄴ', r's', r'S'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r'ㅅ', r'ㅆ', r't', r'T'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'ㅕ', r'ㅕ', r'u', r'U'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'ㅍ', r'ㅍ', r'v', r'V'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'ㅈ', r'ㅉ', r'w', r'W'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'ㅌ', r'ㅌ', r'x', r'X'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'ㅛ', r'ㅛ', r'y', r'Y'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'ㅋ', r'ㅋ', r'z', r'Z'], 'z');
  });

  group('pl', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'ą', r'Ą'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'ļ', r'ű'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ć', r'Ć'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'Ž'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'ę', r'Ę'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ń', r'ž'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'Ū'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'ķ', r'Ó'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Dead', r'ť'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'∆', r'Ô'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'Ż', r'ū'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'ł', r'Ł'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'Ķ', r'ų'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'ń', r'Ń'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ó', r'Ó'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'Ļ', r'ł'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'Ō', r'ő'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'£'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ś', r'Ś'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'ś'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'Ť'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'∑', r'„'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'ź', r'Ź'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'ī', r'Á'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'ż', r'Ż'], 'z');
  });

  group('pt', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'∫', r'ı'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ç', r'Ç'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'Î'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'Dead', r'´'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'Ï'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'˝'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'˙', r'Ó'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Dead', r'ˆ'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'∆', r'Ô'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'˚', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'¬', r'Ò'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'Â'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'˜'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'‰'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'Í'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'ˇ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'¨'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'∑', r'„'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'≈', r'˛'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'¥', r'Á'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'Ω', r'¸'], 'z');
  });

  group('ru', () {
    verifyEntry(mapping, 'KeyA', <String>[r'ф', r'Ф', r'ƒ', r'ƒ'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'и', r'И', r'и', r'И'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'с', r'С', r'≠', r'≠'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'в', r'В', r'ћ', r'Ћ'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'у', r'У', r'ќ', r'Ќ'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'а', r'А', r'÷', r'÷'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'п', r'П', r'©', r'©'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'р', r'Р', r'₽', r'₽'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'ш', r'Ш', r'ѕ', r'Ѕ'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'о', r'О', r'°', r'•'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'л', r'Л', r'љ', r'Љ'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'д', r'Д', r'∆', r'∆'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'ь', r'Ь', r'~', r'~'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'т', r'Т', r'™', r'™'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'щ', r'Щ', r'ў', r'Ў'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'з', r'З', r'‘', r'’'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'й', r'Й', r'ј', r'Ј'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'к', r'К', r'®', r'®'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r'ы', r'Ы', r'ы', r'Ы'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r'е', r'Е', r'†', r'†'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'г', r'Г', r'ѓ', r'Ѓ'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'м', r'М', r'µ', r'µ'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'ц', r'Ц', r'џ', r'Џ'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'ч', r'Ч', r'≈', r'≈'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'н', r'Н', r'њ', r'Њ'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'я', r'Я', r'ђ', r'Ђ'], 'z');
  });

  group('sv', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'', r'◊'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'›', r'»'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ç', r'Ç'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'∆'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'é', r'É'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'∫'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'¸', r'¯'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'˛', r'˘'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'ı', r'ˆ'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'√', r'¬'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'ª', r'º'], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'ﬁ', r'ﬂ'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'’', r'”'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'‘', r'“'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'œ', r'Œ'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'•', r'°'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'√'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'∑'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'‡'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'ü', r'Ü'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'‹', r'«'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'Ω', r'˝'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'≈', r'ˇ'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'µ', r'˜'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'÷', r'⁄'], 'z');
  });

  group('zh-hans', () {
    verifyEntry(mapping, 'KeyA', <String>[r'a', r'A', r'å', r'Å'], 'a');
    verifyEntry(mapping, 'KeyB', <String>[r'b', r'B', r'∫', r'ı'], 'b');
    verifyEntry(mapping, 'KeyC', <String>[r'c', r'C', r'ç', r'Ç'], 'c');
    verifyEntry(mapping, 'KeyD', <String>[r'd', r'D', r'∂', r'Î'], 'd');
    verifyEntry(mapping, 'KeyE', <String>[r'e', r'E', r'Dead', r'´'], 'e');
    verifyEntry(mapping, 'KeyF', <String>[r'f', r'F', r'ƒ', r'Ï'], 'f');
    verifyEntry(mapping, 'KeyG', <String>[r'g', r'G', r'©', r'˝'], 'g');
    verifyEntry(mapping, 'KeyH', <String>[r'h', r'H', r'˙', r'Ó'], 'h');
    verifyEntry(mapping, 'KeyI', <String>[r'i', r'I', r'Dead', r'ˆ'], 'i');
    verifyEntry(mapping, 'KeyJ', <String>[r'j', r'J', r'∆', r'Ô'], 'j');
    verifyEntry(mapping, 'KeyK', <String>[r'k', r'K', r'˚', r''], 'k');
    verifyEntry(mapping, 'KeyL', <String>[r'l', r'L', r'¬', r'Ò'], 'l');
    verifyEntry(mapping, 'KeyM', <String>[r'm', r'M', r'µ', r'Â'], 'm');
    verifyEntry(mapping, 'KeyN', <String>[r'n', r'N', r'Dead', r'˜'], 'n');
    verifyEntry(mapping, 'KeyO', <String>[r'o', r'O', r'ø', r'Ø'], 'o');
    verifyEntry(mapping, 'KeyP', <String>[r'p', r'P', r'π', r'∏'], 'p');
    verifyEntry(mapping, 'KeyQ', <String>[r'q', r'Q', r'œ', r'Œ'], 'q');
    verifyEntry(mapping, 'KeyR', <String>[r'r', r'R', r'®', r'‰'], 'r');
    verifyEntry(mapping, 'KeyS', <String>[r's', r'S', r'ß', r'Í'], 's');
    verifyEntry(mapping, 'KeyT', <String>[r't', r'T', r'†', r'ˇ'], 't');
    verifyEntry(mapping, 'KeyU', <String>[r'u', r'U', r'Dead', r'¨'], 'u');
    verifyEntry(mapping, 'KeyV', <String>[r'v', r'V', r'√', r'◊'], 'v');
    verifyEntry(mapping, 'KeyW', <String>[r'w', r'W', r'∑', r'„'], 'w');
    verifyEntry(mapping, 'KeyX', <String>[r'x', r'X', r'≈', r'˛'], 'x');
    verifyEntry(mapping, 'KeyY', <String>[r'y', r'Y', r'¥', r'Á'], 'y');
    verifyEntry(mapping, 'KeyZ', <String>[r'z', r'Z', r'Ω', r'¸'], 'z');
  });
}
