// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
{
    include: [
        "sys/testing/gtest_runner.shard.cml",
        "sys/component/realm_builder_absolute.shard.cml",

        // This test needs both the vulkan facet and the hermetic-tier-2 facet,
        // so we are forced to make it a system test.
        "sys/testing/system-test.shard.cml",

        // TODO(https://fxbug.dev/404543928): Remove this shard usage when possible
        // and replace with explicit routes.
        "syslog/client.shard.cml",
        "inspect/client.shard.cml",
    ],
    program: {
        binary: "bin/app",
    },
    use: [
      {
        protocol: [
          "fuchsia.ui.test.input.KeyboardInputListener",
        ]
      }
    ],
    offer: [
        {
            protocol: [
                "fuchsia.kernel.RootJobForInspect",
                "fuchsia.kernel.Stats",
                "fuchsia.scheduler.ProfileProvider",
                "fuchsia.sysmem.Allocator",
                "fuchsia.sysmem2.Allocator",
                "fuchsia.tracing.provider.Registry",
                "fuchsia.ui.input.ImeService",
                "fuchsia.vulkan.loader.Loader",
                "fuchsia.ui.test.input.KeyboardInputListener",
                "fuchsia.ui.input3.Keyboard",
                "fuchsia.intl.PropertyProvider",
                "fuchsia.posix.socket.Provider",
                "fuchsia.ui.pointerinjector.Registry",
                "fuchsia.fonts.Provider",
                "fuchsia.feedback.CrashReportingProductRegister",
                "fuchsia.settings.Keyboard",
                "fuchsia.accessibility.semantics.SemanticsManager",
            ],
            from: "parent",
            to: "#realm_builder",
        },
        {
            directory: "pkg",
            subdir: "config",
            as: "config-data",
            from: "framework",
            to: "#realm_builder",
        },
        // See common.shard.cml.
        {
            directory: "tzdata-icu",
            from: "framework",
            to: "#realm_builder",
        },
    ],
    // TODO(https://fxbug.dev/114584): Figure out how to bring these in as deps (if possible oot).
    facets: {
        "fuchsia.test": {
            "deprecated-allowed-packages": [
                "cursor",
                "flatland-scene-manager-test-ui-stack",
                "test-ui-stack",
                "oot_flutter_aot_runner",
                "oot_flutter_jit_runner",
                "oot_flutter_jit_product_runner",
                "oot_flutter_aot_product_runner",
                "test_manager",
                "text-input-view",
            ],
        },
    },
}
