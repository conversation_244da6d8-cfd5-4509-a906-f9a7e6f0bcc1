// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_IMPELLER_COMPILER_CONSTANTS_H_
#define FLUTTER_IMPELLER_COMPILER_CONSTANTS_H_

namespace impeller {
namespace compiler {

constexpr char kExternalTexturePrefix[] = "SAMPLER_EXTERNAL_OES_";

}  // namespace compiler
}  // namespace impeller

#endif  // FLUTTER_IMPELLER_COMPILER_CONSTANTS_H_
