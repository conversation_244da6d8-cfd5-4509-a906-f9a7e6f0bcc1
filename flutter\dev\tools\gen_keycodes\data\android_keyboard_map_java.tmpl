package io.flutter.embedding.android;

// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by flutter/flutter@dev/tools/gen_keycodes/bin/gen_keycodes.dart and
// should not be edited directly.
//
// Edit the template dev/tools/gen_keycodes/data/android_keyboard_map_java.tmpl instead.
// See dev/tools/gen_keycodes/README.md for more information.

import android.view.KeyEvent;
import java.util.HashMap;

/** Static information used by {@link KeyEmbedderResponder}. */
public class KeyboardMap {
  /** A physicalKey-logicalKey pair used to define mappings. */
  public static class KeyPair {
    public KeyPair(long physicalKey, long logicalKey) {
      this.physicalKey = physicalKey;
      this.logicalKey = logicalKey;
    }

    public long physicalKey;
    public long logicalKey;
  }

  /**
   * An immutable configuration item that defines how to synchronize pressing modifiers (such as
   * Shift or Ctrl), so that the {@link KeyEmbedderResponder} must synthesize events until the
   * combined pressing state of {@link keys} matches the true meta state masked by {@link mask}.
   */
  public static class PressingGoal {
    public PressingGoal(int mask, KeyPair[] keys) {
      this.mask = mask;
      this.keys = keys;
    }

    public final int mask;
    public final KeyPair[] keys;
  }

  /**
   * A configuration item that defines how to synchronize toggling modifiers (such as CapsLock), so
   * that the {@link KeyEmbedderResponder} must synthesize events until the enabling state of the
   * key matches the true meta state masked by {@link #mask}.
   *
   * <p>The objects of this class are mutable. The {@link #enabled} field will be used to store the
   * current enabling state.
   */
  public static class TogglingGoal {
    public TogglingGoal(int mask, long physicalKey, long logicalKey) {
      this.mask = mask;
      this.physicalKey = physicalKey;
      this.logicalKey = logicalKey;
    }

    public final int mask;
    public final long physicalKey;
    public final long logicalKey;
    /**
     * Used by {@link KeyEmbedderResponder} to store the current enabling state of this modifier.
     *
     * <p>Initialized as false.
     */
    public boolean enabled = false;
  }

  /** Maps from Android scan codes {@link KeyEvent#getScanCode()} to Flutter physical keys. */
  public static final HashMap<Long, Long> scanCodeToPhysical =
      new HashMap<Long, Long>() {
        private static final long serialVersionUID = 1L;

        {
@@@ANDROID_SCAN_CODE_MAP@@@
        }
      };

  /** Maps from Android key codes {@link KeyEvent#getKeyCode()} to Flutter logical keys. */
  public static final HashMap<Long, Long> keyCodeToLogical =
      new HashMap<Long, Long>() {
        private static final long serialVersionUID = 1L;

        {
@@@ANDROID_KEY_CODE_MAP@@@
        }
      };

  public static final PressingGoal[] pressingGoals =
      new PressingGoal[] {
@@@PRESSING_GOALS@@@
      };

  /**
   * A list of toggling modifiers that must be synchronized on each key event.
   *
   * <p>The list is not a static variable but constructed by a function, because {@link
   * TogglingGoal} is mutable.
   */
  public static TogglingGoal[] getTogglingGoals() {
    return new TogglingGoal[] {
@@@TOGGLING_GOALS@@@
    };
  }

@@@MASK_CONSTANTS@@@
}
