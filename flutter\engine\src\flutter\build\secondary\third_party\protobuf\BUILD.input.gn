# Copyright 2017 The Fuchsia Authors. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#
#    * Redistributions of source code must retain the above copyright
# notice, this list of conditions and the following disclaimer.
#    * Redistributions in binary form must reproduce the above
# copyright notice, this list of conditions and the following disclaimer
# in the documentation and/or other materials provided with the
# distribution.
#    * Neither the name of Google Inc. nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
# "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
# LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
# A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
# OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
# SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
# LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
# DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
# THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

import("//build/test.gni")
import("//third_party/protobuf/proto_library.gni")

config("protobuf_config") {
  include_dirs = [ "src" ]
  defines = [
    "GOOGLE_PROTOBUF_NO_RTTI",
    "HAVE_PTHREAD",
  ]
  cflags = []
  if (is_clang) {
    cflags += [
      # Needed to support PROTOBUF_INTERNAL_CHECK_CLASS_SIZE in descriptor.h
      "-Wno-c++98-compat-extra-semi",

      # There are implicit conversions in parse_context.h
      "-Wno-shorten-64-to-32",
    ]
  }
}

config("protobuf_warnings") {
  cflags = []

  if (is_clang) {
    # These are all needed as of https://github.com/protocolbuffers/protobuf/releases/tag/v3.21.12
    cflags += [
      "-Wno-deprecated-pragma",
      "-Wno-enum-enum-conversion",
      "-Wno-extra-semi",
      "-Wno-float-conversion",
      "-Wno-implicit-float-conversion",
      "-Wno-implicit-int-conversion",
      "-Wno-implicit-int-float-conversion",
      "-Wno-invalid-noreturn",
      "-Wno-missing-field-initializers",
      "-Wno-sign-compare",
      "-Wno-unused-function",
      "-Wno-unused-private-field",
    ]
  }
}

# This config should be applied to targets using generated code from the proto
# compiler. It sets up the include directories properly.
config("using_proto") {
  include_dirs = [
    "src",
    "$root_gen_dir",
  ]
}

static_library("protobuf_lite") {
  sources = [
    "src/google/protobuf/any_lite.cc",
    "src/google/protobuf/arena.cc",
    "src/google/protobuf/arenastring.cc",
    "src/google/protobuf/arenaz_sampler.cc",
    "src/google/protobuf/extension_set.cc",
    "src/google/protobuf/generated_enum_util.cc",
    "src/google/protobuf/generated_message_tctable_lite.cc",
    "src/google/protobuf/generated_message_util.cc",
    "src/google/protobuf/implicit_weak_message.cc",
    "src/google/protobuf/inlined_string_field.cc",
    "src/google/protobuf/io/coded_stream.cc",
    "src/google/protobuf/io/io_win32.cc",
    "src/google/protobuf/io/strtod.cc",
    "src/google/protobuf/io/zero_copy_stream.cc",
    "src/google/protobuf/io/zero_copy_stream_impl.cc",
    "src/google/protobuf/io/zero_copy_stream_impl_lite.cc",
    "src/google/protobuf/map.cc",
    "src/google/protobuf/message_lite.cc",
    "src/google/protobuf/parse_context.cc",
    "src/google/protobuf/repeated_field.cc",
    "src/google/protobuf/repeated_ptr_field.cc",
    "src/google/protobuf/stubs/bytestream.cc",
    "src/google/protobuf/stubs/common.cc",
    "src/google/protobuf/stubs/int128.cc",
    "src/google/protobuf/stubs/status.cc",
    "src/google/protobuf/stubs/statusor.cc",
    "src/google/protobuf/stubs/stringpiece.cc",
    "src/google/protobuf/stubs/stringprintf.cc",
    "src/google/protobuf/stubs/structurally_valid.cc",
    "src/google/protobuf/stubs/strutil.cc",
    "src/google/protobuf/stubs/time.cc",
    "src/google/protobuf/wire_format_lite.cc",
  ]

  # git ls-files -- ':!*/compiler/*' ':!*/testing/*' ':!*/util/*' 'src/google/protobuf/*.h' | sed 's/^/"/' | sed 's/$/",/'
  public = [ PROTOBUF_LITE_PUBLIC ]
  configs += [ ":protobuf_warnings" ]
  public_configs = [ ":protobuf_config" ]
}

# This is the full, heavy protobuf lib that's needed for c++ .protos that don't
# specify the LITE_RUNTIME option. The protocol compiler itself (protoc) falls
# into that category.
static_library("protobuf_full") {
  sources = [
    "src/google/protobuf/any.cc",
    "src/google/protobuf/any.pb.cc",
    "src/google/protobuf/api.pb.cc",
    "src/google/protobuf/compiler/importer.cc",
    "src/google/protobuf/compiler/parser.cc",
    "src/google/protobuf/descriptor.cc",
    "src/google/protobuf/descriptor.pb.cc",
    "src/google/protobuf/descriptor_database.cc",
    "src/google/protobuf/duration.pb.cc",
    "src/google/protobuf/dynamic_message.cc",
    "src/google/protobuf/empty.pb.cc",
    "src/google/protobuf/extension_set_heavy.cc",
    "src/google/protobuf/field_mask.pb.cc",
    "src/google/protobuf/generated_message_bases.cc",
    "src/google/protobuf/generated_message_reflection.cc",
    "src/google/protobuf/generated_message_tctable_full.cc",

    # gzip_stream.cc pulls in zlib, but it's not actually used by protoc, just
    # by test code, so instead of compiling zlib for the host, let's just
    # exclude this.
    # "src/google/protobuf/io/gzip_stream.cc",

    "src/google/protobuf/io/printer.cc",
    "src/google/protobuf/io/tokenizer.cc",
    "src/google/protobuf/map_field.cc",
    "src/google/protobuf/message.cc",
    "src/google/protobuf/reflection_ops.cc",
    "src/google/protobuf/service.cc",
    "src/google/protobuf/source_context.pb.cc",
    "src/google/protobuf/struct.pb.cc",
    "src/google/protobuf/stubs/substitute.cc",
    "src/google/protobuf/text_format.cc",
    "src/google/protobuf/timestamp.pb.cc",
    "src/google/protobuf/type.pb.cc",
    "src/google/protobuf/unknown_field_set.cc",
    "src/google/protobuf/util/delimited_message_util.cc",
    "src/google/protobuf/util/field_comparator.cc",
    "src/google/protobuf/util/field_mask_util.cc",
    "src/google/protobuf/util/internal/datapiece.cc",
    "src/google/protobuf/util/internal/default_value_objectwriter.cc",
    "src/google/protobuf/util/internal/error_listener.cc",
    "src/google/protobuf/util/internal/field_mask_utility.cc",
    "src/google/protobuf/util/internal/json_escaping.cc",
    "src/google/protobuf/util/internal/json_objectwriter.cc",
    "src/google/protobuf/util/internal/json_stream_parser.cc",
    "src/google/protobuf/util/internal/object_writer.cc",
    "src/google/protobuf/util/internal/proto_writer.cc",
    "src/google/protobuf/util/internal/protostream_objectsource.cc",
    "src/google/protobuf/util/internal/protostream_objectwriter.cc",
    "src/google/protobuf/util/internal/type_info.cc",
    "src/google/protobuf/util/internal/utility.cc",
    "src/google/protobuf/util/json_util.cc",
    "src/google/protobuf/util/message_differencer.cc",
    "src/google/protobuf/util/time_util.cc",
    "src/google/protobuf/util/type_resolver_util.cc",
    "src/google/protobuf/wire_format.cc",
    "src/google/protobuf/wrappers.pb.cc",
  ]

  # git ls-files -- ':!*/compiler/*' ':!*/testing/*' 'src/google/protobuf/*.h' | sed 's/^/"/' | sed 's/$/",/'
  public = [ PROTOBUF_FULL_PUBLIC ]
  configs += [ ":protobuf_warnings" ]
  public_configs = [ ":protobuf_config" ]
  deps = [ ":protobuf_lite" ]
}

# Only compile the compiler for the host architecture.
if (current_toolchain == host_toolchain) {
  # protoc compiler is separated into protoc library and executable targets to
  # support protoc plugins that need to link libprotoc, but not the main()
  # itself. See src/google/protobuf/compiler/plugin.h
  #
  # git ls-files -- ':!*/main.cc' ':!*test*' ':!*mock*' 'src/google/protobuf/compiler/*.cc' | sed 's/^/"/' | sed 's/$/",/'
  static_library("protoc_lib") {
    sources = [ PROTOC_LIB_SOURCES ]
    configs += [ ":protobuf_warnings" ]
    public_deps = [ ":protobuf_full" ]
  }

  executable("protoc") {
    sources = [ "src/google/protobuf/compiler/main.cc" ]
    deps = [ ":protoc_lib" ]
  }
}
