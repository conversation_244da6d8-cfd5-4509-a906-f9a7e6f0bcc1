// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
{
    include: [ "syslog/client.shard.cml" ],
    program: {
        data: "data/text-input-view",

        // Always use the jit runner for now.
        // TODO(fxbug.dev/106577): Implement manifest merging build rules for V2 components.
        runner: "flutter_jit_runner",
    },
    capabilities: [
        {
            protocol: [ "fuchsia.ui.app.ViewProvider", "fuchsia.settings.Keyboard" ],
        },
    ],
    use: [
        {
            protocol: [ "fuchsia.ui.test.input.KeyboardInputListener", "fuchsia.settings.Keyboard" ],
        },
    ],
    expose: [
        {
            protocol: [ "fuchsia.ui.app.ViewProvider", "fuchsia.settings.Keyboard" ],
            from: "self",
        },
    ],
}
