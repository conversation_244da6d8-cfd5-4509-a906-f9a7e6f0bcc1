{"version": "6_1_0", "md.comp.navigation-bar.active.focus.icon.color": "onSecondaryContainer", "md.comp.navigation-bar.active.focus.label-text.color": "onSurface", "md.comp.navigation-bar.active.focus.state-layer.color": "onSurface", "md.comp.navigation-bar.active.hover.icon.color": "onSecondaryContainer", "md.comp.navigation-bar.active.hover.label-text.color": "onSurface", "md.comp.navigation-bar.active.hover.state-layer.color": "onSurface", "md.comp.navigation-bar.active.icon.color": "onSecondaryContainer", "md.comp.navigation-bar.active-indicator.color": "secondaryContainer", "md.comp.navigation-bar.active-indicator.height": 32.0, "md.comp.navigation-bar.active-indicator.shape": "md.sys.shape.corner.full", "md.comp.navigation-bar.active-indicator.width": 64.0, "md.comp.navigation-bar.active.label-text.color": "onSurface", "md.comp.navigation-bar.active.pressed.icon.color": "onSecondaryContainer", "md.comp.navigation-bar.active.pressed.label-text.color": "onSurface", "md.comp.navigation-bar.active.pressed.state-layer.color": "onSurface", "md.comp.navigation-bar.container.color": "surfaceContainer", "md.comp.navigation-bar.container.elevation": "md.sys.elevation.level2", "md.comp.navigation-bar.container.height": 80.0, "md.comp.navigation-bar.container.shape": "md.sys.shape.corner.none", "md.comp.navigation-bar.focus.indicator.color": "secondary", "md.comp.navigation-bar.focus.indicator.outline.offset": "md.sys.state.focus-indicator.inner-offset", "md.comp.navigation-bar.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.navigation-bar.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.navigation-bar.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.navigation-bar.icon.size": 24.0, "md.comp.navigation-bar.inactive.focus.icon.color": "onSurface", "md.comp.navigation-bar.inactive.focus.label-text.color": "onSurface", "md.comp.navigation-bar.inactive.focus.state-layer.color": "onSurface", "md.comp.navigation-bar.inactive.hover.icon.color": "onSurface", "md.comp.navigation-bar.inactive.hover.label-text.color": "onSurface", "md.comp.navigation-bar.inactive.hover.state-layer.color": "onSurface", "md.comp.navigation-bar.inactive.icon.color": "onSurfaceVariant", "md.comp.navigation-bar.inactive.label-text.color": "onSurfaceVariant", "md.comp.navigation-bar.inactive.pressed.icon.color": "onSurface", "md.comp.navigation-bar.inactive.pressed.label-text.color": "onSurface", "md.comp.navigation-bar.inactive.pressed.state-layer.color": "onSurface", "md.comp.navigation-bar.label-text.text-style": "labelMedium", "md.comp.navigation-bar.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity"}