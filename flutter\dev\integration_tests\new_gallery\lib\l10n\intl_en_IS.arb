{"loading": "Loading", "deselect": "Deselect", "select": "Select", "selectable": "Selectable (long press)", "selected": "Selected", "demo": "Demo", "bottomAppBar": "Bottom app bar", "notSelected": "Not selected", "demoCupertinoSearchTextFieldTitle": "Search text field", "demoCupertinoPicker": "Picker", "demoCupertinoSearchTextFieldSubtitle": "iOS-style search text field", "demoCupertinoSearchTextFieldDescription": "A search text field that lets the user search by entering text and that can offer and filter suggestions.", "demoCupertinoSearchTextFieldPlaceholder": "Enter some text", "demoCupertinoScrollbarTitle": "Sc<PERSON><PERSON>", "demoCupertinoScrollbarSubtitle": "iOS-style scrollbar", "demoCupertinoScrollbarDescription": "A scrollbar that wraps the given child", "demoTwoPaneItem": "Item {value}", "demoTwoPaneList": "List", "demoTwoPaneFoldableLabel": "Foldable", "demoTwoPaneSmallScreenLabel": "Small screen", "demoTwoPaneSmallScreenDescription": "This is how TwoPane behaves on a small screen device.", "demoTwoPaneTabletLabel": "Tablet/Desktop", "demoTwoPaneTabletDescription": "This is how TwoPane behaves on a larger screen like a tablet or desktop.", "demoTwoPaneTitle": "TwoPane", "demoTwoPaneSubtitle": "Responsive layouts on foldable, large and small screens", "splashSelectDemo": "Select a demo", "demoTwoPaneFoldableDescription": "This is how TwoPane behaves on a foldable device.", "demoTwoPaneDetails": "Details", "demoTwoPaneSelectItem": "Select an item", "demoTwoPaneItemDetails": "Item {value} details", "demoCupertinoContextMenuActionText": "Tap and hold the Flutter logo to see the context menu.", "demoCupertinoContextMenuDescription": "An iOS-style full screen contextual menu that appears when an element is long-pressed.", "demoAppBarTitle": "App bar", "demoAppBarDescription": "The app bar provides content and actions related to the current screen. It's used for branding, screen titles, navigation and actions", "demoDividerTitle": "Divider", "demoDividerSubtitle": "A divider is a thin line that groups content in lists and layouts.", "demoDividerDescription": "Dividers can be used in lists, drawers and elsewhere to separate content.", "demoVerticalDividerTitle": "Vertical divider", "demoCupertinoContextMenuTitle": "Context menu", "demoCupertinoContextMenuSubtitle": "iOS-style context menu", "demoAppBarSubtitle": "Displays information and actions relating to the current screen", "demoCupertinoContextMenuActionOne": "Action one", "demoCupertinoContextMenuActionTwo": "Action two", "demoDateRangePickerDescription": "Shows a dialogue containing a Material Design date range picker.", "demoDateRangePickerTitle": "Date range picker", "demoNavigationDrawerUserName": "User name", "demoNavigationDrawerUserEmail": "<EMAIL>", "demoNavigationDrawerText": "Swipe from the edge or tap the upper-left icon to see the drawer", "demoNavigationRailTitle": "Navigation rail", "demoNavigationRailSubtitle": "Displaying a navigation rail within an app", "demoNavigationRailDescription": "A material widget that is meant to be displayed at the left or right of an app to navigate between a small number of views, typically between three and five.", "demoNavigationRailFirst": "First", "demoNavigationDrawerTitle": "Navigation drawer", "demoNavigationRailThird": "Third", "replyStarredLabel": "Starred", "demoTextButtonDescription": "A text button displays an ink splash on press but does not lift. Use text buttons on toolbars, in dialogues and inline with padding", "demoElevatedButtonTitle": "Elevated button", "demoElevatedButtonDescription": "Elevated buttons add dimension to mostly flat layouts. They emphasise functions on busy or wide spaces.", "demoOutlinedButtonTitle": "Outlined button", "demoOutlinedButtonDescription": "Outlined buttons become opaque and elevate when pressed. They are often paired with raised buttons to indicate an alternative, secondary action.", "demoContainerTransformDemoInstructions": "Cards, lists and FAB", "demoNavigationDrawerSubtitle": "Displaying a drawer within app bar", "replyDescription": "An efficient, focused email app", "demoNavigationDrawerDescription": "A Material Design panel that slides in horizontally from the edge of the screen to show navigation links in an application.", "replyDraftsLabel": "Drafts", "demoNavigationDrawerToPageOne": "Item one", "replyInboxLabel": "Inbox", "demoSharedXAxisDemoInstructions": "Next and back buttons", "replySpamLabel": "Spam", "replyTrashLabel": "Bin", "replySentLabel": "<PERSON><PERSON>", "demoNavigationRailSecond": "Second", "demoNavigationDrawerToPageTwo": "Item two", "demoFadeScaleDemoInstructions": "Modal and FAB", "demoFadeThroughDemoInstructions": "Bottom navigation", "demoSharedZAxisDemoInstructions": "Settings icon button", "demoSharedYAxisDemoInstructions": "Sort by 'Recently played'", "demoTextButtonTitle": "Text button", "demoSharedZAxisBeefSandwichRecipeTitle": "Beef sandwich", "demoSharedZAxisDessertRecipeDescription": "Dessert recipe", "demoSharedYAxisAlbumTileSubtitle": "Artist", "demoSharedYAxisAlbumTileTitle": "Album", "demoSharedYAxisRecentSortTitle": "Recently played", "demoSharedYAxisAlphabeticalSortTitle": "A–Z", "demoSharedYAxisAlbumCount": "268 albums", "demoSharedYAxisTitle": "Shared y-axis", "demoSharedXAxisCreateAccountButtonText": "CREATE ACCOUNT", "demoFadeScaleAlertDialogDiscardButton": "DISCARD", "demoSharedXAxisSignInTextFieldLabel": "Email or phone number", "demoSharedXAxisSignInSubtitleText": "Sign in with your account", "demoSharedXAxisSignInWelcomeText": "<PERSON>", "demoSharedXAxisIndividualCourseSubtitle": "Shown individually", "demoSharedXAxisBundledCourseSubtitle": "Bundled", "demoFadeThroughAlbumsDestination": "Albums", "demoSharedXAxisDesignCourseTitle": "Design", "demoSharedXAxisIllustrationCourseTitle": "Illustration", "demoSharedXAxisBusinessCourseTitle": "Business", "demoSharedXAxisArtsAndCraftsCourseTitle": "Arts and crafts", "demoMotionPlaceholderSubtitle": "Secondary text", "demoFadeScaleAlertDialogCancelButton": "CANCEL", "demoFadeScaleAlertDialogHeader": "Alert dialogue", "demoFadeScaleHideFabButton": "HIDE FAB", "demoFadeScaleShowFabButton": "SHOW FAB", "demoFadeScaleShowAlertDialogButton": "SHOW MODAL", "demoFadeScaleDescription": "The fade pattern is used for UI elements that enter or exit within the bounds of the screen, such as a dialogue that fades in the centre of the screen.", "demoFadeScaleTitle": "Fade", "demoFadeThroughTextPlaceholder": "123 photos", "demoFadeThroughSearchDestination": "Search", "demoFadeThroughPhotosDestination": "Photos", "demoSharedXAxisCoursePageSubtitle": "Bundled categories appear as groups in your feed. You can always change this later.", "demoFadeThroughDescription": "The fade-through pattern is used for transitions between UI elements that do not have a strong relationship to each other.", "demoFadeThroughTitle": "Fade through", "demoSharedZAxisHelpSettingLabel": "Help", "demoMotionSubtitle": "All of the predefined transition patterns", "demoSharedZAxisNotificationSettingLabel": "Notifications", "demoSharedZAxisProfileSettingLabel": "Profile", "demoSharedZAxisSavedRecipesListTitle": "Saved recipes", "demoSharedZAxisBeefSandwichRecipeDescription": "Beef sandwich recipe", "demoSharedZAxisCrabPlateRecipeDescription": "Crab plate recipe", "demoSharedXAxisCoursePageTitle": "Streamline your courses", "demoSharedZAxisCrabPlateRecipeTitle": "<PERSON><PERSON>", "demoSharedZAxisShrimpPlateRecipeDescription": "Shrimp plate recipe", "demoSharedZAxisShrimpPlateRecipeTitle": "<PERSON>mp", "demoContainerTransformTypeFadeThrough": "FADE THROUGH", "demoSharedZAxisDessertRecipeTitle": "Dessert", "demoSharedZAxisSandwichRecipeDescription": "Sandwich recipe", "demoSharedZAxisSandwichRecipeTitle": "Sandwich", "demoSharedZAxisBurgerRecipeDescription": "Burger recipe", "demoSharedZAxisBurgerRecipeTitle": "Burger", "demoSharedZAxisSettingsPageTitle": "Settings", "demoSharedZAxisTitle": "Shared z-axis", "demoSharedZAxisPrivacySettingLabel": "Privacy", "demoMotionTitle": "Motion", "demoContainerTransformTitle": "Container transform", "demoContainerTransformDescription": "The container transform pattern is designed for transitions between UI elements that include a container. This pattern creates a visible connection between two UI elements", "demoContainerTransformModalBottomSheetTitle": "Fade mode", "demoContainerTransformTypeFade": "FADE", "demoSharedYAxisAlbumTileDurationUnit": "min", "demoMotionPlaceholderTitle": "Title", "demoSharedXAxisForgotEmailButtonText": "FORGOT EMAIL?", "demoMotionSmallPlaceholderSubtitle": "Secondary", "demoMotionDetailsPageTitle": "Details page", "demoMotionListTileTitle": "List item", "demoSharedAxisDescription": "The shared axis pattern is used for transitions between the UI elements that have a spatial or navigational relationship. This pattern uses a shared transformation on the x, y or z axis to reinforce the relationship between elements.", "demoSharedXAxisTitle": "Shared x-axis", "demoSharedXAxisBackButtonText": "BACK", "demoSharedXAxisNextButtonText": "NEXT", "demoSharedXAxisCulinaryCourseTitle": "Culinary", "githubRepo": "{repoName} GitHub repository", "fortnightlyMenuUS": "US", "fortnightlyMenuBusiness": "Business", "fortnightlyMenuScience": "Science", "fortnightlyMenuSports": "Sport", "fortnightlyMenuTravel": "Travel", "fortnightlyMenuCulture": "Culture", "fortnightlyTrendingTechDesign": "TechDesign", "rallyBudgetDetailAmountLeft": "Amount left", "fortnightlyHeadlineArmy": "Reforming The Green Army from Within", "fortnightlyDescription": "A content-focused news app", "rallyBillDetailAmountDue": "Amount due", "rallyBudgetDetailTotalCap": "Total cap", "rallyBudgetDetailAmountUsed": "Amount used", "fortnightlyTrendingHealthcareRevolution": "HealthcareRevolution", "fortnightlyMenuFrontPage": "Front page", "fortnightlyMenuWorld": "World", "rallyBillDetailAmountPaid": "Amount paid", "fortnightlyMenuPolitics": "Politics", "fortnightlyHeadlineBees": "Farmland Bees in Short Supply", "fortnightlyHeadlineGasoline": "The Future of Petrol", "fortnightlyTrendingGreenArmy": "GreenArmy", "fortnightlyHeadlineFeminists": "Feminists take on Partisanship", "fortnightlyHeadlineFabrics": "Designers use Tech to make Futuristic Fabrics", "fortnightlyHeadlineStocks": "As Stocks Stagnate, many Look to Currency", "fortnightlyTrendingReform": "Reform", "fortnightlyMenuTech": "Tech", "fortnightlyHeadlineWar": "Divided American Lives During War", "fortnightlyHeadlineHealthcare": "The Quiet, yet Powerful Healthcare Revolution", "fortnightlyLatestUpdates": "Latest updates", "fortnightlyTrendingStocks": "Stocks", "rallyBillDetailTotalAmount": "Total amount", "demoCupertinoPickerDateTime": "Date and time", "signIn": "SIGN IN", "dataTableRowWithSugar": "{value} with sugar", "dataTableRowApplePie": "Apple pie", "dataTableRowDonut": "Doughnut", "dataTableRowHoneycomb": "Honeycomb", "dataTableRowLollipop": "Lollipop", "dataTableRowJellyBean": "Jelly bean", "dataTableRowGingerbread": "Gingerbread", "dataTableRowCupcake": "Cupcake", "dataTableRowEclair": "Eclair", "dataTableRowIceCreamSandwich": "Ice cream sandwich", "dataTableRowFrozenYogurt": "Frozen yogurt", "dataTableColumnIron": "Iron (%)", "dataTableColumnCalcium": "Calcium (%)", "dataTableColumnSodium": "Sodium (mg)", "demoTimePickerTitle": "Time picker", "demo2dTransformationsResetTooltip": "Reset transformations", "dataTableColumnFat": "Fat (gm)", "dataTableColumnCalories": "Calories", "dataTableColumnDessert": "<PERSON><PERSON><PERSON> (1 serving)", "cardsDemoTravelDestinationLocation1": "Thanjavur, Tamil Nadu", "demoTimePickerDescription": "Shows a dialogue containing a material design time picker.", "demoPickersShowPicker": "SHOW PICKER", "demoTabsScrollingTitle": "Scrolling", "demoTabsNonScrollingTitle": "Non-scrolling", "craneHours": "{hours,plural,=1{1 h}other{{hours}h}}", "craneMinutes": "{minutes,plural,=1{1 m}other{{minutes}m}}", "craneFlightDuration": "{hoursShortForm} {minutesShortForm}", "dataTableHeader": "Nutrition", "demoDatePickerTitle": "Date picker", "demoPickersSubtitle": "Date and time selection", "demoPickersTitle": "Pickers", "demo2dTransformationsEditTooltip": "Edit tile", "demoDataTableDescription": "Data tables display information in a grid-like format of rows and columns. They organise information in a way that's easy to scan, so that users can look for patterns and insights.", "demo2dTransformationsDescription": "Tap to edit tiles, and use gestures to move around the scene. Drag to pan, pinch to zoom, rotate with two fingers. Press the reset button to return to the starting orientation.", "demo2dTransformationsSubtitle": "Pan, zoom, rotate", "demo2dTransformationsTitle": "2D transformations", "demoCupertinoTextFieldPIN": "PIN", "demoCupertinoTextFieldDescription": "A text field allows the user to enter text, either with a hardware keyboard or with an on-screen keyboard.", "demoCupertinoTextFieldSubtitle": "iOS-style text fields", "demoCupertinoTextFieldTitle": "Text fields", "demoDatePickerDescription": "Shows a dialogue containing a material design date picker.", "demoCupertinoPickerTime": "Time", "demoCupertinoPickerDate": "Date", "demoCupertinoPickerTimer": "Timer", "demoCupertinoPickerDescription": "An iOS-style picker widget that can be used to select strings, dates, times or both date and time.", "demoCupertinoPickerSubtitle": "iOS-style pickers", "demoCupertinoPickerTitle": "Pickers", "dataTableRowWithHoney": "{value} with honey", "cardsDemoTravelDestinationCity2": "Chettinad", "bannerDemoResetText": "Reset the banner", "bannerDemoMultipleText": "Multiple actions", "bannerDemoLeadingText": "Leading icon", "dismiss": "DISMISS", "cardsDemoTappable": "Tappable", "cardsDemoSelectable": "Selectable (long press)", "cardsDemoExplore": "Explore", "cardsDemoExploreSemantics": "Explore {destinationName}", "cardsDemoShareSemantics": "Share {destinationName}", "cardsDemoTravelDestinationTitle1": "Top 10 cities to visit in Tamil Nadu", "cardsDemoTravelDestinationDescription1": "Number 10", "cardsDemoTravelDestinationCity1": "Thanjavur", "dataTableColumnProtein": "Protein (gm)", "cardsDemoTravelDestinationTitle2": "Artisans of Southern India", "cardsDemoTravelDestinationDescription2": "Silk spinners", "bannerDemoText": "Your password was updated on your other device. Please sign in again.", "cardsDemoTravelDestinationLocation2": "Sivaganga, Tamil Nadu", "cardsDemoTravelDestinationTitle3": "Brihadisvara Temple", "cardsDemoTravelDestinationDescription3": "Temples", "demoBannerTitle": "Banner", "demoBannerSubtitle": "Displaying a banner within a list", "demoBannerDescription": "A banner displays an important, succinct message, and provides actions for users to address (or dismiss the banner). A user action is required for it to be dismissed.", "demoCardTitle": "Cards", "demoCardSubtitle": "Baseline cards with rounded corners", "demoCardDescription": "A card is a sheet of material used to represent some related information, for example, an album, a geographical location, a meal, contact details, etc.", "demoDataTableTitle": "Data tables", "demoDataTableSubtitle": "Rows and columns of information", "dataTableColumnCarbs": "Carbs (gm)", "placeTanjore": "<PERSON><PERSON><PERSON>", "demoGridListsTitle": "Grid lists", "placeFlowerMarket": "Flower market", "placeBronzeWorks": "Bronze works", "placeMarket": "Market", "placeThanjavurTemple": "Thanjavur Temple", "placeSaltFarm": "Salt farm", "placeScooters": "Scooters", "placeSilkMaker": "Silk maker", "placeLunchPrep": "Lunch prep", "placeBeach": "Beach", "placeFisherman": "Fisherman", "demoMenuSelected": "Selected: {value}", "demoMenuRemove": "Remove", "demoMenuGetLink": "Get link", "demoMenuShare": "Share", "demoBottomAppBarSubtitle": "Displays navigation and actions at the bottom", "demoMenuAnItemWithASectionedMenu": "An item with a sectioned menu", "demoMenuADisabledMenuItem": "Disabled menu item", "demoLinearProgressIndicatorTitle": "Linear progress indicator", "demoMenuContextMenuItemOne": "Context menu item one", "demoMenuAnItemWithASimpleMenu": "An item with a simple menu", "demoCustomSlidersTitle": "Custom sliders", "demoMenuAnItemWithAChecklistMenu": "An item with a checklist menu", "demoCupertinoActivityIndicatorTitle": "Activity indicator", "demoCupertinoActivityIndicatorSubtitle": "iOS-style activity indicators", "demoCupertinoActivityIndicatorDescription": "An iOS-style activity indicator that spins clockwise.", "demoCupertinoNavigationBarTitle": "Navigation bar", "demoCupertinoNavigationBarSubtitle": "iOS-style navigation bar", "demoCupertinoNavigationBarDescription": "An iOS-styled navigation bar. The navigation bar is a toolbar that minimally consists of a page title, in the middle of the toolbar.", "demoCupertinoPullToRefreshTitle": "Pull to refresh", "demoCupertinoPullToRefreshSubtitle": "iOS-style pull to refresh control", "demoCupertinoPullToRefreshDescription": "A widget implementing the iOS-style pull to refresh content control.", "demoProgressIndicatorTitle": "Progress indicators", "demoProgressIndicatorSubtitle": "Linear, circular, indeterminate", "demoCircularProgressIndicatorTitle": "Circular progress indicator", "demoCircularProgressIndicatorDescription": "A material design circular progress indicator, which spins to indicate that the application is busy.", "demoMenuFour": "Four", "demoLinearProgressIndicatorDescription": "A material design linear progress indicator, also known as a progress bar.", "demoTooltipTitle": "Tooltips", "demoTooltipSubtitle": "Short message displayed on long press or hover", "demoTooltipDescription": "Tooltips provide text labels that help to explain the function of a button or other user interface action. Tooltips display informative text when users hover over, focus on or long press an element.", "demoTooltipInstructions": "Long press or hover to display the tooltip.", "placeChennai": "Chennai", "demoMenuChecked": "Checked: {value}", "placeChettinad": "Chettinad", "demoMenuPreview": "Preview", "demoBottomAppBarTitle": "Bottom app bar", "demoBottomAppBarDescription": "Bottom app bars provide access to a bottom navigation drawer and up to four actions, including the floating action button.", "bottomAppBarNotch": "Notch", "bottomAppBarPosition": "Floating action button position", "bottomAppBarPositionDockedEnd": "Docked - End", "bottomAppBarPositionDockedCenter": "Docked - Centre", "bottomAppBarPositionFloatingEnd": "Floating - End", "bottomAppBarPositionFloatingCenter": "Floating - Centre", "demoSlidersEditableNumericalValue": "Editable numerical value", "demoGridListsSubtitle": "Row and column layout", "demoGridListsDescription": "Grid lists are best suited for presenting homogeneous data, typically images. Each item in a grid list is called a tile.", "demoGridListsImageOnlyTitle": "Image only", "demoGridListsHeaderTitle": "With header", "demoGridListsFooterTitle": "With footer", "demoSlidersTitle": "Sliders", "demoSlidersSubtitle": "Widgets for selecting a value by swiping", "demoSlidersDescription": "Sliders reflect a range of values along a bar, from which users may select a single value. They are ideal for adjusting settings such as volume, brightness or applying image filters.", "demoRangeSlidersTitle": "Range sliders", "demoRangeSlidersDescription": "Sliders reflect a range of values along a bar. They can have icons on both ends of the bar that reflect a range of values. They are ideal for adjusting settings such as volume, brightness or applying image filters.", "demoMenuAnItemWithAContextMenuButton": "An item with a context menu", "demoCustomSlidersDescription": "Sliders reflect a range of values along a bar, from which users may select a single value or range of values. The sliders can be themed and customised.", "demoSlidersContinuousWithEditableNumericalValue": "Continuous with editable numerical value", "demoSlidersDiscrete": "Discrete", "demoSlidersDiscreteSliderWithCustomTheme": "Discrete slider with custom theme", "demoSlidersContinuousRangeSliderWithCustomTheme": "Continuous range slider with custom theme", "demoSlidersContinuous": "Continuous", "placePondicherry": "Pondicherry", "demoMenuTitle": "<PERSON><PERSON>", "demoContextMenuTitle": "Context menu", "demoSectionedMenuTitle": "Sectioned menu", "demoSimpleMenuTitle": "Simple menu", "demoChecklistMenuTitle": "Checklist menu", "demoMenuSubtitle": "Menu buttons and simple menus", "demoMenuDescription": "A menu displays a list of choices on a temporary surface. They appear when users interact with a button, action or other control.", "demoMenuItemValueOne": "Menu item one", "demoMenuItemValueTwo": "Menu item two", "demoMenuItemValueThree": "Menu item three", "demoMenuOne": "One", "demoMenuTwo": "Two", "demoMenuThree": "Three", "demoMenuContextMenuItemThree": "Context menu item three", "demoCupertinoSwitchSubtitle": "iOS-style switch", "demoSnackbarsText": "This is a snackbar.", "demoCupertinoSliderSubtitle": "iOS-style slider", "demoCupertinoSliderDescription": "A slider can be used to select from either a continuous or a discrete set of values.", "demoCupertinoSliderContinuous": "Continuous: {value}", "demoCupertinoSliderDiscrete": "Discrete: {value}", "demoSnackbarsAction": "You pressed the snackbar action.", "backToGallery": "Back to Gallery", "demoCupertinoTabBarTitle": "Tab bar", "demoCupertinoSwitchDescription": "A switch is used to toggle the on/off state of a single setting.", "demoSnackbarsActionButtonLabel": "ACTION", "cupertinoTabBarProfileTab": "Profile", "demoSnackbarsButtonLabel": "SHOW A SNACKBAR", "demoSnackbarsDescription": "Snackbars inform users of a process that an app has performed or will perform. They appear temporarily, towards the bottom of the screen. They shouldn't interrupt the user experience, and they don't require user input to disappear.", "demoSnackbarsSubtitle": "Snackbars show messages at the bottom of the screen", "demoSnackbarsTitle": "Snackbars", "demoCupertinoSliderTitle": "Slide<PERSON>", "cupertinoTabBarChatTab": "Cha<PERSON>", "cupertinoTabBarHomeTab": "Home", "demoCupertinoTabBarDescription": "An iOS-style bottom navigation tab bar. Displays multiple tabs with one tab being active, the first tab by default.", "demoCupertinoTabBarSubtitle": "iOS-style bottom tab bar", "demoOptionsFeatureTitle": "View options", "demoOptionsFeatureDescription": "Tap here to view available options for this demo.", "demoCodeViewerCopyAll": "COPY ALL", "shrineScreenReaderRemoveProductButton": "Remove {product}", "shrineScreenReaderProductAddToCart": "Add to basket", "shrineScreenReaderCart": "{quantity,plural,=0{Shopping basket, no items}=1{Shopping basket, 1 item}other{Shopping basket, {quantity} items}}", "demoCodeViewerFailedToCopyToClipboardMessage": "Failed to copy to clipboard: {error}", "demoCodeViewerCopiedToClipboardMessage": "Copied to clipboard.", "craneSleep8SemanticLabel": "Mayan ruins on a cliff above a beach", "craneSleep4SemanticLabel": "Lake-side hotel in front of mountains", "craneSleep2SemanticLabel": "Machu Picchu citadel", "craneSleep1SemanticLabel": "Chalet in a snowy landscape with evergreen trees", "craneSleep0SemanticLabel": "Overwater bungalows", "craneFly13SemanticLabel": "Seaside pool with palm trees", "craneFly12SemanticLabel": "Pool with palm trees", "craneFly11SemanticLabel": "Brick lighthouse at sea", "craneFly10SemanticLabel": "Al-Azhar Mosque towers during sunset", "craneFly9SemanticLabel": "Man leaning on an antique blue car", "craneFly8SemanticLabel": "Supertree Grove", "craneEat9SemanticLabel": "Café counter with pastries", "craneEat2SemanticLabel": "Burger", "craneFly5SemanticLabel": "Lake-side hotel in front of mountains", "demoSelectionControlsSubtitle": "Tick boxes, radio buttons and switches", "craneEat10SemanticLabel": "Woman holding huge pastrami sandwich", "craneFly4SemanticLabel": "Overwater bungalows", "craneEat7SemanticLabel": "Bakery entrance", "craneEat6SemanticLabel": "Shrimp dish", "craneEat5SemanticLabel": "Artsy restaurant seating area", "craneEat4SemanticLabel": "Chocolate dessert", "craneEat3SemanticLabel": "Korean taco", "craneFly3SemanticLabel": "Machu Picchu citadel", "craneEat1SemanticLabel": "Empty bar with diner-style stools", "craneEat0SemanticLabel": "Pizza in a wood-fired oven", "craneSleep11SemanticLabel": "Taipei 101 skyscraper", "craneSleep10SemanticLabel": "Al-Azhar Mosque towers during sunset", "craneSleep9SemanticLabel": "Brick lighthouse at sea", "craneEat8SemanticLabel": "Plate of crawfish", "craneSleep7SemanticLabel": "Colourful apartments at Ribeira Square", "craneSleep6SemanticLabel": "Pool with palm trees", "craneSleep5SemanticLabel": "Tent in a field", "settingsButtonCloseLabel": "Close settings", "demoSelectionControlsCheckboxDescription": "Tick boxes allow the user to select multiple options from a set. A normal tick box's value is true or false and a tristate tick box's value can also be null.", "settingsButtonLabel": "Settings", "demoListsTitle": "Lists", "demoListsSubtitle": "Scrolling list layouts", "demoListsDescription": "A single fixed-height row that typically contains some text as well as a leading or trailing icon.", "demoOneLineListsTitle": "One line", "demoTwoLineListsTitle": "Two lines", "demoListsSecondary": "Secondary text", "demoSelectionControlsTitle": "Selection controls", "craneFly7SemanticLabel": "Mount Rushmore", "demoSelectionControlsCheckboxTitle": "Tick box", "craneSleep3SemanticLabel": "Man leaning on an antique blue car", "demoSelectionControlsRadioTitle": "Radio", "demoSelectionControlsRadioDescription": "Radio buttons allow the user to select one option from a set. Use radio buttons for exclusive selection if you think that the user needs to see all available options side by side.", "demoSelectionControlsSwitchTitle": "Switch", "demoSelectionControlsSwitchDescription": "On/off switches toggle the state of a single settings option. The option that the switch controls, as well as the state it's in, should be made clear from the corresponding inline label.", "craneFly0SemanticLabel": "Chalet in a snowy landscape with evergreen trees", "craneFly1SemanticLabel": "Tent in a field", "craneFly2SemanticLabel": "Prayer flags in front of snowy mountain", "craneFly6SemanticLabel": "Aerial view of Palacio de Bellas Artes", "rallySeeAllAccounts": "See all accounts", "rallyBillAmount": "{billName} bill due {date} for {amount}.", "shrineTooltipCloseCart": "Close basket", "shrineTooltipCloseMenu": "Close menu", "shrineTooltipOpenMenu": "Open menu", "shrineTooltipSettings": "Settings", "shrineTooltipSearch": "Search", "demoTabsDescription": "Tabs organise content across different screens, data sets and other interactions.", "demoTabsSubtitle": "Tabs with independently scrollable views", "demoTabsTitle": "Tabs", "rallyBudgetAmount": "{budgetName} budget with {amountUsed} used of {amountTotal}, {amountLeft} left", "shrineTooltipRemoveItem": "Remove item", "rallyAccountAmount": "{accountName} account {accountNumber} with {amount}.", "rallySeeAllBudgets": "See all budgets", "rallySeeAllBills": "See all bills", "craneFormDate": "Select date", "craneFormOrigin": "Choose origin", "craneFly2": "Khumbu Valley, Nepal", "craneFly3": "Machu <PERSON>hu, Peru", "craneFly4": "Malé, Maldives", "craneFly5": "Vitznau, Switzerland", "craneFly6": "Mexico City, Mexico", "craneFly7": "Mount Rushmore, United States", "settingsTextDirectionLocaleBased": "Based on locale", "craneFly9": "Havana, Cuba", "craneFly10": "Cairo, Egypt", "craneFly11": "Lisbon, Portugal", "craneFly12": "Napa, United States", "craneFly13": "Bali, Indonesia", "craneSleep0": "Malé, Maldives", "craneSleep1": "Aspen, United States", "craneSleep2": "Machu <PERSON>hu, Peru", "demoCupertinoSegmentedControlTitle": "Segmented control", "craneSleep4": "Vitznau, Switzerland", "craneSleep5": "Big Sur, United States", "craneSleep6": "Napa, United States", "craneSleep7": "Porto, Portugal", "craneSleep8": "Tulum, Mexico", "craneEat5": "Seoul, South Korea", "demoChipTitle": "Chips", "demoChipSubtitle": "Compact elements that represent an input, attribute or action", "demoActionChipTitle": "Action chip", "demoActionChipDescription": "Action chips are a set of options which trigger an action related to primary content. Action chips should appear dynamically and contextually in a UI.", "demoChoiceChipTitle": "Choice chip", "demoChoiceChipDescription": "Choice chips represent a single choice from a set. Choice chips contain related descriptive text or categories.", "demoFilterChipTitle": "Filter chip", "demoFilterChipDescription": "Filter chips use tags or descriptive words as a way to filter content.", "demoInputChipTitle": "Input chip", "demoInputChipDescription": "Input chips represent a complex piece of information, such as an entity (person, place or thing) or conversational text, in a compact form.", "craneSleep9": "Lisbon, Portugal", "craneEat10": "Lisbon, Portugal", "demoCupertinoSegmentedControlDescription": "Used to select between a number of mutually exclusive options. When one option in the segmented control is selected, the other options in the segmented control cease to be selected.", "chipTurnOnLights": "Turn on lights", "chipSmall": "Small", "chipMedium": "Medium", "chipLarge": "Large", "chipElevator": "Lift", "chipWasher": "Washing machine", "chipFireplace": "Fireplace", "chipBiking": "Cycling", "craneFormDiners": "Diners", "rallyAlertsMessageUnassignedTransactions": "{count,plural,=1{Increase your potential tax deduction! Assign categories to 1 unassigned transaction.}other{Increase your potential tax deduction! Assign categories to {count} unassigned transactions.}}", "craneFormTime": "Select time", "craneFormLocation": "Select location", "craneFormTravelers": "Travellers", "craneEat8": "Atlanta, United States", "craneFormDestination": "Choose destination", "craneFormDates": "Select dates", "craneFly": "FLY", "craneSleep": "SLEEP", "craneEat": "EAT", "craneFlySubhead": "Explore flights by destination", "craneSleepSubhead": "Explore properties by destination", "craneEatSubhead": "Explore restaurants by destination", "craneFlyStops": "{numberOfStops,plural,=0{Non-stop}=1{1 stop}other{{numberOfStops} stops}}", "craneSleepProperties": "{totalProperties,plural,=0{No available properties}=1{1 available property}other{{totalProperties} available properties}}", "craneEatRestaurants": "{totalRestaurants,plural,=0{No restaurants}=1{1 restaurant}other{{totalRestaurants} restaurants}}", "craneFly0": "Aspen, United States", "demoCupertinoSegmentedControlSubtitle": "iOS-style segmented control", "craneSleep10": "Cairo, Egypt", "craneEat9": "Madrid, Spain", "craneFly1": "Big Sur, United States", "craneEat7": "Nashville, United States", "craneEat6": "Seattle, United States", "craneFly8": "Singapore", "craneEat4": "Paris, France", "craneEat3": "Portland, United States", "craneEat2": "Córdoba, Argentina", "craneEat1": "Dallas, United States", "craneEat0": "Naples, Italy", "craneSleep11": "Taipei, Taiwan", "craneSleep3": "Havana, Cuba", "shrineLogoutButtonCaption": "LOGOUT", "rallyTitleBills": "BILLS", "rallyTitleAccounts": "ACCOUNTS", "shrineProductVagabondSack": "Vagabond sack", "rallyAccountDetailDataInterestYtd": "Interest YTD", "shrineProductWhitneyBelt": "Whitney belt", "shrineProductGardenStrand": "Garden strand", "shrineProductStrutEarrings": "<PERSON><PERSON><PERSON> earrings", "shrineProductVarsitySocks": "Varsity socks", "shrineProductWeaveKeyring": "Weave keyring", "shrineProductGatsbyHat": "Gatsby hat", "shrineProductShrugBag": "Shrug bag", "shrineProductGiltDeskTrio": "Gilt desk trio", "shrineProductCopperWireRack": "Copper wire rack", "shrineProductSootheCeramicSet": "Soothe ceramic set", "shrineProductHurrahsTeaSet": "Hurrahs tea set", "shrineProductBlueStoneMug": "Blue stone mug", "shrineProductRainwaterTray": "Rainwater tray", "shrineProductChambrayNapkins": "Chambray napkins", "shrineProductSucculentPlanters": "Succulent planters", "shrineProductQuartetTable": "Quartet table", "shrineProductKitchenQuattro": "Kitchen quattro", "shrineProductClaySweater": "Clay sweater", "shrineProductSeaTunic": "Sea tunic", "shrineProductPlasterTunic": "Plaster tunic", "rallyBudgetCategoryRestaurants": "Restaurants", "shrineProductChambrayShirt": "Chambray shirt", "shrineProductSeabreezeSweater": "Seabreeze sweater", "shrineProductGentryJacket": "Gentry jacket", "shrineProductNavyTrousers": "Navy trousers", "shrineProductWalterHenleyWhite": "<PERSON> (white)", "shrineProductSurfAndPerfShirt": "Surf and perf shirt", "shrineProductGingerScarf": "Ginger scarf", "shrineProductRamonaCrossover": "Ramona crossover", "shrineProductClassicWhiteCollar": "Classic white collar", "shrineProductSunshirtDress": "Sunshirt dress", "rallyAccountDetailDataInterestRate": "Interest rate", "rallyAccountDetailDataAnnualPercentageYield": "Annual percentage yield", "rallyAccountDataVacation": "Holiday", "shrineProductFineLinesTee": "Fine lines tee", "rallyAccountDataHomeSavings": "Home savings", "rallyAccountDataChecking": "Current", "rallyAccountDetailDataInterestPaidLastYear": "Interest paid last year", "rallyAccountDetailDataNextStatement": "Next statement", "rallyAccountDetailDataAccountOwner": "Account owner", "rallyBudgetCategoryCoffeeShops": "Coffee shops", "rallyBudgetCategoryGroceries": "Groceries", "shrineProductCeriseScallopTee": "<PERSON><PERSON> scallop tee", "rallyBudgetCategoryClothing": "Clothing", "rallySettingsManageAccounts": "Manage accounts", "rallyAccountDataCarSavings": "Car savings", "rallySettingsTaxDocuments": "Tax documents", "rallySettingsPasscodeAndTouchId": "Passcode and Touch ID", "rallySettingsNotifications": "Notifications", "rallySettingsPersonalInformation": "Personal information", "rallySettingsPaperlessSettings": "Paperless settings", "rallySettingsFindAtms": "Find ATMs", "rallySettingsHelp": "Help", "rallySettingsSignOut": "Sign out", "rallyAccountTotal": "Total", "rallyBillsDue": "Due", "rallyBudgetLeft": "Left", "rallyAccounts": "Accounts", "rallyBills": "Bills", "rallyBudgets": "Budgets", "rallyAlerts": "<PERSON><PERSON><PERSON>", "rallySeeAll": "SEE ALL", "rallyFinanceLeft": "LEFT", "rallyTitleOverview": "OVERVIEW", "shrineProductShoulderRollsTee": "Shoulder rolls tee", "shrineNextButtonCaption": "NEXT", "rallyTitleBudgets": "BUDGETS", "rallyTitleSettings": "SETTINGS", "rallyLoginLoginToRally": "Log in to Rally", "rallyLoginNoAccount": "Don't have an account?", "rallyLoginSignUp": "SIGN UP", "rallyLoginUsername": "Username", "rallyLoginPassword": "Password", "rallyLoginLabelLogin": "Log in", "rallyLoginRememberMe": "Remember me", "rallyLoginButtonLogin": "LOGIN", "rallyAlertsMessageHeadsUpShopping": "Heads up: you've used up {percent} of your shopping budget for this month.", "rallyAlertsMessageSpentOnRestaurants": "You've spent {amount} on restaurants this week.", "rallyAlertsMessageATMFees": "You've spent {amount} in ATM fees this month", "rallyAlertsMessageCheckingAccount": "Good work! Your current account is {percent} higher than last month.", "shrineMenuCaption": "MENU", "shrineCategoryNameAll": "ALL", "shrineCategoryNameAccessories": "ACCESSORIES", "shrineCategoryNameClothing": "CLOTHING", "shrineCategoryNameHome": "HOME", "shrineLoginUsernameLabel": "Username", "shrineLoginPasswordLabel": "Password", "shrineCancelButtonCaption": "CANCEL", "shrineCartTaxCaption": "Tax:", "shrineCartPageCaption": "BASKET", "shrineProductQuantity": "Quantity: {quantity}", "shrineProductPrice": "x {price}", "shrineCartItemCount": "{quantity,plural,=0{NO ITEMS}=1{1 ITEM}other{{quantity} ITEMS}}", "shrineCartClearButtonCaption": "CLEAR BASKET", "shrineCartTotalCaption": "TOTAL", "shrineCartSubtotalCaption": "Subtotal:", "shrineCartShippingCaption": "Delivery:", "shrineProductGreySlouchTank": "Grey slouch tank top", "shrineProductStellaSunglasses": "Stella sunglasses", "shrineProductWhitePinstripeShirt": "White pinstripe shirt", "demoTextFieldWhereCanWeReachYou": "Where can we contact you?", "settingsTextDirectionLTR": "LTR", "settingsTextScalingLarge": "Large", "demoBottomSheetHeader": "Header", "demoBottomSheetItem": "Item {value}", "demoBottomTextFieldsTitle": "Text fields", "demoTextFieldTitle": "Text fields", "demoTextFieldSubtitle": "Single line of editable text and numbers", "demoTextFieldDescription": "Text fields allow users to enter text into a UI. They typically appear in forms and dialogues.", "demoTextFieldShowPasswordLabel": "Show password", "demoTextFieldHidePasswordLabel": "Hide password", "demoTextFieldFormErrors": "Please fix the errors in red before submitting.", "demoTextFieldNameRequired": "Name is required.", "demoTextFieldOnlyAlphabeticalChars": "Please enter only alphabetical characters.", "demoTextFieldEnterUSPhoneNumber": "(###) ###-#### – Enter a US phone number.", "demoTextFieldEnterPassword": "Please enter a password.", "demoTextFieldPasswordsDoNotMatch": "The passwords don't match", "demoTextFieldWhatDoPeopleCallYou": "What do people call you?", "demoTextFieldNameField": "Name*", "demoBottomSheetButtonText": "SHOW BOTTOM SHEET", "demoTextFieldPhoneNumber": "Phone number*", "demoBottomSheetTitle": "Bottom sheet", "demoTextFieldEmail": "Email", "demoTextFieldTellUsAboutYourself": "Tell us about yourself (e.g. write down what you do or what hobbies you have)", "demoTextFieldKeepItShort": "Keep it short, this is just a demo.", "starterAppGenericButton": "BUTTON", "demoTextFieldLifeStory": "Life story", "demoTextFieldSalary": "Salary", "demoTextFieldUSD": "USD", "demoTextFieldNoMoreThan": "No more than 8 characters.", "demoTextFieldPassword": "Password*", "demoTextFieldRetypePassword": "Re-type password*", "demoTextFieldSubmit": "SUBMIT", "demoBottomNavigationSubtitle": "Bottom navigation with cross-fading views", "demoBottomSheetAddLabel": "Add", "demoBottomSheetModalDescription": "A modal bottom sheet is an alternative to a menu or a dialogue and prevents the user from interacting with the rest of the app.", "demoBottomSheetModalTitle": "Modal bottom sheet", "demoBottomSheetPersistentDescription": "A persistent bottom sheet shows information that supplements the primary content of the app. A persistent bottom sheet remains visible even when the user interacts with other parts of the app.", "demoBottomSheetPersistentTitle": "Persistent bottom sheet", "demoBottomSheetSubtitle": "Persistent and modal bottom sheets", "demoTextFieldNameHasPhoneNumber": "{name} phone number is {phoneNumber}", "buttonText": "BUTTON", "demoTypographyDescription": "Definitions for the various typographical styles found in Material Design.", "demoTypographySubtitle": "All of the predefined text styles", "demoTypographyTitle": "Typography", "demoFullscreenDialogDescription": "The fullscreenDialog property specifies whether the incoming page is a full-screen modal dialogue", "demoFlatButtonDescription": "A flat button displays an ink splash on press but does not lift. Use flat buttons on toolbars, in dialogues and inline with padding", "demoBottomNavigationDescription": "Bottom navigation bars display three to five destinations at the bottom of a screen. Each destination is represented by an icon and an optional text label. When a bottom navigation icon is tapped, the user is taken to the top-level navigation destination associated with that icon.", "demoBottomNavigationSelectedLabel": "Selected label", "demoBottomNavigationPersistentLabels": "Persistent labels", "starterAppDrawerItem": "Item {value}", "demoTextFieldRequiredField": "* indicates required field", "demoBottomNavigationTitle": "Bottom navigation", "settingsLightTheme": "Light", "settingsTheme": "Theme", "settingsPlatformIOS": "iOS", "settingsPlatformAndroid": "Android", "settingsTextDirectionRTL": "RTL", "settingsTextScalingHuge": "<PERSON>ge", "cupertinoButton": "<PERSON><PERSON>", "settingsTextScalingNormal": "Normal", "settingsTextScalingSmall": "Small", "settingsSystemDefault": "System", "settingsTitle": "Settings", "rallyDescription": "A personal finance app", "aboutDialogDescription": "To see the source code for this app, please visit the {repoLink}.", "bottomNavigationCommentsTab": "Comments", "starterAppGenericBody": "Body", "starterAppGenericHeadline": "Headline", "starterAppGenericSubtitle": "Subtitle", "starterAppGenericTitle": "Title", "starterAppTooltipSearch": "Search", "starterAppTooltipShare": "Share", "starterAppTooltipFavorite": "Favourite", "starterAppTooltipAdd": "Add", "bottomNavigationCalendarTab": "Calendar", "starterAppDescription": "A responsive starter layout", "starterAppTitle": "Starter app", "aboutFlutterSamplesRepo": "Flutter samples GitHub repo", "bottomNavigationContentPlaceholder": "Placeholder for {title} tab", "bottomNavigationCameraTab": "Camera", "bottomNavigationAlarmTab": "Alarm", "bottomNavigationAccountTab": "Account", "demoTextFieldYourEmailAddress": "Your email address", "demoToggleButtonDescription": "Toggle buttons can be used to group related options. To emphasise groups of related toggle buttons, a group should share a common container", "colorsGrey": "GREY", "colorsBrown": "BROWN", "colorsDeepOrange": "DEEP ORANGE", "colorsOrange": "ORANGE", "colorsAmber": "AMBER", "colorsYellow": "YELLOW", "colorsLime": "LIME", "colorsLightGreen": "LIGHT GREEN", "colorsGreen": "GREEN", "homeHeaderGallery": "Gallery", "homeHeaderCategories": "Categories", "shrineDescription": "A fashionable retail app", "craneDescription": "A personalised travel app", "homeCategoryReference": "STYLES AND OTHER", "demoInvalidURL": "Couldn't display URL:", "demoOptionsTooltip": "Options", "demoInfoTooltip": "Info", "demoCodeTooltip": "Demo code", "demoDocumentationTooltip": "API Documentation", "demoFullscreenTooltip": "Full screen", "settingsTextScaling": "Text scaling", "settingsTextDirection": "Text direction", "settingsLocale": "Locale", "settingsPlatformMechanics": "Platform mechanics", "settingsDarkTheme": "Dark", "settingsSlowMotion": "Slow motion", "settingsAbout": "About Flutter Gallery", "settingsFeedback": "Send feedback", "settingsAttribution": "Designed by TOASTER in London", "demoButtonTitle": "Buttons", "demoButtonSubtitle": "Text, elevated, outlined and more", "demoFlatButtonTitle": "Flat Button", "demoRaisedButtonDescription": "Raised buttons add dimension to mostly flat layouts. They emphasise functions on busy or wide spaces.", "demoRaisedButtonTitle": "Raised <PERSON>", "demoOutlineButtonTitle": "Outline Button", "demoOutlineButtonDescription": "Outline buttons become opaque and elevate when pressed. They are often paired with raised buttons to indicate an alternative, secondary action.", "demoToggleButtonTitle": "Toggle <PERSON>", "colorsTeal": "TEAL", "demoFloatingButtonTitle": "Floating Action Button", "demoFloatingButtonDescription": "A floating action button is a circular icon button that hovers over content to promote a primary action in the application.", "demoDialogTitle": "Dialogues", "demoDialogSubtitle": "Simple, alert and full-screen", "demoAlertDialogTitle": "<PERSON><PERSON>", "demoAlertDialogDescription": "An alert dialogue informs the user about situations that require acknowledgement. An alert dialogue has an optional title and an optional list of actions.", "demoAlertTitleDialogTitle": "<PERSON><PERSON> With Title", "demoSimpleDialogTitle": "Simple", "demoSimpleDialogDescription": "A simple dialogue offers the user a choice between several options. A simple dialogue has an optional title that is displayed above the choices.", "demoFullscreenDialogTitle": "Full screen", "demoCupertinoButtonsTitle": "Buttons", "demoCupertinoButtonsSubtitle": "iOS-style buttons", "demoCupertinoButtonsDescription": "An iOS-style button. It takes in text and/or an icon that fades out and in on touch. May optionally have a background.", "demoCupertinoAlertsTitle": "<PERSON><PERSON><PERSON>", "demoCupertinoAlertsSubtitle": "iOS-style alert dialogues", "demoCupertinoAlertTitle": "<PERSON><PERSON>", "demoCupertinoAlertDescription": "An alert dialogue informs the user about situations that require acknowledgement. An alert dialogue has an optional title, optional content and an optional list of actions. The title is displayed above the content and the actions are displayed below the content.", "demoCupertinoAlertWithTitleTitle": "<PERSON><PERSON> with title", "demoCupertinoAlertButtonsTitle": "<PERSON><PERSON>", "demoCupertinoAlertButtonsOnlyTitle": "<PERSON><PERSON> Only", "demoCupertinoActionSheetTitle": "Action Sheet", "demoCupertinoActionSheetDescription": "An action sheet is a specific style of alert that presents the user with a set of two or more choices related to the current context. An action sheet can have a title, an additional message and a list of actions.", "demoColorsTitle": "Colours", "demoColorsSubtitle": "All of the predefined colours", "demoColorsDescription": "Colour and colour swatch constants which represent Material Design's colour palette.", "buttonTextEnabled": "ENABLED", "buttonTextDisabled": "DISABLED", "buttonTextCreate": "Create", "dialogSelectedOption": "You selected: '{value}'", "dialogDiscardTitle": "Discard draft?", "dialogLocationTitle": "Use Google's location service?", "dialogLocationDescription": "Let Google help apps determine location. This means sending anonymous location data to Google, even when no apps are running.", "dialogCancel": "CANCEL", "dialogDiscard": "DISCARD", "dialogDisagree": "DISAGREE", "dialogAgree": "AGREE", "dialogSetBackup": "Set backup account", "colorsBlueGrey": "BLUE GREY", "dialogShow": "SHOW DIALOGUE", "dialogFullscreenTitle": "Full-Screen Dialogue", "dialogFullscreenSave": "SAVE", "dialogFullscreenDescription": "A full-screen dialogue demo", "cupertinoButtonEnabled": "Enabled", "cupertinoButtonDisabled": "Disabled", "cupertinoButtonWithBackground": "With background", "cupertinoAlertCancel": "Cancel", "cupertinoAlertDiscard": "Discard", "cupertinoAlertLocationTitle": "Allow 'Maps' to access your location while you are using the app?", "cupertinoAlertLocationDescription": "Your current location will be displayed on the map and used for directions, nearby search results and estimated travel times.", "cupertinoAlertAllow": "Allow", "cupertinoAlertDontAllow": "Don't allow", "cupertinoAlertFavoriteDessert": "Select Favourite Dessert", "cupertinoAlertDessertDescription": "Please select your favourite type of dessert from the list below. Your selection will be used to customise the suggested list of eateries in your area.", "cupertinoAlertCheesecake": "Cheesecake", "cupertinoAlertTiramisu": "Tiramisu", "cupertinoAlertApplePie": "Apple Pie", "cupertinoAlertChocolateBrownie": "Chocolate brownie", "cupertinoShowAlert": "Show alert", "colorsRed": "RED", "colorsPink": "PINK", "colorsPurple": "PURPLE", "colorsDeepPurple": "DEEP PURPLE", "colorsIndigo": "INDIGO", "colorsBlue": "BLUE", "colorsLightBlue": "LIGHT BLUE", "colorsCyan": "CYAN", "dialogAddAccount": "Add account", "Gallery": "Gallery", "Categories": "Categories", "SHRINE": "SHRINE", "Basic shopping app": "Basic shopping app", "RALLY": "RALLY", "CRANE": "CRANE", "Travel app": "Travel app", "MATERIAL": "MATERIAL", "CUPERTINO": "CUPERTINO", "REFERENCE STYLES & MEDIA": "REFERENCE STYLES & MEDIA"}