{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/Code Bharat/ecocura_flutter/build/.cxx/Debug/1z5is355/armeabi-v7a", "source": "D:/Code Bharat/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}