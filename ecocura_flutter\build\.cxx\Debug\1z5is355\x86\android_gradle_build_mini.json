{"buildFiles": ["D:\\Code Bharat\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Code Bharat\\ecocura_flutter\\build\\.cxx\\Debug\\1z5is355\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Code Bharat\\ecocura_flutter\\build\\.cxx\\Debug\\1z5is355\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}