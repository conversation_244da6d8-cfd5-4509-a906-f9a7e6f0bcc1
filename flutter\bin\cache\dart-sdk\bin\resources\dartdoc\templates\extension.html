{{ >head }}
<div
    id="dartdoc-main-content"
    class="main-content extension-page"
    data-above-sidebar="{{ aboveSidebarPath }}"
    data-below-sidebar="{{ belowSidebarPath }}">
  {{ #self }}
    <div>{{ >source_link }}<h1><span class="kind-class">{{{ nameWithGenerics }}}</span> {{ kind }} {{ >feature_set }} {{ >categorization }}</h1></div>
  {{ /self }}

  {{ #extension }}
    {{ >documentation }}
    <section>
      <dl class="dl-horizontal">
        <dt>on</dt>
        <dd>
          <ul class="comma-separated clazz-relationships">
            <li>{{{ extendedElement.linkedName }}}</li>
          </ul>
        </dd>
      </dl>
      {{ >container_annotations }}
    </section>

    {{ >instance_fields }}
    {{ >instance_methods }}
    {{ >instance_operators }}
    {{ >static_properties }}
    {{ >static_methods }}
    {{ >static_constants }}
  {{ #extension }}

</div> <!-- /.main-content -->

<div id="dartdoc-sidebar-left" class="sidebar sidebar-offcanvas-left">
  {{ >search_sidebar }}
  <h5>{{ parent.name }} {{ parent.kind }}</h5>
  <div id="dartdoc-sidebar-left-content"></div>
</div>

<div id="dartdoc-sidebar-right" class="sidebar sidebar-offcanvas-right">
</div><!--/.sidebar-offcanvas-->

{{ >footer }}
