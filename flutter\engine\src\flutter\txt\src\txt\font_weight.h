// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_TXT_SRC_TXT_FONT_WEIGHT_H_
#define FLUTTER_TXT_SRC_TXT_FONT_WEIGHT_H_

namespace txt {

enum class FontWeight {
  // NOLINTBEGIN(readability-identifier-naming)
  w100,  // Thin
  w200,  // Extra-Light
  w300,  // Light
  w400,  // Normal/Regular
  w500,  // Medium
  w600,  // Semi-bold
  w700,  // Bold
  w800,  // Extra-Bold
  w900,  // Black
  // NOLINTEND(readability-identifier-naming)
};

}  // namespace txt

#endif  // FLUTTER_TXT_SRC_TXT_FONT_WEIGHT_H_
