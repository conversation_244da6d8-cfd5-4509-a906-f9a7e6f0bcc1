// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
{
    // TODO(https://fxbug.dev/404543928): Remove this shard usage when possible
    // and replace with explicit routes.
    include: [ "syslog/client.shard.cml" ],
    use: [
        // This is used by the Dart VM to communicate from Dart code to C++ code.
        {
            storage: "tmp",
            path: "/tmp",
        },
        // This is used by the Dart VM to load i18n data.
        {
            directory: "config-data",
            rights: [ "r*" ],
            path: "/config/data",
        },
        // The ICU time zone data, factored out of `config-data`.
        // See:
        // https://fuchsia.dev/fuchsia-src/concepts/process/namespaces?typical_directory_structure
        {
            directory: "tzdata-icu",
            rights: [ "r*" ],
            path: "/config/tzdata/icu",
        },
        {
            protocol: [
                "fuchsia.device.NameProvider",  // For fdio uname()
                "fuchsia.feedback.CrashReporter",
                "fuchsia.intl.PropertyProvider",  // For dartVM timezone support
                "fuchsia.net.name.Lookup",  // For fdio sockets
                "fuchsia.posix.socket.Provider",  // For fdio sockets
            ],
            from: "parent",
        },
        {
            protocol: [
                "fuchsia.tracing.provider.Registry",  // For trace-provider
            ],
            from: "parent",
            availability: "optional",
        },
    ]
}
