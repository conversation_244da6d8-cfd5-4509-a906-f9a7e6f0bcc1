{"version": "6_1_0", "md.comp.navigation-rail.active.focus.icon.color": "onSecondaryContainer", "md.comp.navigation-rail.active.focus.label-text.color": "onSurface", "md.comp.navigation-rail.active.focus.state-layer.color": "onSurface", "md.comp.navigation-rail.active.hover.icon.color": "onSecondaryContainer", "md.comp.navigation-rail.active.hover.label-text.color": "onSurface", "md.comp.navigation-rail.active.hover.state-layer.color": "onSurface", "md.comp.navigation-rail.active.icon.color": "onSecondaryContainer", "md.comp.navigation-rail.active-indicator.color": "secondaryContainer", "md.comp.navigation-rail.active-indicator.height": 32.0, "md.comp.navigation-rail.active-indicator.shape": "md.sys.shape.corner.full", "md.comp.navigation-rail.active-indicator.width": 56.0, "md.comp.navigation-rail.active.label-text.color": "onSurface", "md.comp.navigation-rail.active.pressed.icon.color": "onSecondaryContainer", "md.comp.navigation-rail.active.pressed.label-text.color": "onSurface", "md.comp.navigation-rail.active.pressed.state-layer.color": "onSurface", "md.comp.navigation-rail.container.color": "surface", "md.comp.navigation-rail.container.elevation": "md.sys.elevation.level0", "md.comp.navigation-rail.container.shape": "md.sys.shape.corner.none", "md.comp.navigation-rail.container.width": 80.0, "md.comp.navigation-rail.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.navigation-rail.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.navigation-rail.icon.size": 24.0, "md.comp.navigation-rail.inactive.focus.icon.color": "onSurface", "md.comp.navigation-rail.inactive.focus.label-text.color": "onSurface", "md.comp.navigation-rail.inactive.focus.state-layer.color": "onSurface", "md.comp.navigation-rail.inactive.hover.icon.color": "onSurface", "md.comp.navigation-rail.inactive.hover.label-text.color": "onSurface", "md.comp.navigation-rail.inactive.hover.state-layer.color": "onSurface", "md.comp.navigation-rail.inactive.icon.color": "onSurfaceVariant", "md.comp.navigation-rail.inactive.label-text.color": "onSurfaceVariant", "md.comp.navigation-rail.inactive.pressed.icon.color": "onSurface", "md.comp.navigation-rail.inactive.pressed.label-text.color": "onSurface", "md.comp.navigation-rail.inactive.pressed.state-layer.color": "onSurface", "md.comp.navigation-rail.label-text.text-style": "labelMedium", "md.comp.navigation-rail.no-label.active-indicator.height": 56.0, "md.comp.navigation-rail.no-label.active-indicator.shape": "md.sys.shape.corner.full", "md.comp.navigation-rail.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity"}